/**
 * Vapi Service Manager
 *
 * This service manages the Vapi integration, automatically switching between
 * the real Vapi service and a mock service based on connectivity.
 */

import { vapiMcpService } from './vapiMcpService';

class VapiServiceManager {
  constructor() {
    this.useMock = false; // 💀 NO MORE MOCKS!
    this.apiKey = null;
    this.initialized = false;
    this.mcpService = vapiMcpService;
  }

  /**
   * Initialize the Vapi service manager
   * @param {string} apiKey - Vapi API key
   * @param {boolean} forceMock - Force using mock service
   * @param {Object} options - Additional options
   * @param {boolean} options.isAttorneyDashboard - Whether this is the attorney dashboard
   * @param {boolean} options.isPreview - Whether this is the preview mode
   * @param {boolean} options.isProduction - Whether this is a production environment
   * @param {boolean} options.forceMcpMode - Force using MCP mode instead of direct API
   * @param {boolean} options.forceDirect - Force using direct API mode
   * @returns {Promise<boolean>} - Initialization success
   */
  async initialize(apiKey, forceMock = false, options = {}) {
    const {
      isAttorneyDashboard = false,
      isPreview = false,
      isProduction = false,
      forceMcpMode = false,
      forceDirect = false
    } = options;

    // Check if fast loading mode is enabled
    const fastLoadingMode = typeof window !== 'undefined' && window.FAST_LOADING_MODE;

    console.log('[VapiServiceManager] Initializing with API key:', apiKey ? '****' : 'none',
      `(Attorney Dashboard: ${isAttorneyDashboard}, Preview: ${isPreview}, Production: ${isProduction}, Force MCP: ${forceMcpMode}, Force Direct: ${forceDirect}, Fast Loading: ${fastLoadingMode})`);

    this.apiKey = apiKey;
    this.isAttorneyDashboard = isAttorneyDashboard;
    this.isPreview = isPreview;
    this.isProduction = isProduction;
    this.forceMcpMode = forceMcpMode;

    // In attorney dashboard, preview, or production, we should prioritize real connectivity
    const prioritizeReal = isAttorneyDashboard || isPreview || isProduction;

    // 💀 NO MORE MOCKS! Force mock is ignored
    if (forceMock) {
      console.error('💀 [VapiServiceManager] Mock mode requested but MOCKS ARE DEAD! Using real service only.');
    }

    try {
      // Try to connect to the real Vapi service
      console.log('[VapiServiceManager] Trying to connect to real Vapi service');

      let connected = false;

      // If fast loading mode is enabled or forceDirect is true, use direct API immediately
      if (fastLoadingMode || forceDirect) {
        console.log('[VapiServiceManager] Fast loading mode - using direct API immediately');
        const directConnected = await vapiMcpService.connect(apiKey, true);

        if (directConnected) {
          console.log('[VapiServiceManager] Connected to Vapi using direct API (fast loading)');
          this.useMock = false;
          this.mcpService = vapiMcpService;
          connected = true;
        }
      } else if (this.forceMcpMode) {
        console.log('[VapiServiceManager] Forcing MCP mode, skipping direct API');
        const mcpConnected = await vapiMcpService.connect(apiKey);

        if (mcpConnected) {
          console.log('[VapiServiceManager] Connected to Vapi MCP server');
          this.useMock = false;
          this.mcpService = vapiMcpService;
          connected = true;
        }
      } else {
        // Try MCP connection first
        const mcpConnected = await vapiMcpService.connect(apiKey);

        if (mcpConnected) {
          console.log('[VapiServiceManager] Connected to Vapi MCP server');
          this.useMock = false;
          this.mcpService = vapiMcpService;
          connected = true;
        } else {
          // If MCP connection fails, try direct API
          console.log('[VapiServiceManager] MCP connection failed, trying direct API');
          const directConnected = await vapiMcpService.connect(apiKey, true);

          if (directConnected) {
            console.log('[VapiServiceManager] Connected to Vapi using direct API');
            this.useMock = false;
            this.mcpService = vapiMcpService;
            connected = true;
          }
        }
      }

      // If all connection attempts failed
      if (!connected) {
        console.error('💀 [VapiServiceManager] Failed to connect to Vapi - NO MOCKS ALLOWED!');
        this.connectionError = true;
        this.connectionWarning = "Critical: Unable to connect to voice service. Please check your API key and network connection.";
        throw new Error('Failed to connect to Vapi service - no fallback available');
      }

      this.initialized = true;
      return true;
    } catch (error) {
      console.error('💀 [VapiServiceManager] Error initializing Vapi service - NO MOCKS ALLOWED!', error);
      this.connectionError = true;
      this.connectionWarning = "Critical error connecting to voice service. Please check your configuration.";
      throw error; // Let the error propagate instead of falling back to mocks
    }
  }

  /**
   * Get connection warning if any
   * @returns {string|null} - Warning message or null if no warning
   */
  getConnectionWarning() {
    return this.connectionWarning || null;
  }

  /**
   * Get the MCP service (real only - no mocks!)
   * @returns {Object} - MCP service
   */
  getMcpService() {
    return this.mcpService; // 💀 NO MORE MOCKS!
  }

  /**
   * Create a Vapi instance (real only - no mocks!)
   * @param {string} apiKey - Vapi API key
   * @param {Object} options - Vapi options
   * @returns {Object} - Vapi instance
   */
  createVapiInstance(apiKey, options = {}) {
    console.log('💀 [VapiServiceManager] Creating real Vapi instance - NO MOCKS!');

    // Check if Vapi is available globally
    if (typeof window !== 'undefined' && window.Vapi) {
      return window.Vapi.create(apiKey, options);
    } else if (typeof Vapi !== 'undefined') {
      return Vapi.create(apiKey, options);
    } else {
      console.error('💀 [VapiServiceManager] Vapi not available - NO MOCKS ALLOWED!');
      throw new Error('Vapi SDK not available - please ensure it is loaded');
    }
  }

  /**
   * Check if using mock service
   * @returns {boolean} - Using mock service
   */
  isUsingMock() {
    return this.useMock;
  }

  /**
   * Get connection status (real only - no mocks!)
   * @returns {Object} - Connection status
   */
  getConnectionStatus() {
    return {
      initialized: this.initialized,
      useMock: false, // 💀 NO MORE MOCKS!
      connected: this.mcpService.connected,
      connectionMode: this.mcpService.useDirect ? 'direct' : 'mcp'
    };
  }
}

// Export a singleton instance
export const vapiServiceManager = new VapiServiceManager();

export default vapiServiceManager;
