/**
 * Direct Database Service
 * 
 * This service provides direct database access without relying on the Supabase client
 * to avoid initialization issues in production environments.
 */

import { getSupabaseUrl, getSupabaseKey } from '../lib/supabase-fixed';

class DirectDatabaseService {
  constructor() {
    this.baseUrl = getSupabaseUrl();
    this.apiKey = getSupabaseKey();
  }

  /**
   * Make a direct REST API call to Supabase
   * @param {string} table - Table name
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Query result
   */
  async query(table, options = {}) {
    try {
      const { select = '*', eq, single = false } = options;
      
      let url = `${this.baseUrl}/rest/v1/${table}?select=${select}`;
      
      // Add equality filters
      if (eq) {
        Object.entries(eq).forEach(([key, value]) => {
          url += `&${key}=eq.${encodeURIComponent(value)}`;
        });
      }
      
      console.log(`🔍 [DirectDB] Querying: ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': this.apiKey,
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Prefer': single ? 'return=representation' : 'return=minimal'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      
      console.log(`✅ [DirectDB] Query successful, got ${Array.isArray(data) ? data.length : 1} records`);
      
      if (single) {
        return { data: data[0] || null, error: null };
      }
      
      return { data, error: null };
    } catch (error) {
      console.error(`❌ [DirectDB] Query failed:`, error);
      return { data: null, error };
    }
  }

  /**
   * Get assistant by subdomain using direct database query
   * @param {string} subdomain - The subdomain to lookup
   * @returns {Promise<Object|null>} Assistant mapping or null
   */
  async getAssistantBySubdomain(subdomain) {
    try {
      console.log(`🔍 [DirectDB] Looking up assistant for subdomain: ${subdomain}`);
      
      const result = await this.query('v_subdomain_assistant_lookup', {
        select: '*',
        eq: { 
          subdomain: subdomain,
          is_active: true 
        },
        single: true
      });

      if (result.error) {
        console.error(`❌ [DirectDB] Database error for subdomain ${subdomain}:`, result.error);
        return null;
      }

      if (!result.data) {
        console.log(`📭 [DirectDB] No assistant found for subdomain: ${subdomain}`);
        return null;
      }

      console.log(`✅ [DirectDB] Found assistant for subdomain ${subdomain}:`, {
        assistant_id: result.data.assistant_id,
        attorney_id: result.data.attorney_id,
        firm_name: result.data.firm_name,
        is_primary: result.data.is_primary
      });
      
      return result.data;
    } catch (error) {
      console.error('❌ [DirectDB] Error getting assistant by subdomain:', error);
      return null;
    }
  }

  /**
   * Get attorney by ID using direct database query
   * @param {string} attorneyId - Attorney ID
   * @returns {Promise<Object|null>} Attorney data or null
   */
  async getAttorneyById(attorneyId) {
    try {
      console.log(`🔍 [DirectDB] Looking up attorney: ${attorneyId}`);
      
      const result = await this.query('attorneys', {
        select: '*',
        eq: { id: attorneyId },
        single: true
      });

      if (result.error) {
        console.error(`❌ [DirectDB] Database error for attorney ${attorneyId}:`, result.error);
        return null;
      }

      if (!result.data) {
        console.log(`📭 [DirectDB] No attorney found with ID: ${attorneyId}`);
        return null;
      }

      console.log(`✅ [DirectDB] Found attorney:`, {
        id: result.data.id,
        firm_name: result.data.firm_name,
        email: result.data.email
      });
      
      return result.data;
    } catch (error) {
      console.error('❌ [DirectDB] Error getting attorney by ID:', error);
      return null;
    }
  }

  /**
   * Get attorney configuration for subdomain using direct queries
   * @param {string} subdomain - The subdomain to lookup
   * @returns {Promise<Object|null>} Attorney config or null
   */
  async getAttorneyConfigForSubdomain(subdomain) {
    try {
      console.log(`🎯 [DirectDB] Getting attorney config for subdomain: ${subdomain}`);
      
      // First, get the assistant mapping
      const assistantMapping = await this.getAssistantBySubdomain(subdomain);
      if (!assistantMapping) {
        return null;
      }

      // Then, get the attorney data
      const attorney = await this.getAttorneyById(assistantMapping.attorney_id);
      if (!attorney) {
        return null;
      }

      // Combine the data into attorney config format
      const attorneyConfig = {
        ...attorney,
        
        // Assistant mapping specific fields
        current_assistant_id: assistantMapping.assistant_id,
        assistant_subdomain: assistantMapping.subdomain,
        is_primary_assistant: assistantMapping.is_primary,
        
        // Ensure proper field mapping
        firmName: attorney.firm_name || attorney.name || "LegalScout",
        vapi_assistant_id: assistantMapping.assistant_id, // Use the specific assistant for this subdomain
        
        // Status tracking
        vapiSyncStatus: 'assistant_subdomain_mapped',
        loadedVia: 'direct_database_query'
      };

      console.log(`🎯 [DirectDB] Returning attorney config:`, {
        firmName: attorneyConfig.firmName,
        vapi_assistant_id: attorneyConfig.vapi_assistant_id,
        subdomain: attorneyConfig.assistant_subdomain
      });

      return attorneyConfig;
    } catch (error) {
      console.error('❌ [DirectDB] Error getting attorney config for subdomain:', error);
      return null;
    }
  }
}

// Export singleton instance
export const directDatabaseService = new DirectDatabaseService();
export default directDatabaseService;
