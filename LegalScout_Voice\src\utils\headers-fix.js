/**
 * Headers Fix
 * 
 * This utility adds proper headers to all fetch requests to ensure they work correctly.
 */

// Store the original fetch function
const originalFetch = window.fetch;

// Override the fetch function to add proper headers
window.fetch = function(url, options = {}) {
  // Create a new options object with the original options
  const newOptions = { ...options };
  
  // Ensure headers object exists
  newOptions.headers = newOptions.headers || {};
  
  // Convert headers to a regular object if it's a Headers instance
  if (newOptions.headers instanceof Headers) {
    const headersObj = {};
    for (const [key, value] of newOptions.headers.entries()) {
      headersObj[key] = value;
    }
    newOptions.headers = headersObj;
  }
  
  // Add Accept header if not present
  if (!newOptions.headers['Accept'] && !newOptions.headers['accept']) {
    newOptions.headers['Accept'] = 'application/json';
  }
  
  // Log the request for debugging
  console.log('[HeadersFix] Fetch request to', url, 'with headers:', newOptions.headers);
  
  // Call the original fetch with the new options
  return originalFetch(url, newOptions);
};

console.log('Headers fix applied to fetch');
