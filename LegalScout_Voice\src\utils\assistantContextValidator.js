/**
 * Assistant Context Validator
 * 
 * Prevents attorney IDs from being used as assistant IDs and provides
 * clean assistant context resolution with validation.
 */

export class AssistantContextValidator {
  // Cache of known attorney IDs to prevent them being used as assistant IDs
  static attorneyIds = new Set();
  
  /**
   * Initialize with known attorney IDs from the system
   */
  static initialize(attorneyIds = []) {
    this.attorneyIds = new Set(attorneyIds);
  }
  
  /**
   * Add an attorney ID to the validation cache
   */
  static addAttorneyId(attorneyId) {
    if (attorneyId) {
      this.attorneyIds.add(attorneyId);
    }
  }
  
  /**
   * Validate that an ID is a proper assistant ID, not an attorney ID
   */
  static validateAssistantId(assistantId, attorneyId = null) {
    if (!assistantId) {
      return { valid: false, reason: 'missing', assistantId };
    }
    
    if (typeof assistantId !== 'string') {
      return { valid: false, reason: 'invalid_type', assistantId };
    }
    
    // Check if this is an attorney ID
    if (attorneyId && assistantId === attorneyId) {
      return { valid: false, reason: 'attorney_id_used', assistantId };
    }
    
    // Check against known attorney IDs
    if (this.attorneyIds.has(assistantId)) {
      return { valid: false, reason: 'known_attorney_id', assistantId };
    }
    
    // Check for mock/test IDs
    if (assistantId.includes('mock') || assistantId.includes('test') || assistantId.includes('undefined')) {
      return { valid: false, reason: 'mock_id', assistantId };
    }
    
    // Check for null/undefined strings
    if (assistantId === 'null' || assistantId === 'undefined') {
      return { valid: false, reason: 'null_string', assistantId };
    }
    
    // Basic length check (UUIDs should be reasonable length)
    if (assistantId.length < 10) {
      return { valid: false, reason: 'too_short', assistantId };
    }
    
    return { valid: true, assistantId };
  }
  
  /**
   * Resolve assistant context from attorney data with validation
   */
  static resolveAssistantContext(attorney, forceAssistantId = null) {
    console.log('🔍 [AssistantContextValidator] Resolving context:', {
      forceAssistantId,
      attorney_current: attorney?.current_assistant_id,
      attorney_vapi: attorney?.vapi_assistant_id,
      attorney_id: attorney?.id
    });
    
    // Add attorney ID to validation cache
    if (attorney?.id) {
      this.addAttorneyId(attorney.id);
    }
    
    // Try candidates in priority order
    const candidates = [
      forceAssistantId,
      attorney?.current_assistant_id,
      attorney?.vapi_assistant_id
    ].filter(Boolean);
    
    for (const candidate of candidates) {
      const validation = this.validateAssistantId(candidate, attorney?.id);
      
      console.log('🔍 [AssistantContextValidator] Validating candidate:', {
        candidate,
        validation
      });
      
      if (validation.valid) {
        const context = {
          assistantId: candidate,
          attorneyId: attorney?.id,
          subdomain: attorney?.subdomain,
          isValid: true,
          source: candidate === forceAssistantId ? 'forced' : 
                  candidate === attorney?.current_assistant_id ? 'current' : 'vapi'
        };
        
        console.log('✅ [AssistantContextValidator] Resolved valid context:', context);
        return context;
      } else {
        console.warn('❌ [AssistantContextValidator] Rejected candidate:', validation);
      }
    }
    
    // No valid assistant ID found
    const context = {
      assistantId: null,
      attorneyId: attorney?.id,
      subdomain: attorney?.subdomain,
      isValid: false,
      reason: 'no_valid_assistant_id'
    };
    
    console.warn('⚠️ [AssistantContextValidator] No valid assistant context found:', context);
    return context;
  }
  
  /**
   * Validate assistant context before using in services
   */
  static validateContext(context) {
    if (!context) {
      return { valid: false, reason: 'missing_context' };
    }
    
    if (!context.isValid) {
      return { valid: false, reason: 'context_marked_invalid' };
    }
    
    const assistantValidation = this.validateAssistantId(context.assistantId, context.attorneyId);
    if (!assistantValidation.valid) {
      return { valid: false, reason: `assistant_id_${assistantValidation.reason}` };
    }
    
    return { valid: true };
  }
  
  /**
   * Create error message for invalid context
   */
  static getErrorMessage(validation) {
    const messages = {
      missing: 'No assistant ID provided',
      invalid_type: 'Assistant ID must be a string',
      attorney_id_used: 'Attorney ID cannot be used as assistant ID',
      known_attorney_id: 'This ID belongs to an attorney, not an assistant',
      mock_id: 'Mock/test IDs are not valid',
      null_string: 'Null or undefined string is not valid',
      too_short: 'Assistant ID is too short',
      missing_context: 'Assistant context is missing',
      context_marked_invalid: 'Assistant context is marked as invalid',
      no_valid_assistant_id: 'No valid assistant ID found in attorney data'
    };
    
    return messages[validation.reason] || `Invalid assistant context: ${validation.reason}`;
  }
}

// Initialize with common attorney ID patterns if needed
export default AssistantContextValidator;
