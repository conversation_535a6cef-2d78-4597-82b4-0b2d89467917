export const isPublicKeyMissingError = ({ vapiError }) => {
  return vapiError && vapiError.message && vapiError.message.includes('public key')
}

export * from './debugConfig'

if (import.meta.env.DEV && typeof window !== 'undefined') {
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout = {
      version: '0.1.0',
      components: {},
      journeySteps: [],
      webhookCalls: [],
      vapiMessages: [],
      errors: []
    };
    
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.trackJourneyStep = (step, data) => {
      const journeyStep = {
        step,
        timestamp: new Date().toISOString(),
        data
      };
      
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout.journeySteps.push(journeyStep);
      console.log('User Journey Step:', journeyStep);
      return journeyStep;
    };
    
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.logWebhookCall = (toolId, url, data) => {
      const webhookCall = {
        toolId,
        url,
        data,
        timestamp: new Date().toISOString()
      };
      
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout.webhookCalls.push(webhookCall);
      console.log('Webhook Call:', webhookCall);
      return webhookCall;
    };
    
    // Add a specific function to monitor Vapi message transcriptions
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.logVapiMessage = (message, source = 'unknown') => {
      const vapiMessage = {
        role: message.role,
        content: message.content,
        source, // 'api', 'user_typed', 'user_speech', 'assistant'
        isTranscription: message.isTranscription || false,
        timestamp: new Date().toISOString(),
        id: message.id || `msg-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`
      };
      
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout.vapiMessages.push(vapiMessage);
      
      if (window.LegalScoutDebug?.logLevel === 'verbose') {
        console.group(`Vapi Message [${source}] - ${message.role}`);
        console.log('Content:', message.content);
        console.log('Details:', vapiMessage);
        console.groupEnd();
      } else {
        console.log(`Vapi Message [${source}]: ${message.role} - ${message.content.slice(0, 50)}${message.content.length > 50 ? '...' : ''}`);
      }
      
      return vapiMessage;
    };
    
    // Add a helper method to get all Vapi messages
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.getVapiMessages = () => {
      return window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout.vapiMessages;
    };
    
    // Add a helper method to clear Vapi message logs
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.clearVapiMessages = () => {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout.vapiMessages = [];
      console.log('Vapi message logs cleared');
    };
    
    console.log('🛠️ React DevTools enhanced for LegalScout');
  }
}
