/**
 * Vapi Debugger
 *
 * A comprehensive debugging utility for Vapi integration.
 * This module provides tools for:
 * - Structured logging with different log levels
 * - Network request/response logging
 * - Connection status tracking
 * - Environment variable verification
 * - Error aggregation and analysis
 */

// Log levels
export const LOG_LEVELS = {
  NONE: 0,
  ERROR: 1,
  WARN: 2,
  INFO: 3,
  DEBUG: 4,
  TRACE: 5
};

/**
 * Sanitize data for logging by truncating base64 image strings and large objects
 * @param {any} data - Data to sanitize
 * @returns {any} - Sanitized data
 */
const sanitizeLogData = (data) => {
  if (typeof data === 'string') {
    // Check for base64 image data
    if (data.startsWith('data:image/') && data.includes('base64,')) {
      const [prefix, base64Data] = data.split('base64,');
      if (base64Data && base64Data.length > 50) {
        return `${prefix}base64,[${base64Data.length} chars]`;
      }
    }
    // Check for long base64 strings without data: prefix
    if (data.match(/^[A-Za-z0-9+/]{100,}={0,2}$/)) {
      return `[base64 string: ${data.length} chars]`;
    }
    // Truncate very long strings
    if (data.length > 1000) {
      return `[${data.length} chars]${data.slice(0, 200)}...`;
    }
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(sanitizeLogData);
  }

  if (data && typeof data === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeLogData(value);
    }
    return sanitized;
  }

  return data;
};

// Default configuration
const DEFAULT_CONFIG = {
  enabled: true,
  logLevel: LOG_LEVELS.INFO,
  logToConsole: true,
  logToMemory: true,
  maxLogEntries: 1000,
  includeTimestamp: true,
  networkLogging: {
    enabled: true,
    includeHeaders: true,
    includeBody: true,
    maxBodyLength: 500 // Reduced from 1000 to prevent large logs
  },
  components: {
    vapiMcpService: true,
    vapiAssistantService: true,
    vapiEmissionsService: true,
    vapiServiceManager: true,
    vapiCall: true
  }
};

// In-memory log storage
const logs = [];
const networkLogs = [];
const connectionEvents = [];
const errors = [];

// Current configuration
let config = { ...DEFAULT_CONFIG };

/**
 * Configure the Vapi debugger
 * @param {Object} newConfig - Configuration options
 */
export const configureVapiDebugger = (newConfig = {}) => {
  config = { ...DEFAULT_CONFIG, ...newConfig };

  // Make configuration available globally for debugging
  if (typeof window !== 'undefined') {
    window.VapiDebugger = {
      config,
      logs,
      networkLogs,
      connectionEvents,
      errors,
      getLogs: () => [...logs],
      getNetworkLogs: () => [...networkLogs],
      getConnectionEvents: () => [...connectionEvents],
      getErrors: () => [...errors],
      clearLogs: () => {
        logs.length = 0;
        console.log('Vapi logs cleared');
      },
      clearNetworkLogs: () => {
        networkLogs.length = 0;
        console.log('Vapi network logs cleared');
      },
      clearConnectionEvents: () => {
        connectionEvents.length = 0;
        console.log('Vapi connection events cleared');
      },
      clearErrors: () => {
        errors.length = 0;
        console.log('Vapi errors cleared');
      },
      clearAll: () => {
        logs.length = 0;
        networkLogs.length = 0;
        connectionEvents.length = 0;
        errors.length = 0;
        console.log('All Vapi logs cleared');
      }
    };
  }
};

/**
 * Create a logger for a specific component
 * @param {string} component - Component name
 * @returns {Object} Logger object
 */
export const createVapiLogger = (component) => {
  // Check if component logging is enabled
  const isComponentEnabled = () => {
    if (!config.enabled) return false;
    if (!config.components) return true;
    return config.components[component] !== false;
  };

  // Check if log level is enabled
  const isLevelEnabled = (level) => {
    return config.logLevel >= level;
  };

  // Format log entry
  const formatLogEntry = (level, message, data) => {
    const timestamp = config.includeTimestamp ? new Date().toISOString() : null;

    return {
      timestamp,
      component,
      level,
      message,
      data: data ? sanitizeLogData(data) : undefined
    };
  };

  // Log to console
  const logToConsole = (level, formattedMessage, entry) => {
    if (!config.logToConsole) return;

    switch (level) {
      case LOG_LEVELS.ERROR:
        console.error(formattedMessage, entry.data);
        break;
      case LOG_LEVELS.WARN:
        console.warn(formattedMessage, entry.data);
        break;
      case LOG_LEVELS.INFO:
        console.info(formattedMessage, entry.data);
        break;
      case LOG_LEVELS.DEBUG:
        console.debug(formattedMessage, entry.data);
        break;
      case LOG_LEVELS.TRACE:
        console.log(formattedMessage, entry.data);
        break;
    }
  };

  // Log to memory
  const logToMemory = (entry) => {
    if (!config.logToMemory) return;

    logs.push(entry);

    // Trim logs if they exceed max entries
    if (logs.length > config.maxLogEntries) {
      logs.splice(0, logs.length - config.maxLogEntries);
    }

    // Add to errors if it's an error
    if (entry.level === LOG_LEVELS.ERROR) {
      errors.push(entry);
    }
  };

  // Create logger methods
  return {
    error: (message, data) => {
      if (!isComponentEnabled() || !isLevelEnabled(LOG_LEVELS.ERROR)) return;

      const entry = formatLogEntry(LOG_LEVELS.ERROR, message, data);
      const formattedMessage = `[ERROR][${component}] ${message}`;

      logToConsole(LOG_LEVELS.ERROR, formattedMessage, entry);
      logToMemory(entry);
    },

    warn: (message, data) => {
      if (!isComponentEnabled() || !isLevelEnabled(LOG_LEVELS.WARN)) return;

      const entry = formatLogEntry(LOG_LEVELS.WARN, message, data);
      const formattedMessage = `[WARN][${component}] ${message}`;

      logToConsole(LOG_LEVELS.WARN, formattedMessage, entry);
      logToMemory(entry);
    },

    info: (message, data) => {
      if (!isComponentEnabled() || !isLevelEnabled(LOG_LEVELS.INFO)) return;

      const entry = formatLogEntry(LOG_LEVELS.INFO, message, data);
      const formattedMessage = `[INFO][${component}] ${message}`;

      logToConsole(LOG_LEVELS.INFO, formattedMessage, entry);
      logToMemory(entry);
    },

    debug: (message, data) => {
      if (!isComponentEnabled() || !isLevelEnabled(LOG_LEVELS.DEBUG)) return;

      const entry = formatLogEntry(LOG_LEVELS.DEBUG, message, data);
      const formattedMessage = `[DEBUG][${component}] ${message}`;

      logToConsole(LOG_LEVELS.DEBUG, formattedMessage, entry);
      logToMemory(entry);
    },

    trace: (message, data) => {
      if (!isComponentEnabled() || !isLevelEnabled(LOG_LEVELS.TRACE)) return;

      const entry = formatLogEntry(LOG_LEVELS.TRACE, message, data);
      const formattedMessage = `[TRACE][${component}] ${message}`;

      logToConsole(LOG_LEVELS.TRACE, formattedMessage, entry);
      logToMemory(entry);
    },

    // Log network request
    logRequest: (url, method, headers, body) => {
      if (!isComponentEnabled() || !config.networkLogging.enabled) return;

      const entry = {
        timestamp: config.includeTimestamp ? new Date().toISOString() : null,
        component,
        type: 'request',
        url,
        method,
        headers: config.networkLogging.includeHeaders ? sanitizeLogData(headers) : undefined,
        body: config.networkLogging.includeBody ? (
          typeof body === 'string'
            ? (body.length > config.networkLogging.maxBodyLength
                ? body.substring(0, config.networkLogging.maxBodyLength) + '...'
                : body)
            : sanitizeLogData(body)
        ) : undefined
      };

      networkLogs.push(entry);

      if (config.logToConsole) {
        console.log(`[NETWORK][${component}] Request: ${method} ${url}`, entry);
      }
    },

    // Log network response
    logResponse: (url, status, headers, body) => {
      if (!isComponentEnabled() || !config.networkLogging.enabled) return;

      const entry = {
        timestamp: config.includeTimestamp ? new Date().toISOString() : null,
        component,
        type: 'response',
        url,
        status,
        headers: config.networkLogging.includeHeaders ? sanitizeLogData(headers) : undefined,
        body: config.networkLogging.includeBody ? (
          typeof body === 'string'
            ? (body.length > config.networkLogging.maxBodyLength
                ? body.substring(0, config.networkLogging.maxBodyLength) + '...'
                : body)
            : sanitizeLogData(body)
        ) : undefined
      };

      networkLogs.push(entry);

      if (config.logToConsole) {
        console.log(`[NETWORK][${component}] Response: ${status} ${url}`, entry);
      }
    },

    // Log connection event
    logConnection: (status, details) => {
      if (!isComponentEnabled()) return;

      const entry = {
        timestamp: config.includeTimestamp ? new Date().toISOString() : null,
        component,
        status,
        details
      };

      connectionEvents.push(entry);

      if (config.logToConsole) {
        console.log(`[CONNECTION][${component}] ${status}`, details);
      }
    }
  };
};

// Initialize the debugger
configureVapiDebugger();

// Export default instance
export default {
  configureVapiDebugger,
  createVapiLogger,
  LOG_LEVELS
};
