/**
 * Utility for toggling between real and mock authentication in development mode
 */

/**
 * Enable real authentication in development mode
 * This will bypass the mock authentication and use the real OAuth flow
 */
export const enableRealAuth = () => {
  localStorage.setItem('use_real_auth', 'true');
  console.log('Real authentication enabled. Refresh the page to apply changes.');
};

/**
 * Disable real authentication in development mode
 * This will use mock authentication instead of the real OAuth flow
 */
export const disableRealAuth = () => {
  localStorage.removeItem('use_real_auth');
  console.log('Mock authentication enabled. Refresh the page to apply changes.');
};

/**
 * Check if real authentication is enabled in development mode
 * @returns {boolean} Whether real authentication is enabled
 */
export const isRealAuthEnabled = () => {
  return localStorage.getItem('use_real_auth') === 'true';
};

/**
 * Toggle between real and mock authentication in development mode
 * @returns {boolean} The new state of real authentication (true = real auth, false = mock auth)
 */
export const toggleAuthMode = () => {
  const currentMode = isRealAuthEnabled();
  if (currentMode) {
    disableRealAuth();
  } else {
    enableRealAuth();
  }
  return !currentMode;
};

export default {
  enableRealAuth,
  disableRealAuth,
  isRealAuthEnabled,
  toggleAuthMode
};
