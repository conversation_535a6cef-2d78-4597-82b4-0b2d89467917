/**
 * Unified Authentication Service
 * 
 * This service consolidates all authentication methods and provides a single,
 * reliable interface for authentication across development and production environments.
 */

import { signInWithGoogle, getSession, getCurrentUser, signOut } from '../lib/supabase-fixed';

class UnifiedAuthService {
  constructor() {
    this.authState = {
      user: null,
      session: null,
      loading: false,
      error: null
    };
    this.listeners = new Set();
  }

  /**
   * Initialize authentication service
   */
  async initialize() {
    console.log('🔐 [UnifiedAuth] Initializing authentication service...');
    
    try {
      // Check for existing session
      const session = await getSession();
      const user = await getCurrentUser();
      
      this.authState = {
        user,
        session,
        loading: false,
        error: null
      };
      
      console.log('✅ [UnifiedAuth] Initialized with user:', user?.email || 'No user');
      this.notifyListeners();
      
      return this.authState;
    } catch (error) {
      console.error('❌ [UnifiedAuth] Initialization error:', error);
      this.authState = {
        user: null,
        session: null,
        loading: false,
        error: error.message
      };
      this.notifyListeners();
      return this.authState;
    }
  }

  /**
   * Sign in with Google OAuth
   */
  async signInWithGoogle() {
    console.log('🔐 [UnifiedAuth] Starting Google sign-in...');
    
    this.authState.loading = true;
    this.authState.error = null;
    this.notifyListeners();
    
    try {
      const result = await signInWithGoogle();
      
      if (result.error) {
        throw new Error(result.error.message || 'Authentication failed');
      }
      
      console.log('✅ [UnifiedAuth] Google sign-in initiated successfully');
      
      // Note: The actual user/session will be set after redirect callback
      return { success: true, data: result.data };
    } catch (error) {
      console.error('❌ [UnifiedAuth] Google sign-in error:', error);
      
      this.authState.loading = false;
      this.authState.error = error.message;
      this.notifyListeners();
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle OAuth callback (called after redirect)
   */
  async handleOAuthCallback() {
    console.log('🔐 [UnifiedAuth] Handling OAuth callback...');
    
    this.authState.loading = true;
    this.authState.error = null;
    this.notifyListeners();
    
    try {
      // Wait a moment for Supabase to process the callback
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Get the updated session and user
      const session = await getSession();
      const user = await getCurrentUser();
      
      if (!user) {
        throw new Error('No user found after OAuth callback');
      }
      
      this.authState = {
        user,
        session,
        loading: false,
        error: null
      };
      
      console.log('✅ [UnifiedAuth] OAuth callback handled successfully for:', user.email);
      this.notifyListeners();
      
      return { success: true, user, session };
    } catch (error) {
      console.error('❌ [UnifiedAuth] OAuth callback error:', error);
      
      this.authState = {
        user: null,
        session: null,
        loading: false,
        error: error.message
      };
      this.notifyListeners();
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Sign out user
   */
  async signOut() {
    console.log('🔐 [UnifiedAuth] Signing out...');
    
    this.authState.loading = true;
    this.notifyListeners();
    
    try {
      const result = await signOut();
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Sign out failed');
      }
      
      this.authState = {
        user: null,
        session: null,
        loading: false,
        error: null
      };
      
      console.log('✅ [UnifiedAuth] Successfully signed out');
      this.notifyListeners();
      
      return { success: true };
    } catch (error) {
      console.error('❌ [UnifiedAuth] Sign out error:', error);
      
      this.authState.loading = false;
      this.authState.error = error.message;
      this.notifyListeners();
      
      return { success: false, error: error.message };
    }
  }

  /**
   * Get current authentication state
   */
  getAuthState() {
    return { ...this.authState };
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!this.authState.user;
  }

  /**
   * Subscribe to authentication state changes
   */
  subscribe(listener) {
    this.listeners.add(listener);
    
    // Immediately call with current state
    listener(this.authState);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Notify all listeners of state changes
   */
  notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.authState);
      } catch (error) {
        console.error('❌ [UnifiedAuth] Listener error:', error);
      }
    });
  }

  /**
   * Refresh authentication state
   */
  async refresh() {
    console.log('🔄 [UnifiedAuth] Refreshing authentication state...');
    return await this.initialize();
  }

  /**
   * Get environment-specific redirect URLs for configuration
   */
  getRedirectUrls() {
    const isDevelopment = window.location.hostname === 'localhost';
    const baseUrl = window.location.origin;
    
    return {
      development: `http://localhost:5173/auth/callback`,
      production: `${baseUrl}/auth/callback`,
      current: `${baseUrl}/auth/callback`,
      isDevelopment
    };
  }
}

// Export singleton instance
export const unifiedAuthService = new UnifiedAuthService();

// Export class for testing
export default UnifiedAuthService;

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.unifiedAuthService = unifiedAuthService;
  console.log('🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService');
}
