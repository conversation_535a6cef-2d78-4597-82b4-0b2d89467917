/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as Vapi from "../../../../index";
export interface LogsGetRequest {
    /**
     * This is the type of the log.
     */
    type?: Vapi.LogsGetRequestType;
    /**
     * This is the type of the webhook, given the log is from a webhook.
     */
    webhookType?: string;
    /**
     * This is the ID of the assistant.
     */
    assistantId?: string;
    /**
     * This is the ID of the phone number.
     */
    phoneNumberId?: string;
    /**
     * This is the ID of the customer.
     */
    customerId?: string;
    /**
     * This is the ID of the squad.
     */
    squadId?: string;
    /**
     * This is the ID of the call.
     */
    callId?: string;
    /**
     * This is the page number to return. Defaults to 1.
     */
    page?: number;
    /**
     * This is the sort order for pagination. Defaults to 'DESC'.
     */
    sortOrder?: Vapi.LogsGetRequestSortOrder;
    /**
     * This is the maximum number of items to return. Defaults to 100.
     */
    limit?: number;
    /**
     * This will return items where the createdAt is greater than the specified value.
     */
    createdAtGt?: string;
    /**
     * This will return items where the createdAt is less than the specified value.
     */
    createdAtLt?: string;
    /**
     * This will return items where the createdAt is greater than or equal to the specified value.
     */
    createdAtGe?: string;
    /**
     * This will return items where the createdAt is less than or equal to the specified value.
     */
    createdAtLe?: string;
    /**
     * This will return items where the updatedAt is greater than the specified value.
     */
    updatedAtGt?: string;
    /**
     * This will return items where the updatedAt is less than the specified value.
     */
    updatedAtLt?: string;
    /**
     * This will return items where the updatedAt is greater than or equal to the specified value.
     */
    updatedAtGe?: string;
    /**
     * This will return items where the updatedAt is less than or equal to the specified value.
     */
    updatedAtLe?: string;
}
