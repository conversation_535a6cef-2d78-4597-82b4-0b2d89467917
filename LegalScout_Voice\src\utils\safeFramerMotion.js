/**
 * Safe Framer Motion
 * 
 * This module attempts to import Framer Motion, but falls back to a simple implementation
 * if Framer Motion fails to load or throws errors.
 */

import React from 'react';
import { motion as realMotion, AnimatePresence as realAnimatePresence } from 'framer-motion';
import { motion as fallbackMotion, AnimatePresence as fallbackAnimatePresence } from '../components/FramerMotionFallback';

let motion;
let AnimatePresence;

// Try to use the real Framer Motion
try {
  // Test if Framer Motion is working properly
  const testContext = realMotion.div;
  
  if (testContext) {
    console.log('[SafeFramerMotion] Using real Framer Motion');
    motion = realMotion;
    AnimatePresence = realAnimatePresence;
  } else {
    throw new Error('Framer Motion test failed');
  }
} catch (error) {
  // If there's any error, use the fallback implementation
  console.warn('[SafeFramerMotion] Framer Motion failed to load, using fallback implementation');
  console.error(error);
  motion = fallbackMotion;
  AnimatePresence = fallbackAnimatePresence;
}

export { motion, AnimatePresence };
