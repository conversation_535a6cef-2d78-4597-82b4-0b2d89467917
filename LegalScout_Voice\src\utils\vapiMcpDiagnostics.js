/**
 * Vapi MCP Diagnostics
 *
 * This module provides diagnostic tools for the Vapi MCP server integration.
 * It can be used to diagnose connection issues, test API endpoints, and verify configuration.
 */

import { logConnection, checkVapiEnvironment } from './vapiMcpDebugger';
import { mcpConfig } from '../config/mcp.config';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

/**
 * Test MCP connection with different proxy paths
 * @returns {Promise<Object>} Test results
 */
export const testMcpProxyPaths = async () => {
  const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||
                (typeof localStorage !== 'undefined' && localStorage.getItem('vapi_api_key'));

  if (!apiKey) {
    logConnection('error', 'No API key available for MCP proxy test');
    return { success: false, error: 'No API key available' };
  }

  // Get proxy paths to test
  const proxyPaths = [
    '/vapi-mcp-server/sse',
    '/vapi-mcp/sse',
    '/api/vapi-mcp-server/sse',
    '/api/vapi-mcp-server'
  ];

  // Add paths from config if available
  if (mcpConfig?.voice?.vapi?.mcpProxyPath) {
    if (!proxyPaths.includes(mcpConfig.voice.vapi.mcpProxyPath)) {
      proxyPaths.unshift(mcpConfig.voice.vapi.mcpProxyPath);
    }
  }

  if (mcpConfig?.voice?.vapi?.fallbackProxyPaths) {
    for (const path of mcpConfig.voice.vapi.fallbackProxyPaths) {
      if (!proxyPaths.includes(path)) {
        proxyPaths.push(path);
      }
    }
  }

  logConnection('info', 'Testing MCP proxy paths', { proxyPaths });

  const results = [];

  // Test each proxy path
  for (const proxyPath of proxyPaths) {
    try {
      logConnection('info', `Testing proxy path: ${proxyPath}`);

      // Create MCP client
      const client = new Client({
        name: 'legalscout-diagnostic-client',
        version: '1.0.0',
      });

      // Create URL
      const sseUrl = new URL(proxyPath, window.location.origin);

      // Create transport
      const transport = new SSEClientTransport({
        url: sseUrl.toString(),
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      // Set timeout
      const connectPromise = client.connect(transport);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Connection timeout')), 10000);
      });

      // Try to connect
      await Promise.race([connectPromise, timeoutPromise]);

      // If we get here, connection was successful
      logConnection('success', `MCP connection successful with proxy path: ${proxyPath}`);

      // Try to list assistants
      try {
        const response = await client.callTool({
          name: 'list_assistants_vapi-mcp-server',
          arguments: {}
        });

        logConnection('success', `Successfully listed assistants with proxy path: ${proxyPath}`, { count: response.content.length });

        results.push({
          proxyPath,
          success: true,
          assistantsCount: response.content.length
        });
      } catch (toolError) {
        logConnection('warning', `Connected but failed to list assistants with proxy path: ${proxyPath}`, { error: toolError.message });

        results.push({
          proxyPath,
          success: true,
          toolError: toolError.message
        });
      }

      // Disconnect
      await client.close();
    } catch (error) {
      logConnection('warning', `Failed to connect with proxy path: ${proxyPath}`, { error: error.message });

      results.push({
        proxyPath,
        success: false,
        error: error.message
      });
    }
  }

  // Check if any proxy path was successful
  const anySuccess = results.some(result => result.success);

  if (anySuccess) {
    logConnection('success', 'At least one proxy path was successful', { results });
    return { success: true, results };
  } else {
    logConnection('error', 'All proxy paths failed', { results });
    return { success: false, results };
  }
};

/**
 * Test direct connection to Vapi API
 * @returns {Promise<Object>} Test results
 */
export const testDirectApiConnection = async () => {
  const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||
                (typeof localStorage !== 'undefined' && localStorage.getItem('vapi_api_key'));

  if (!apiKey) {
    logConnection('error', 'No API key available for direct API test');
    return { success: false, error: 'No API key available' };
  }

  // Endpoints to test - updated with current Vapi API endpoints from docs
  const endpoints = [
    'https://api.vapi.ai/call',
    'https://api.vapi.ai/assistant',
    'https://api.vapi.ai/phone-number',
    'https://api.vapi.ai/tool'
  ];

  logConnection('info', 'Testing direct API endpoints', { endpoints });

  const results = [];

  // Test each endpoint
  for (const endpoint of endpoints) {
    try {
      logConnection('info', `Testing endpoint: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        logConnection('success', `Successfully connected to endpoint: ${endpoint}`, { count: data.length });

        results.push({
          endpoint,
          success: true,
          status: response.status,
          count: data.length
        });
      } else {
        logConnection('warning', `Endpoint ${endpoint} returned status: ${response.status}`);

        results.push({
          endpoint,
          success: false,
          status: response.status
        });
      }
    } catch (error) {
      logConnection('warning', `Error with endpoint ${endpoint}`, { error: error.message });

      results.push({
        endpoint,
        success: false,
        error: error.message
      });
    }
  }

  // Check if any endpoint was successful
  const anySuccess = results.some(result => result.success);

  if (anySuccess) {
    logConnection('success', 'At least one direct API endpoint was successful', { results });
    return { success: true, results };
  } else {
    logConnection('error', 'All direct API endpoints failed', { results });
    return { success: false, results };
  }
};

/**
 * Run all diagnostic tests
 * @returns {Promise<Object>} Test results
 */
export const runAllDiagnostics = async () => {
  logConnection('info', 'Running all Vapi MCP diagnostics');

  // Check environment
  const environment = checkVapiEnvironment();

  // Test MCP proxy paths
  const mcpResults = await testMcpProxyPaths();

  // Test direct API connection
  const directResults = await testDirectApiConnection();

  // Compile results
  const results = {
    environment,
    mcpConnection: mcpResults,
    directConnection: directResults,
    timestamp: new Date().toISOString(),
    overallSuccess: mcpResults.success || directResults.success
  };

  // Log overall result
  if (results.overallSuccess) {
    logConnection('success', 'Diagnostics completed successfully - at least one connection method works');
  } else {
    logConnection('error', 'Diagnostics failed - no connection method works');
  }

  return results;
};

// Export default
export default {
  testMcpProxyPaths,
  testDirectApiConnection,
  runAllDiagnostics
};
