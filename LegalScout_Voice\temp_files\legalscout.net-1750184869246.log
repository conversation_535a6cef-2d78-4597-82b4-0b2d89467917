callback:18 🚀 [LegalScout] Initializing environment...
callback:40 ✅ [LegalScout] Environment initialized
callback:135 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
callback:228 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-5f0a6388.js:52 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
index-5f0a6388.js:52 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
index-5f0a6388.js:86 [VapiLoader] Starting Vapi SDK loading process
index-5f0a6388.js:86 [VapiLoader] Attempting to import @vapi-ai/web package
index-5f0a6388.js:173 [VapiMcpService] Created clean fetch from iframe
index-5f0a6388.js:173 [VapiMcpService] INFO: Vapi MCP Service initialized Object
index-5f0a6388.js:52 ⚠️ [Supabase-Fixed] Client not initialized, returning stub for: channel
overrideMethod @ hook.js:608
get @ index-5f0a6388.js:52
initializeRealtimeSubscriptions @ index-5f0a6388.js:435
index-5f0a6388.js:435 ❌ [AssistantSyncManager] Error initializing real-time subscriptions: Error: Supabase client not initialized. Call getSupabaseClient() first.
    at Proxy.<anonymous> (index-5f0a6388.js:52:11549)
    at m1e.initializeRealtimeSubscriptions (index-5f0a6388.js:435:47687)
overrideMethod @ hook.js:608
initializeRealtimeSubscriptions @ index-5f0a6388.js:435
index-5f0a6388.js:50 ⚠️ [AssistantContextValidator] No valid assistant context found: Object
overrideMethod @ hook.js:608
resolveAssistantContext @ index-5f0a6388.js:50
(anonymous) @ index-5f0a6388.js:50
useMemo @ index-5f0a6388.js:38
Vn.useMemo @ index-5f0a6388.js:9
e6 @ index-5f0a6388.js:50
wI @ index-5f0a6388.js:38
vj @ index-5f0a6388.js:40
pj @ index-5f0a6388.js:40
xX @ index-5f0a6388.js:40
wx @ index-5f0a6388.js:40
dj @ index-5f0a6388.js:40
M @ index-5f0a6388.js:25
J @ index-5f0a6388.js:25
index-5f0a6388.js:49 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
overrideMethod @ hook.js:608
cv @ index-5f0a6388.js:49
nne @ index-5f0a6388.js:50
_initSupabaseAuthClient @ index-5f0a6388.js:50
lne @ index-5f0a6388.js:50
Ax @ index-5f0a6388.js:50
gne @ index-5f0a6388.js:52
QI @ index-5f0a6388.js:52
(anonymous) @ index-5f0a6388.js:52
index-5f0a6388.js:50 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
overrideMethod @ hook.js:608
(anonymous) @ index-5f0a6388.js:50
$5 @ index-5f0a6388.js:38
Vn.useMemo @ index-5f0a6388.js:9
e6 @ index-5f0a6388.js:50
wI @ index-5f0a6388.js:38
mT @ index-5f0a6388.js:40
vj @ index-5f0a6388.js:40
pj @ index-5f0a6388.js:40
xX @ index-5f0a6388.js:40
wx @ index-5f0a6388.js:40
dj @ index-5f0a6388.js:40
M @ index-5f0a6388.js:25
J @ index-5f0a6388.js:25
index.ts:5 Loaded contentScript
callback#:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
index-5f0a6388.js:50 ❌ [Supabase-Fixed] OAuth callback attempt 5 failed: Error: No user session found after OAuth callback
    at m8 (index-5f0a6388.js:50:37299)
    at async Oxe.handleOAuthCallback (index-5f0a6388.js:476:52978)
    at async index-5f0a6388.js:530:485
overrideMethod @ hook.js:608
m8 @ index-5f0a6388.js:50
index-5f0a6388.js:476 ❌ [UnifiedAuth-Fixed] OAuth callback error: Error: No user session found after OAuth callback
    at Oxe.handleOAuthCallback (index-5f0a6388.js:476:53014)
    at async index-5f0a6388.js:530:485
overrideMethod @ hook.js:608
handleOAuthCallback @ index-5f0a6388.js:476
index-5f0a6388.js:530 Auth callback error: Error: No user session found after OAuth callback
    at index-5f0a6388.js:530:536
overrideMethod @ hook.js:608
(anonymous) @ index-5f0a6388.js:530
