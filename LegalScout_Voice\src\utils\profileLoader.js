/**
 * Profile Loader Utility
 * 
 * This utility provides functions to load attorney profiles from various sources
 * and ensure they are properly synchronized.
 */

import { supabase } from '../lib/supabase';
import { ensureProfilePersistence } from '../services/syncHelpers';

/**
 * Load attorney profile with enhanced persistence
 * 
 * This function loads an attorney profile from Supabase and ensures it is
 * properly synchronized with Vapi and local storage.
 * 
 * @param {string} attorneyId - The ID of the attorney to load
 * @param {boolean} [forceSync=false] - Whether to force synchronization even if no discrepancies are found
 * @returns {Promise<Object>} The attorney profile
 */
export const loadAttorneyProfile = async (attorneyId, forceSync = false) => {
  console.log(`Loading attorney profile for ${attorneyId}`);
  
  try {
    // Get attorney data from Supabase
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorneyId)
      .single();
    
    if (error) {
      console.error('Error loading attorney profile from Supabase:', error);
      
      // Try to get from localStorage as fallback
      try {
        const storedAttorney = localStorage.getItem('attorney');
        if (storedAttorney) {
          const parsedAttorney = JSON.parse(storedAttorney);
          console.log('Retrieved attorney from localStorage as fallback');
          
          // If the stored attorney has the same ID, use it
          if (parsedAttorney.id === attorneyId) {
            return parsedAttorney;
          }
        }
      } catch (localStorageError) {
        console.error('Error retrieving attorney from localStorage:', localStorageError);
      }
      
      throw error;
    }
    
    // Ensure profile persistence
    await ensureProfilePersistence({
      attorneyId,
      localData: data,
      forceUpdate: forceSync
    });
    
    return data;
  } catch (error) {
    console.error('Error loading attorney profile:', error);
    throw error;
  }
};

/**
 * Load attorney profile by subdomain
 * 
 * This function loads an attorney profile from Supabase by subdomain and ensures
 * it is properly synchronized with Vapi and local storage.
 * 
 * @param {string} subdomain - The subdomain of the attorney to load
 * @param {boolean} [forceSync=false] - Whether to force synchronization even if no discrepancies are found
 * @returns {Promise<Object>} The attorney profile
 */
export const loadAttorneyBySubdomain = async (subdomain, forceSync = false) => {
  console.log(`Loading attorney profile for subdomain ${subdomain}`);
  
  try {
    // Get attorney data from Supabase
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('subdomain', subdomain)
      .single();
    
    if (error) {
      console.error('Error loading attorney profile from Supabase by subdomain:', error);
      throw error;
    }
    
    // Ensure profile persistence
    await ensureProfilePersistence({
      attorneyId: data.id,
      localData: data,
      forceUpdate: forceSync
    });
    
    return data;
  } catch (error) {
    console.error('Error loading attorney profile by subdomain:', error);
    throw error;
  }
};

/**
 * Load attorney profile by user ID
 * 
 * This function loads an attorney profile from Supabase by user ID and ensures
 * it is properly synchronized with Vapi and local storage.
 * 
 * @param {string} userId - The user ID of the attorney to load
 * @param {boolean} [forceSync=false] - Whether to force synchronization even if no discrepancies are found
 * @returns {Promise<Object>} The attorney profile
 */
export const loadAttorneyByUserId = async (userId, forceSync = false) => {
  console.log(`Loading attorney profile for user ID ${userId}`);
  
  try {
    // Get attorney data from Supabase
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (error) {
      console.error('Error loading attorney profile from Supabase by user ID:', error);
      throw error;
    }
    
    // Ensure profile persistence
    await ensureProfilePersistence({
      attorneyId: data.id,
      localData: data,
      forceUpdate: forceSync
    });
    
    return data;
  } catch (error) {
    console.error('Error loading attorney profile by user ID:', error);
    throw error;
  }
};
