/**
 * Simple direct fetch functions for VAPI API
 *
 * This module provides simple functions to directly fetch data from the VAPI API
 * without relying on the MCP server or other complex services.
 */

// Default API URL
const API_URL = 'https://api.vapi.ai/v1';

/**
 * Get API key from various sources
 * @returns {string|null} API key or null if not found
 */
const getApiKey = () => {
  // Try to get SECRET key first (for assistant operations), then public key (for client operations)
  const secretKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_SECRET_KEY) ||
                   (typeof window !== 'undefined' && window.VITE_VAPI_SECRET_KEY);

  const publicKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                   (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY);

  const localStorageKey = localStorage.getItem('vapi_api_key');

  // Return SECRET key first (for server operations), then public key, then localStorage
  return secretKey || publicKey || localStorageKey;
};

/**
 * List all assistants
 * @returns {Promise<Array>} List of assistants
 */
export const listAssistants = async () => {
  try {
    const apiKey = getApiKey();
    if (!apiKey) {
      console.error('No API key available');
      return [];
    }

    console.log('Direct API: Listing assistants');
    const response = await fetch(`${API_URL}/assistants`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      console.error(`Direct API error: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json();
    console.log(`Direct API: Found ${data.length} assistants`);
    return data;
  } catch (error) {
    console.error('Direct API error listing assistants:', error);
    return [];
  }
};

/**
 * Get assistant by ID
 * @param {string} assistantId Assistant ID
 * @returns {Promise<Object|null>} Assistant or null if not found
 */
export const getAssistant = async (assistantId) => {
  try {
    const apiKey = getApiKey();
    if (!apiKey) {
      console.error('No API key available');
      return null;
    }

    console.log(`Direct API: Getting assistant ${assistantId}`);
    const response = await fetch(`${API_URL}/assistants/${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        console.warn(`Direct API: Assistant not found: ${assistantId}`);
        return null;
      }
      console.error(`Direct API error: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    console.log(`Direct API: Found assistant ${data.name}`);
    return data;
  } catch (error) {
    console.error(`Direct API error getting assistant ${assistantId}:`, error);
    return null;
  }
};

export default {
  listAssistants,
  getAssistant
};
