/**
 * Voice Assistant Diagnostics Utility
 * 
 * This utility provides diagnostic functions for voice assistant integration.
 */

// Import direct API utilities
import { getApiKey } from './vapiDirectApi';

// Constants
const VAPI_API_URL = 'https://api.vapi.ai/v1';

/**
 * Test direct connection to voice assistant API
 * @returns {Promise<Object>} Test results
 */
export const testDirectConnection = async () => {
  try {
    console.log('[voiceAssistantDiagnostics] Testing direct connection to voice assistant API');
    
    // Get API key
    const apiKey = getApiKey();
    if (!apiKey) {
      return {
        success: false,
        error: 'No API key available',
        details: 'Could not find voice assistant API key in environment variables or localStorage'
      };
    }
    
    // Test connection with a simple API call
    const response = await fetch(`${VAPI_API_URL}/assistants?limit=1`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: 'Successfully connected to voice assistant API',
        assistantCount: data.length || 0
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API error: ${response.status} ${response.statusText}`,
        details: errorText
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Connection error: ${error.message}`,
      details: error.stack
    };
  }
};

/**
 * Get assistant by ID using direct API
 * @param {string} assistantId - Assistant ID
 * @returns {Promise<Object>} Test results
 */
export const testGetAssistant = async (assistantId) => {
  try {
    console.log(`[voiceAssistantDiagnostics] Testing get assistant: ${assistantId}`);
    
    if (!assistantId) {
      return {
        success: false,
        error: 'No assistant ID provided'
      };
    }
    
    // Get API key
    const apiKey = getApiKey();
    if (!apiKey) {
      return {
        success: false,
        error: 'No API key available',
        details: 'Could not find voice assistant API key in environment variables or localStorage'
      };
    }
    
    // Test connection with a simple API call
    const response = await fetch(`${VAPI_API_URL}/assistants/${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: `Successfully retrieved assistant: ${data.name}`,
        assistant: data
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API error: ${response.status} ${response.statusText}`,
        details: errorText
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Connection error: ${error.message}`,
      details: error.stack
    };
  }
};

/**
 * Get environment configuration for voice assistant
 * @returns {Object} Environment configuration
 */
export const getEnvironmentConfig = () => {
  return {
    publicKey: import.meta.env.VITE_VAPI_PUBLIC_KEY || 'Not set',
    secretKey: import.meta.env.VITE_VAPI_SECRET_KEY ? 'Set (hidden)' : 'Not set',
    forceMcpMode: false, // Updated in initAttorneyProfileManager.js to prioritize direct API
    localStorageKey: localStorage.getItem('vapi_api_key') ? 'Set (hidden)' : 'Not set'
  };
};

export default {
  testDirectConnection,
  testGetAssistant,
  getEnvironmentConfig
};
