/**
 * Tests for Enhanced Sync Tools
 */

import {
  syncAttorneyProfile,
  manageAuthState,
  validateConfiguration,
  checkPreviewConsistency
} from '../services/EnhancedSyncTools';

import * as EnhancedSyncHelpers from '../services/EnhancedSyncHelpers';

// Mock EnhancedSyncHelpers
jest.mock('../services/EnhancedSyncHelpers', () => ({
  fetchFromSupabase: jest.fn().mockResolvedValue({
    id: 'test-attorney-id',
    name: 'Test Attorney',
    email: '<EMAIL>',
    firm_name: 'Test Law Firm',
    welcome_message: 'Welcome to Test Law Firm',
    vapi_instructions: 'You are a legal assistant for Test Law Firm',
    voice_provider: 'playht',
    voice_id: 'ranger',
    vapi_assistant_id: 'test-assistant-id',
    ai_model: 'gpt-4o'
  }),
  updateSupabaseAttorney: jest.fn().mockResolvedValue({
    id: 'test-attorney-id',
    name: 'Test Attorney',
    email: '<EMAIL>',
    firm_name: 'Updated Law Firm',
    welcome_message: 'Welcome to Updated Law Firm',
    vapi_instructions: 'You are a legal assistant for Updated Law Firm',
    voice_provider: 'playht',
    voice_id: 'ranger',
    vapi_assistant_id: 'test-assistant-id',
    ai_model: 'gpt-4o'
  }),
  getAttorneyByEmail: jest.fn().mockResolvedValue({
    id: 'test-attorney-id',
    name: 'Test Attorney',
    email: '<EMAIL>',
    firm_name: 'Test Law Firm',
    welcome_message: 'Welcome to Test Law Firm',
    vapi_instructions: 'You are a legal assistant for Test Law Firm',
    voice_provider: 'playht',
    voice_id: 'ranger',
    vapi_assistant_id: 'test-assistant-id',
    ai_model: 'gpt-4o'
  }),
  getAttorneyByAuthId: jest.fn().mockResolvedValue({
    id: 'test-attorney-id',
    name: 'Test Attorney',
    email: '<EMAIL>',
    user_id: 'test-user-id',
    firm_name: 'Test Law Firm',
    welcome_message: 'Welcome to Test Law Firm',
    vapi_instructions: 'You are a legal assistant for Test Law Firm',
    voice_provider: 'playht',
    voice_id: 'ranger',
    vapi_assistant_id: 'test-assistant-id',
    ai_model: 'gpt-4o'
  }),
  getAttorneyById: jest.fn().mockResolvedValue({
    id: 'test-attorney-id',
    name: 'Test Attorney',
    email: '<EMAIL>',
    firm_name: 'Test Law Firm',
    welcome_message: 'Welcome to Test Law Firm',
    vapi_instructions: 'You are a legal assistant for Test Law Firm',
    voice_provider: 'playht',
    voice_id: 'ranger',
    vapi_assistant_id: 'test-assistant-id',
    ai_model: 'gpt-4o'
  }),
  createAttorney: jest.fn().mockResolvedValue({
    id: 'new-attorney-id',
    name: 'New Attorney',
    email: '<EMAIL>',
    firm_name: 'New Law Firm',
    welcome_message: 'Welcome to New Law Firm',
    vapi_instructions: 'You are a legal assistant for New Law Firm',
    voice_provider: 'playht',
    voice_id: 'ranger',
    ai_model: 'gpt-4o'
  }),
  updateAttorney: jest.fn().mockResolvedValue({
    id: 'test-attorney-id',
    name: 'Test Attorney',
    email: '<EMAIL>',
    firm_name: 'Updated Law Firm',
    welcome_message: 'Welcome to Updated Law Firm',
    vapi_instructions: 'You are a legal assistant for Updated Law Firm',
    voice_provider: 'playht',
    voice_id: 'ranger',
    vapi_assistant_id: 'test-assistant-id',
    ai_model: 'gpt-4o'
  }),
  fetchFromVapi: jest.fn().mockResolvedValue({
    id: 'test-assistant-id',
    name: 'Test Law Firm',
    firstMessage: 'Welcome to Test Law Firm',
    instructions: 'You are a legal assistant for Test Law Firm',
    voice: {
      provider: 'playht',
      voiceId: 'ranger'
    },
    llm: {
      provider: 'openai',
      model: 'gpt-4o'
    }
  }),
  createVapiAssistant: jest.fn().mockResolvedValue({
    id: 'new-assistant-id',
    name: 'New Law Firm',
    firstMessage: 'Welcome to New Law Firm',
    instructions: 'You are a legal assistant for New Law Firm',
    voice: {
      provider: 'playht',
      voiceId: 'ranger'
    },
    llm: {
      provider: 'openai',
      model: 'gpt-4o'
    }
  }),
  updateVapiAssistant: jest.fn().mockResolvedValue({
    id: 'test-assistant-id',
    name: 'Updated Law Firm',
    firstMessage: 'Welcome to Updated Law Firm',
    instructions: 'You are a legal assistant for Updated Law Firm',
    voice: {
      provider: 'playht',
      voiceId: 'ranger'
    },
    llm: {
      provider: 'openai',
      model: 'gpt-4o'
    }
  }),
  getVapiAssistant: jest.fn().mockResolvedValue({
    id: 'test-assistant-id',
    name: 'Test Law Firm',
    firstMessage: 'Welcome to Test Law Firm',
    instructions: 'You are a legal assistant for Test Law Firm',
    voice: {
      provider: 'playht',
      voiceId: 'ranger'
    },
    llm: {
      provider: 'openai',
      model: 'gpt-4o'
    }
  }),
  findProfileDiscrepancies: jest.fn().mockReturnValue({}),
  getValidVoicesForProvider: jest.fn().mockResolvedValue(['ranger', 'waylon', 'leyro', 'nova']),
  ensureProfilePersistence: jest.fn().mockResolvedValue({
    action: 'none',
    success: true,
    message: 'Profile persistence ensured',
    sources: {
      supabase: true,
      vapi: true
    },
    vapiResult: {
      action: 'none',
      assistantId: 'test-assistant-id'
    }
  })
}));

describe('EnhancedSyncTools', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
  });
  
  describe('syncAttorneyProfile', () => {
    it('should sync attorney profile', async () => {
      const result = await syncAttorneyProfile({
        attorneyId: 'test-attorney-id'
      });
      
      expect(result).toEqual({
        action: 'none',
        assistantId: 'test-assistant-id',
        message: 'Profile persistence ensured',
        success: true,
        sources: {
          supabase: true,
          vapi: true
        },
        discrepancies: null
      });
      expect(EnhancedSyncHelpers.ensureProfilePersistence).toHaveBeenCalledWith({
        attorneyId: 'test-attorney-id',
        forceUpdate: false
      });
    });
    
    it('should force update if forceUpdate is true', async () => {
      await syncAttorneyProfile({
        attorneyId: 'test-attorney-id',
        forceUpdate: true
      });
      
      expect(EnhancedSyncHelpers.ensureProfilePersistence).toHaveBeenCalledWith({
        attorneyId: 'test-attorney-id',
        forceUpdate: true
      });
    });
    
    it('should handle errors', async () => {
      // Mock ensureProfilePersistence to throw an error
      EnhancedSyncHelpers.ensureProfilePersistence.mockRejectedValueOnce(new Error('Test error'));
      
      const result = await syncAttorneyProfile({
        attorneyId: 'test-attorney-id'
      });
      
      expect(result).toEqual({
        action: 'error',
        error: 'Test error',
        message: 'Error syncing attorney profile: Test error'
      });
    });
  });
  
  describe('manageAuthState', () => {
    it('should handle login action', async () => {
      const result = await manageAuthState({
        authData: {
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
            user_metadata: {
              name: 'Test User'
            }
          },
          session: {
            access_token: 'test-access-token'
          }
        },
        action: 'login'
      });
      
      expect(result.action).toBe('login');
      expect(result.success).toBe(true);
      expect(result.attorney).toBeDefined();
      expect(result.syncResult).toBeDefined();
      expect(result.session).toBeDefined();
      expect(EnhancedSyncHelpers.getAttorneyByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(EnhancedSyncHelpers.ensureProfilePersistence).toHaveBeenCalled();
    });
    
    it('should handle logout action', async () => {
      const result = await manageAuthState({
        authData: {},
        action: 'logout'
      });
      
      expect(result).toEqual({
        action: 'logout',
        success: true,
        message: 'User logged out successfully'
      });
    });
    
    it('should handle refresh action', async () => {
      const result = await manageAuthState({
        authData: {
          user: {
            id: 'test-user-id'
          }
        },
        action: 'refresh'
      });
      
      expect(result.action).toBe('refresh');
      expect(result.success).toBe(true);
      expect(result.attorney).toBeDefined();
      expect(result.syncResult).toBeDefined();
      expect(EnhancedSyncHelpers.getAttorneyByAuthId).toHaveBeenCalledWith('test-user-id');
      expect(EnhancedSyncHelpers.ensureProfilePersistence).toHaveBeenCalled();
    });
    
    it('should handle unknown action', async () => {
      const result = await manageAuthState({
        authData: {},
        action: 'unknown'
      });
      
      expect(result).toEqual({
        action: 'unknown',
        success: false,
        message: 'Unknown action: unknown'
      });
    });
  });
  
  describe('validateConfiguration', () => {
    it('should validate configuration', async () => {
      const result = await validateConfiguration({
        attorneyId: 'test-attorney-id',
        configData: {
          firm_name: 'Updated Law Firm',
          welcome_message: 'Welcome to Updated Law Firm',
          vapi_instructions: 'You are a legal assistant for Updated Law Firm',
          voice_provider: 'playht',
          voice_id: 'ranger',
          ai_model: 'gpt-4o'
        }
      });
      
      expect(result.valid).toBe(true);
      expect(result.message).toBe('Configuration is valid');
      expect(EnhancedSyncHelpers.getAttorneyById).toHaveBeenCalledWith('test-attorney-id');
      expect(EnhancedSyncHelpers.getValidVoicesForProvider).toHaveBeenCalledWith('playht');
    });
    
    it('should validate configuration with missing fields', async () => {
      const result = await validateConfiguration({
        attorneyId: 'test-attorney-id',
        configData: {
          firm_name: '',
          welcome_message: '',
          vapi_instructions: 'You are a legal assistant for Updated Law Firm',
          voice_provider: 'playht',
          voice_id: 'ranger',
          ai_model: 'gpt-4o'
        }
      });
      
      expect(result.valid).toBe(false);
      expect(result.missingFields).toBeDefined();
      expect(result.message).toBe('Configuration validation failed');
    });
    
    it('should validate configuration with invalid voice', async () => {
      // Mock getValidVoicesForProvider to return a list that doesn't include the voice ID
      EnhancedSyncHelpers.getValidVoicesForProvider.mockResolvedValueOnce(['waylon', 'leyro', 'nova']);
      
      const result = await validateConfiguration({
        attorneyId: 'test-attorney-id',
        configData: {
          firm_name: 'Updated Law Firm',
          welcome_message: 'Welcome to Updated Law Firm',
          vapi_instructions: 'You are a legal assistant for Updated Law Firm',
          voice_provider: 'playht',
          voice_id: 'invalid-voice',
          ai_model: 'gpt-4o'
        }
      });
      
      expect(result.valid).toBe(false);
      expect(result.validationErrors).toBeDefined();
      expect(result.message).toBe('Configuration validation failed');
    });
  });
  
  describe('checkPreviewConsistency', () => {
    it('should check preview consistency', async () => {
      const result = await checkPreviewConsistency({
        attorneyId: 'test-attorney-id'
      });
      
      expect(result.consistent).toBe(true);
      expect(result.message).toBe('Preview is consistent with deployment');
      expect(EnhancedSyncHelpers.getAttorneyById).toHaveBeenCalledWith('test-attorney-id');
      expect(EnhancedSyncHelpers.getVapiAssistant).toHaveBeenCalledWith('test-assistant-id');
      expect(EnhancedSyncHelpers.findProfileDiscrepancies).toHaveBeenCalled();
    });
    
    it('should handle missing attorney', async () => {
      // Mock getAttorneyById to return null
      EnhancedSyncHelpers.getAttorneyById.mockResolvedValueOnce(null);
      
      const result = await checkPreviewConsistency({
        attorneyId: 'non-existent-attorney-id'
      });
      
      expect(result.consistent).toBe(false);
      expect(result.errors).toContain('Attorney not found');
      expect(result.message).toBe('Cannot check consistency for non-existent attorney');
    });
    
    it('should handle missing assistant ID', async () => {
      // Mock getAttorneyById to return an attorney without an assistant ID
      EnhancedSyncHelpers.getAttorneyById.mockResolvedValueOnce({
        id: 'test-attorney-id',
        name: 'Test Attorney',
        email: '<EMAIL>',
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        ai_model: 'gpt-4o'
      });
      
      const result = await checkPreviewConsistency({
        attorneyId: 'test-attorney-id'
      });
      
      expect(result.consistent).toBe(false);
      expect(result.errors).toContain('No Vapi assistant ID found');
      expect(result.message).toBe('Attorney record is missing Vapi assistant ID');
    });
    
    it('should handle missing assistant in Vapi', async () => {
      // Mock getVapiAssistant to return null
      EnhancedSyncHelpers.getVapiAssistant.mockResolvedValueOnce(null);
      
      const result = await checkPreviewConsistency({
        attorneyId: 'test-attorney-id'
      });
      
      expect(result.consistent).toBe(false);
      expect(result.errors).toContain('Vapi assistant not found');
      expect(result.action).toBe('fixed');
      expect(result.syncResult).toBeDefined();
      expect(EnhancedSyncHelpers.ensureProfilePersistence).toHaveBeenCalledWith({
        attorneyId: 'test-attorney-id',
        forceUpdate: true
      });
    });
    
    it('should handle discrepancies', async () => {
      // Mock findProfileDiscrepancies to return discrepancies
      EnhancedSyncHelpers.findProfileDiscrepancies.mockReturnValueOnce({
        firm_name: {
          supabase: 'Test Law Firm',
          vapi: 'Different Law Firm'
        }
      });
      
      const result = await checkPreviewConsistency({
        attorneyId: 'test-attorney-id'
      });
      
      expect(result.consistent).toBe(false);
      expect(result.discrepancies).toBeDefined();
      expect(result.action).toBe('fixed');
      expect(result.syncResult).toBeDefined();
      expect(EnhancedSyncHelpers.ensureProfilePersistence).toHaveBeenCalledWith({
        attorneyId: 'test-attorney-id',
        forceUpdate: true
      });
    });
  });
});
