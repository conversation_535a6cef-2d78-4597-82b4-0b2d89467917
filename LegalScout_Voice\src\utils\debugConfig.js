// React DevTools Enhancement Configuration
// This file provides utilities and configurations to improve debugging experience
import React from 'react';

// Global debugging configuration
window.LegalScoutDebug = {
  enabled: true,
  logLevel: 'verbose', // 'error', 'warn', 'info', 'verbose'
  components: {
    vapiCall: true,
    mapView: true,
    dossier: true,
    attorneySearch: true
  },
  webhooks: {
    logRequests: true,
    logResponses: true
  }
};

// Debug logging utility that integrates with React DevTools
export const createDebugger = (componentName) => {
  return {
    log: (message, data) => {
      if (!window.LegalScoutDebug?.enabled) return;
      
      const componentConfig = window.LegalScoutDebug?.components?.[componentName.toLowerCase()];
      if (componentConfig === false) return;
      
      console.log(`[${componentName}] ${message}`, data);
      
      // Add __DEV__ property for better visibility in React DevTools
      if (typeof data === 'object' && data !== null) {
        data.__DEV__ = { 
          component: componentName,
          timestamp: new Date().toISOString(),
          message
        };
      }
    },
    
    group: (label) => {
      if (!window.LegalScoutDebug?.enabled) return;
      console.group(`[${componentName}] ${label}`);
    },
    
    groupEnd: () => {
      if (!window.LegalScoutDebug?.enabled) return;
      console.groupEnd();
    },
    
    time: (label) => {
      if (!window.LegalScoutDebug?.enabled) return;
      console.time(`[${componentName}] ${label}`);
    },
    
    timeEnd: (label) => {
      if (!window.LegalScoutDebug?.enabled) return;
      console.timeEnd(`[${componentName}] ${label}`);
    }
  };
};

// Enhance component for better React DevTools visibility
export const withDevTools = (Component, options = {}) => {
  // Use a named function for better Fast Refresh compatibility
  function WithDevTools(props) {
    return React.createElement(Component, props);
  }
  
  // Apply custom displayName or use Component's name
  WithDevTools.displayName = options.displayName || `WithDevTools(${Component.name || 'Component'})`;
  
  // Add custom __DEV__ property to help with debugging
  WithDevTools.__DEV__ = {
    componentType: options.type || 'unknown',
    description: options.description || '',
    responsibleFor: options.responsibleFor || []
  };
  
  return WithDevTools;
};

// User journey tracking for React DevTools
export const trackUserJourney = (step, data = {}) => {
  if (!window.LegalScoutDebug?.enabled) return;
  
  console.log(`User Journey [${step}]:`, {
    timestamp: new Date().toISOString(),
    ...data,
    __DEV__: { 
      type: 'USER_JOURNEY',
      step
    }
  });
};

// Hook to expose component state to React DevTools
export const useDevToolsState = (stateName, stateValue) => {
  // This creates a top-level property in React DevTools
  React.useDebugValue(stateValue);
  
  // For components that need more visibility
  if (window.LegalScoutDebug?.enabled && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    // Ensure legalScout object exists
    if (!window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout = {};
    }
    // Now safely assign the value
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout[stateName] = stateValue;
  }
  
  return stateValue;
}; 