/**
 * Vapi Direct API Service
 *
 * This service provides direct integration with Vapi's REST API,
 * bypassing the MCP server for more reliable access to Vapi resources.
 */

// Default API URL
const DEFAULT_API_URL = 'https://api.vapi.ai/v1';

/**
 * Get API key from various sources
 * @returns {string|null} - API key or null if not found
 */
const getApiKey = () => {
  // Try to get SECRET key first (for assistant operations), then public key (for client operations)
  const secretKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_SECRET_KEY) ||
                   (typeof window !== 'undefined' && window.VITE_VAPI_SECRET_KEY);

  const publicKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                   (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY);

  const localStorageKey = localStorage.getItem('vapi_api_key');

  // Log available keys for debugging (without showing the full key)
  if (secretKey) {
    console.log('[VapiDirectService] Found SECRET API key in environment variables');
  } else if (publicKey) {
    console.log('[VapiDirectService] Found PUBLIC API key in environment variables');
  }

  if (localStorageKey) {
    console.log('[VapiDirectService] Found API key in localStorage');
  }

  // Try to get the key from the MCP client if available
  if (window.mcp && window.mcp.config && window.mcp.config.vapiApiKey) {
    console.log('[VapiDirectService] Found API key in MCP client config');
    return window.mcp.config.vapiApiKey;
  }

  // Return SECRET key first (for server operations), then public key, then localStorage
  return secretKey || publicKey || localStorageKey;
};

/**
 * Make a request to the Vapi API
 * @param {string} endpoint - API endpoint (without leading slash)
 * @param {Object} options - Fetch options
 * @param {string} apiKey - Optional API key (will try to get from environment if not provided)
 * @returns {Promise<Object>} - Response data
 */
const makeRequest = async (endpoint, options = {}, apiKey = null) => {
  try {
    // Get API key if not provided
    const key = apiKey || getApiKey();

    if (!key) {
      console.error('[VapiDirectService] No API key available for VAPI API request');
      throw new Error('No API key available for VAPI API request. Please check your configuration.');
    }

    // Create full URL
    const url = `${DEFAULT_API_URL}/${endpoint}`;
    console.log(`[VapiDirectService] Making request to: ${url}`);

    // Set up headers (mask the API key in logs)
    const maskedKey = key.substring(0, 4) + '...' + key.substring(key.length - 4);
    console.log(`[VapiDirectService] Using API key: ${maskedKey}`);

    const headers = {
      'Authorization': `Bearer ${key}`,
      'Content-Type': 'application/json',
      ...(options.headers || {})
    };

    // Make request
    console.log(`[VapiDirectService] Sending ${options.method || 'GET'} request`);
    const response = await fetch(url, {
      ...options,
      headers
    });

    // Check if response is OK
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[VapiDirectService] API error (${response.status}): ${errorText}`);
      throw new Error(`API error (${response.status}): ${errorText}`);
    }

    // Parse response
    const data = await response.json();
    console.log(`[VapiDirectService] Received response from ${endpoint}:`,
                endpoint.includes('assistants') ? `${data.length} assistants` : 'data');
    return data;
  } catch (error) {
    console.error(`[VapiDirectService] Error in ${endpoint}:`, error);
    throw error;
  }
};

/**
 * Get an assistant by ID
 * @param {string} assistantId - ID of the assistant to get
 * @param {string} apiKey - Optional API key
 * @returns {Promise<Object>} - Assistant data
 */
export const getAssistant = async (assistantId, apiKey = null) => {
  try {
    console.log(`[VapiDirectService] Getting assistant: ${assistantId}`);
    return await makeRequest(`assistants/${assistantId}`, {
      method: 'GET'
    }, apiKey);
  } catch (error) {
    // If 404, return null to indicate assistant not found
    if (error.message.includes('404')) {
      console.warn(`[VapiDirectService] Assistant not found: ${assistantId}`);
      return null;
    }
    throw error;
  }
};

/**
 * List all assistants
 * @param {Object} options - List options (limit, offset, etc.)
 * @param {string} apiKey - Optional API key
 * @returns {Promise<Array>} - List of assistants
 */
export const listAssistants = async (options = {}, apiKey = null) => {
  try {
    console.log('[VapiDirectService] Listing assistants');

    // Build query string
    const queryParams = new URLSearchParams();
    if (options.limit) queryParams.append('limit', options.limit);
    if (options.offset) queryParams.append('offset', options.offset);

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

    return await makeRequest(`assistants${queryString}`, {
      method: 'GET'
    }, apiKey);
  } catch (error) {
    console.error('[VapiDirectService] Error listing assistants:', error);
    return [];
  }
};

/**
 * Create a new assistant
 * @param {Object} assistantData - Assistant configuration
 * @param {string} apiKey - Optional API key
 * @returns {Promise<Object>} - Created assistant
 */
export const createAssistant = async (assistantData, apiKey = null) => {
  console.log('[VapiDirectService] Creating assistant:', assistantData.name);
  return await makeRequest('assistants', {
    method: 'POST',
    body: JSON.stringify(assistantData)
  }, apiKey);
};

/**
 * Update an existing assistant
 * @param {string} assistantId - ID of the assistant to update
 * @param {Object} assistantData - Updated assistant configuration
 * @param {string} apiKey - Optional API key
 * @returns {Promise<Object>} - Updated assistant
 */
export const updateAssistant = async (assistantId, assistantData, apiKey = null) => {
  console.log(`[VapiDirectService] Updating assistant: ${assistantId}`);
  return await makeRequest(`assistants/${assistantId}`, {
    method: 'PATCH',
    body: JSON.stringify(assistantData)
  }, apiKey);
};

export default {
  getAssistant,
  listAssistants,
  createAssistant,
  updateAssistant
};
