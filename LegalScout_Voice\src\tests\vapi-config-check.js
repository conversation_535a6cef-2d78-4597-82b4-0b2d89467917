/**
 * Vapi Configuration Check Script
 * 
 * This script checks the current Vapi configuration to ensure
 * webhooks are properly set up for your assistant.
 */

import fetch from 'node-fetch';

// Configuration
const VAPI_API_KEY = process.env.VAPI_TOKEN || process.env.VITE_VAPI_SECRET_KEY;
const VAPI_BASE_URL = 'https://api.vapi.ai';
const ASSISTANT_ID = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
const EXPECTED_WEBHOOK_URL = 'https://legalscout.app/api/webhook/vapi-call';

/**
 * Make Vapi API request
 */
async function vapiRequest(endpoint, options = {}) {
  const url = `${VAPI_BASE_URL}${endpoint}`;
  
  try {
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${VAPI_API_KEY}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Vapi API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error calling ${endpoint}:`, error.message);
    return null;
  }
}

/**
 * Check organization settings
 */
async function checkOrganizationSettings() {
  console.log('🔍 Checking organization settings...');
  
  const org = await vapiRequest('/org');
  if (!org) {
    console.log('❌ Failed to get organization settings');
    return null;
  }

  console.log('✅ Organization found:');
  console.log('  Name:', org.name);
  console.log('  ID:', org.id);
  
  if (org.serverUrl) {
    console.log('  Server URL:', org.serverUrl);
    if (org.serverUrl === EXPECTED_WEBHOOK_URL) {
      console.log('  ✅ Webhook URL matches expected');
    } else {
      console.log('  ⚠️  Webhook URL does not match expected');
      console.log('     Expected:', EXPECTED_WEBHOOK_URL);
      console.log('     Actual:', org.serverUrl);
    }
  } else {
    console.log('  ❌ No webhook URL configured');
  }

  if (org.serverUrlSecret) {
    console.log('  ✅ Webhook secret is configured');
  } else {
    console.log('  ⚠️  No webhook secret configured');
  }

  return org;
}

/**
 * Check assistant configuration
 */
async function checkAssistantConfig() {
  console.log('\n🔍 Checking assistant configuration...');
  
  const assistant = await vapiRequest(`/assistant/${ASSISTANT_ID}`);
  if (!assistant) {
    console.log('❌ Failed to get assistant configuration');
    return null;
  }

  console.log('✅ Assistant found:');
  console.log('  ID:', assistant.id);
  console.log('  Name:', assistant.name || 'Unnamed');
  
  if (assistant.serverUrl) {
    console.log('  Server URL:', assistant.serverUrl);
    if (assistant.serverUrl === EXPECTED_WEBHOOK_URL) {
      console.log('  ✅ Assistant webhook URL matches expected');
    } else {
      console.log('  ⚠️  Assistant webhook URL does not match expected');
    }
  } else {
    console.log('  ℹ️  No assistant-specific webhook URL (using org default)');
  }

  if (assistant.voice) {
    console.log('  Voice provider:', assistant.voice.provider);
    console.log('  Voice ID:', assistant.voice.voiceId);
  }

  return assistant;
}

/**
 * Check recent calls for the assistant
 */
async function checkRecentCalls() {
  console.log('\n🔍 Checking recent calls...');
  
  const calls = await vapiRequest('/call');
  if (!calls) {
    console.log('❌ Failed to get calls');
    return null;
  }

  const assistantCalls = calls.filter(call => call.assistantId === ASSISTANT_ID);
  
  console.log(`✅ Found ${assistantCalls.length} calls for assistant`);
  
  if (assistantCalls.length > 0) {
    console.log('Recent calls:');
    assistantCalls.slice(0, 5).forEach((call, index) => {
      console.log(`  ${index + 1}. ID: ${call.id}`);
      console.log(`     Status: ${call.status}`);
      console.log(`     Created: ${call.createdAt}`);
      console.log(`     Duration: ${call.duration || 'N/A'} seconds`);
      if (call.endedReason) {
        console.log(`     End reason: ${call.endedReason}`);
      }
    });
  }

  return assistantCalls;
}

/**
 * Test webhook endpoint
 */
async function testWebhookEndpoint() {
  console.log('\n🔍 Testing webhook endpoint...');
  
  try {
    const response = await fetch(EXPECTED_WEBHOOK_URL, {
      method: 'GET'
    });

    if (response.status === 405) {
      console.log('✅ Webhook endpoint exists (returns 405 for GET as expected)');
      return true;
    } else {
      console.log(`⚠️  Webhook endpoint returned unexpected status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Webhook endpoint not accessible:', error.message);
    return false;
  }
}

/**
 * Check phone numbers
 */
async function checkPhoneNumbers() {
  console.log('\n🔍 Checking phone numbers...');
  
  const phoneNumbers = await vapiRequest('/phone-number');
  if (!phoneNumbers) {
    console.log('❌ Failed to get phone numbers');
    return null;
  }

  console.log(`✅ Found ${phoneNumbers.length} phone numbers`);
  
  phoneNumbers.forEach((phone, index) => {
    console.log(`  ${index + 1}. ${phone.number} (${phone.provider})`);
    console.log(`     ID: ${phone.id}`);
    if (phone.assistantId) {
      console.log(`     Assistant: ${phone.assistantId}`);
      if (phone.assistantId === ASSISTANT_ID) {
        console.log('     ✅ Linked to your assistant');
      }
    }
  });

  return phoneNumbers;
}

/**
 * Main configuration check
 */
async function runConfigCheck() {
  console.log('🚀 Starting Vapi Configuration Check\n');
  
  if (!VAPI_API_KEY) {
    console.log('❌ VAPI_API_KEY not found in environment variables');
    return;
  }

  console.log('Using API key:', VAPI_API_KEY.substring(0, 8) + '...');
  console.log('Expected webhook URL:', EXPECTED_WEBHOOK_URL);
  console.log('Assistant ID:', ASSISTANT_ID);
  console.log('');

  // Run all checks
  const org = await checkOrganizationSettings();
  const assistant = await checkAssistantConfig();
  const calls = await checkRecentCalls();
  const webhookWorking = await testWebhookEndpoint();
  const phoneNumbers = await checkPhoneNumbers();

  // Summary
  console.log('\n📊 CONFIGURATION SUMMARY:');
  console.log('  Organization configured:', org ? '✅ Yes' : '❌ No');
  console.log('  Assistant found:', assistant ? '✅ Yes' : '❌ No');
  console.log('  Webhook URL configured:', org?.serverUrl ? '✅ Yes' : '❌ No');
  console.log('  Webhook secret configured:', org?.serverUrlSecret ? '✅ Yes' : '❌ No');
  console.log('  Webhook endpoint accessible:', webhookWorking ? '✅ Yes' : '❌ No');
  console.log('  Recent calls found:', calls?.length > 0 ? `✅ Yes (${calls.length})` : '❌ No');
  console.log('  Phone numbers configured:', phoneNumbers?.length > 0 ? `✅ Yes (${phoneNumbers.length})` : '❌ No');

  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (!org?.serverUrl) {
    console.log('  ⚠️  Configure webhook URL in Vapi organization settings');
    console.log(`     Set to: ${EXPECTED_WEBHOOK_URL}`);
  }
  
  if (!org?.serverUrlSecret) {
    console.log('  ⚠️  Configure webhook secret for security');
  }
  
  if (!webhookWorking) {
    console.log('  ⚠️  Webhook endpoint is not accessible - check deployment');
  }
  
  if (calls?.length === 0) {
    console.log('  ℹ️  No recent calls found - make a test call to verify integration');
  }
}

// Run check if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runConfigCheck().catch(console.error);
}

export { runConfigCheck, checkOrganizationSettings, checkAssistantConfig };
