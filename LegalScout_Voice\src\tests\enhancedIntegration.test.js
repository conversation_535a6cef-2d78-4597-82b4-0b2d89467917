/**
 * Tests for Enhanced Integration Utilities
 */

import {
  initializeEnhancedVapi,
  ensureAttorneyAssistant,
  syncAttorneyAssistant,
  createOutboundCall,
  listAssistants,
  getAssistant,
  getInitializationState
} from '../utils/enhancedIntegration';

import { enhancedVapiAssistantManager } from '../services/EnhancedVapiAssistantManager';
import { enhancedVapiMcpService } from '../services/EnhancedVapiMcpService';
import { mcpConfig } from '../config/mcp.config';

// Mock enhancedVapiAssistantManager
jest.mock('../services/EnhancedVapiAssistantManager', () => ({
  enhancedVapiAssistantManager: {
    initialize: jest.fn().mockResolvedValue(true),
    ensureAssistant: jest.fn().mockImplementation(attorney => Promise.resolve({
      ...attorney,
      vapi_assistant_id: attorney.vapi_assistant_id || 'new-assistant-id'
    })),
    syncAssistant: jest.fn().mockImplementation(attorney => Promise.resolve({
      id: attorney.vapi_assistant_id || 'test-assistant-id',
      name: attorney.firm_name || 'Test Law Firm',
      firstMessage: attorney.welcome_message || 'Welcome to Test Law Firm',
      instructions: attorney.vapi_instructions || 'You are a legal assistant for Test Law Firm'
    }))
  }
}));

// Mock enhancedVapiMcpService
jest.mock('../services/EnhancedVapiMcpService', () => ({
  enhancedVapiMcpService: {
    connect: jest.fn().mockResolvedValue(true),
    createCall: jest.fn().mockResolvedValue({
      id: 'test-call-id',
      assistantId: 'test-assistant-id',
      customer: {
        phoneNumber: '+**********'
      },
      status: 'queued'
    }),
    listAssistants: jest.fn().mockResolvedValue([
      {
        id: 'test-assistant-id',
        name: 'Test Assistant'
      }
    ]),
    getAssistant: jest.fn().mockResolvedValue({
      id: 'test-assistant-id',
      name: 'Test Assistant',
      firstMessage: 'Welcome to Test Law Firm',
      instructions: 'You are a legal assistant for Test Law Firm',
      voice: {
        provider: 'playht',
        voiceId: 'ranger'
      },
      llm: {
        provider: 'openai',
        model: 'gpt-4o'
      }
    })
  }
}));

// Mock mcpConfig
jest.mock('../config/mcp.config', () => ({
  mcpConfig: {
    voice: {
      vapi: {
        publicKey: 'test-public-key',
        secretKey: 'test-secret-key',
        mcpUrl: 'https://test-mcp-url.com',
        defaultVoice: 'sarah',
        defaultProvider: '11labs',
        defaultAssistantId: 'default-assistant-id'
      }
    }
  }
}));

describe('enhancedIntegration', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
  });
  
  describe('initializeEnhancedVapi', () => {
    it('should initialize enhanced Vapi services', async () => {
      const result = await initializeEnhancedVapi();
      
      expect(result).toBe(true);
      expect(enhancedVapiMcpService.connect).toHaveBeenCalledWith('test-public-key', undefined);
      expect(enhancedVapiAssistantManager.initialize).toHaveBeenCalled();
    });
    
    it('should initialize with custom options', async () => {
      const result = await initializeEnhancedVapi({
        apiKey: 'custom-api-key',
        forceDirect: true,
        mcpUrl: 'https://custom-mcp-url.com'
      });
      
      expect(result).toBe(true);
      expect(enhancedVapiMcpService.connect).toHaveBeenCalledWith('custom-api-key', true);
      expect(enhancedVapiAssistantManager.initialize).toHaveBeenCalled();
    });
    
    it('should return false if MCP service initialization fails', async () => {
      // Mock enhancedVapiMcpService.connect to return false
      enhancedVapiMcpService.connect.mockResolvedValueOnce(false);
      
      const result = await initializeEnhancedVapi();
      
      expect(result).toBe(false);
      expect(enhancedVapiMcpService.connect).toHaveBeenCalled();
      expect(enhancedVapiAssistantManager.initialize).not.toHaveBeenCalled();
    });
    
    it('should return false if assistant manager initialization fails', async () => {
      // Mock enhancedVapiAssistantManager.initialize to return false
      enhancedVapiAssistantManager.initialize.mockResolvedValueOnce(false);
      
      const result = await initializeEnhancedVapi();
      
      expect(result).toBe(false);
      expect(enhancedVapiMcpService.connect).toHaveBeenCalled();
      expect(enhancedVapiAssistantManager.initialize).toHaveBeenCalled();
    });
  });
  
  describe('ensureAttorneyAssistant', () => {
    it('should ensure attorney has a valid assistant', async () => {
      // Mock initializeEnhancedVapi to return true
      const initializeSpy = jest.spyOn(global, 'initializeEnhancedVapi').mockResolvedValue(true);
      
      const attorney = {
        id: 'test-attorney-id',
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        ai_model: 'gpt-4o'
      };
      
      const result = await ensureAttorneyAssistant(attorney);
      
      expect(result).toEqual({
        ...attorney,
        vapi_assistant_id: 'new-assistant-id'
      });
      expect(initializeSpy).toHaveBeenCalled();
      expect(enhancedVapiAssistantManager.ensureAssistant).toHaveBeenCalledWith(attorney);
      
      // Clean up
      initializeSpy.mockRestore();
    });
    
    it('should throw an error if attorney is not provided', async () => {
      await expect(ensureAttorneyAssistant()).rejects.toThrow('Attorney object is required');
    });
    
    it('should throw an error if initialization fails', async () => {
      // Mock initializeEnhancedVapi to return false
      const initializeSpy = jest.spyOn(global, 'initializeEnhancedVapi').mockResolvedValue(false);
      
      const attorney = {
        id: 'test-attorney-id',
        firm_name: 'Test Law Firm'
      };
      
      await expect(ensureAttorneyAssistant(attorney)).rejects.toThrow('Failed to initialize enhanced Vapi services');
      
      // Clean up
      initializeSpy.mockRestore();
    });
  });
  
  describe('syncAttorneyAssistant', () => {
    it('should sync attorney assistant', async () => {
      // Mock initializeEnhancedVapi to return true
      const initializeSpy = jest.spyOn(global, 'initializeEnhancedVapi').mockResolvedValue(true);
      
      const attorney = {
        id: 'test-attorney-id',
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        vapi_assistant_id: 'test-assistant-id',
        ai_model: 'gpt-4o'
      };
      
      const result = await syncAttorneyAssistant(attorney);
      
      expect(result).toEqual({
        id: 'test-assistant-id',
        name: 'Test Law Firm',
        firstMessage: 'Welcome to Test Law Firm',
        instructions: 'You are a legal assistant for Test Law Firm'
      });
      expect(initializeSpy).toHaveBeenCalled();
      expect(enhancedVapiAssistantManager.syncAssistant).toHaveBeenCalledWith(attorney);
      
      // Clean up
      initializeSpy.mockRestore();
    });
    
    it('should throw an error if attorney is not provided', async () => {
      await expect(syncAttorneyAssistant()).rejects.toThrow('Attorney object is required');
    });
  });
  
  describe('createOutboundCall', () => {
    it('should create an outbound call', async () => {
      // Mock initializeEnhancedVapi to return true
      const initializeSpy = jest.spyOn(global, 'initializeEnhancedVapi').mockResolvedValue(true);
      
      const result = await createOutboundCall('test-assistant-id', '+**********');
      
      expect(result).toEqual({
        id: 'test-call-id',
        assistantId: 'test-assistant-id',
        customer: {
          phoneNumber: '+**********'
        },
        status: 'queued'
      });
      expect(initializeSpy).toHaveBeenCalled();
      expect(enhancedVapiMcpService.createCall).toHaveBeenCalledWith('test-assistant-id', '+**********', {});
      
      // Clean up
      initializeSpy.mockRestore();
    });
    
    it('should create an outbound call with options', async () => {
      // Mock initializeEnhancedVapi to return true
      const initializeSpy = jest.spyOn(global, 'initializeEnhancedVapi').mockResolvedValue(true);
      
      const options = {
        phoneNumberId: 'test-phone-number-id',
        scheduledAt: '2025-03-25T22:39:27.771Z'
      };
      
      await createOutboundCall('test-assistant-id', '+**********', options);
      
      expect(enhancedVapiMcpService.createCall).toHaveBeenCalledWith('test-assistant-id', '+**********', options);
      
      // Clean up
      initializeSpy.mockRestore();
    });
  });
  
  describe('listAssistants', () => {
    it('should list assistants', async () => {
      // Mock initializeEnhancedVapi to return true
      const initializeSpy = jest.spyOn(global, 'initializeEnhancedVapi').mockResolvedValue(true);
      
      const result = await listAssistants();
      
      expect(result).toEqual([
        {
          id: 'test-assistant-id',
          name: 'Test Assistant'
        }
      ]);
      expect(initializeSpy).toHaveBeenCalled();
      expect(enhancedVapiMcpService.listAssistants).toHaveBeenCalled();
      
      // Clean up
      initializeSpy.mockRestore();
    });
  });
  
  describe('getAssistant', () => {
    it('should get an assistant', async () => {
      // Mock initializeEnhancedVapi to return true
      const initializeSpy = jest.spyOn(global, 'initializeEnhancedVapi').mockResolvedValue(true);
      
      const result = await getAssistant('test-assistant-id');
      
      expect(result).toEqual({
        id: 'test-assistant-id',
        name: 'Test Assistant',
        firstMessage: 'Welcome to Test Law Firm',
        instructions: 'You are a legal assistant for Test Law Firm',
        voice: {
          provider: 'playht',
          voiceId: 'ranger'
        },
        llm: {
          provider: 'openai',
          model: 'gpt-4o'
        }
      });
      expect(initializeSpy).toHaveBeenCalled();
      expect(enhancedVapiMcpService.getAssistant).toHaveBeenCalledWith('test-assistant-id');
      
      // Clean up
      initializeSpy.mockRestore();
    });
  });
});
