/**
 * Assistant Manager Integration Utilities
 * 
 * This file provides utility functions to ensure consistent use of the vapiAssistantManager
 * throughout the application. It helps solve the inconsistent assistant creation issue.
 */

import { vapiAssistantManager } from '../services/vapiAssistantManager';
import { mcpConfig } from '../config/mcp.config';

// Keep track of initialization state
let isInitialized = false;
let initializationPromise = null;

/**
 * Initialize the Vapi assistant manager if not already initialized
 * @returns {Promise<boolean>} - Whether initialization was successful
 */
export const ensureManagerInitialized = async () => {
  // Return existing promise if initialization is in progress
  if (initializationPromise) {
    return initializationPromise;
  }
  
  // Return immediately if already initialized
  if (isInitialized) {
    return true;
  }
  
  // Start initialization
  initializationPromise = (async () => {
    try {
      console.log('[AssistantManagerIntegration] Initializing Vapi assistant manager');
      
      // Get API key from config
      const apiKey = mcpConfig.voice.vapi.secretKey || mcpConfig.voice.vapi.publicKey;
      
      // Initialize with direct API if key is provided
      if (apiKey) {
        const result = await vapiAssistantManager.initialize({
          directApiKey: apiKey
        });
        isInitialized = result;
        return result;
      }
      
      // Otherwise use MCP
      const result = await vapiAssistantManager.initialize({
        mcpUrl: mcpConfig.voice.vapi.mcpUrl
      });
      isInitialized = result;
      return result;
    } catch (error) {
      console.error('[AssistantManagerIntegration] Error initializing Vapi assistant manager:', error);
      isInitialized = false;
      return false;
    } finally {
      initializationPromise = null;
    }
  })();
  
  return initializationPromise;
};

/**
 * Ensure an attorney has a valid Vapi assistant ID
 * @param {Object} attorney - The attorney object from Supabase
 * @returns {Promise<Object>} - The updated attorney object
 */
export const ensureAttorneyHasAssistant = async (attorney) => {
  if (!attorney) {
    throw new Error('Attorney object is required');
  }
  
  // Initialize manager if needed
  await ensureManagerInitialized();
  
  // Ensure attorney has a valid assistant ID
  return await vapiAssistantManager.ensureAssistant(attorney);
};

/**
 * Sync an attorney's Vapi assistant with the latest data from Supabase
 * @param {Object} attorney - The attorney object from Supabase
 * @returns {Promise<Object>} - The sync result
 */
export const syncAttorneyAssistant = async (attorney) => {
  if (!attorney) {
    throw new Error('Attorney object is required');
  }
  
  // Initialize manager if needed
  await ensureManagerInitialized();
  
  // Sync assistant
  return await vapiAssistantManager.syncAssistant(attorney);
};

/**
 * Create a new Vapi assistant for an attorney
 * @param {Object} attorney - The attorney object from Supabase
 * @returns {Promise<Object>} - The created assistant
 */
export const createAssistantForAttorney = async (attorney) => {
  if (!attorney) {
    throw new Error('Attorney object is required');
  }
  
  // Initialize manager if needed
  await ensureManagerInitialized();
  
  // Create assistant
  const assistant = await vapiAssistantManager.createAssistant(attorney);
  
  // Update attorney record with new assistant ID
  return await vapiAssistantManager.updateAttorneyAssistantId(attorney.id, assistant.id);
};

/**
 * Verify that a Vapi assistant exists
 * @param {string} assistantId - The Vapi assistant ID to verify
 * @returns {Promise<boolean>} - Whether the assistant exists
 */
export const verifyAssistantExists = async (assistantId) => {
  if (!assistantId) {
    return false;
  }
  
  // Initialize manager if needed
  await ensureManagerInitialized();
  
  // Verify assistant
  return await vapiAssistantManager.verifyAssistant(assistantId);
};

/**
 * Get assistant configuration for an attorney
 * @param {Object} attorney - The attorney object from Supabase
 * @returns {Object} - The assistant configuration
 */
export const getAssistantConfig = (attorney) => {
  if (!attorney) {
    throw new Error('Attorney object is required');
  }
  
  return vapiAssistantManager.createAssistantConfig(attorney);
};
