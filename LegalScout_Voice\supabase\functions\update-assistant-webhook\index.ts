import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface UpdateWebhookRequest {
  assistantId: string;
  webhookUrl: string;
  attorneyId: string;
  subdomain: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🔗 Update Assistant Webhook function called');

    // Parse the request body
    const { assistantId, webhookUrl, attorneyId, subdomain }: UpdateWebhookRequest = await req.json();

    console.log('📝 Request details:', {
      assistantId,
      webhookUrl,
      attorneyId,
      subdomain
    });

    // Validate required fields
    if (!assistantId || !webhookUrl) {
      throw new Error('Missing required fields: assistantId and webhookUrl are required');
    }

    // Get Vapi private key from environment
    const vapiPrivateKey = Deno.env.get('VAPI_PRIVATE_KEY');
    if (!vapiPrivateKey) {
      throw new Error('VAPI_PRIVATE_KEY environment variable not set');
    }

    // Get webhook secret from environment
    const webhookSecret = Deno.env.get('VAPI_WEBHOOK_SECRET') || 'legalscout-webhook-secret';

    console.log('🔄 Updating Vapi assistant webhook URL...');

    // Update the Vapi assistant with new webhook URL
    const vapiResponse = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${vapiPrivateKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        serverUrl: webhookUrl,
        serverUrlSecret: webhookSecret
      })
    });

    if (!vapiResponse.ok) {
      const errorText = await vapiResponse.text();
      throw new Error(`Vapi API error: ${vapiResponse.status} - ${errorText}`);
    }

    const updatedAssistant = await vapiResponse.json();
    console.log('✅ Successfully updated assistant webhook URL');

    // Log the update in Supabase for audit trail
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Update the attorney record with a timestamp of the last webhook sync
    await supabase
      .from('attorneys')
      .update({ 
        webhook_last_synced: new Date().toISOString(),
        webhook_url: webhookUrl
      })
      .eq('id', attorneyId);

    console.log('📝 Updated attorney record with webhook sync timestamp');

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Assistant webhook URL updated successfully',
        assistantId,
        webhookUrl,
        updatedAt: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('❌ Error updating assistant webhook URL:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})

/* To deploy this function:
1. Make sure you have the Supabase CLI installed
2. Run: supabase functions deploy update-assistant-webhook
3. Set the required environment variables:
   - VAPI_PRIVATE_KEY
   - VAPI_WEBHOOK_SECRET
   - SUPABASE_URL
   - SUPABASE_SERVICE_ROLE_KEY
*/
