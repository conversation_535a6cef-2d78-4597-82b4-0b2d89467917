/**
 * MVP Assistant Compatibility Service
 * 
 * Provides a compatibility layer that supports both:
 * - Legacy: attorney.vapi_assistant_id (single assistant)
 * - Future: Multiple assistants via assistant_subdomains table
 * 
 * This ensures we don't break existing functionality while building toward
 * the full multi-assistant architecture.
 */

import { getSupabaseClient } from '../lib/supabase-fixed';
import { createVapiAssistant, updateVapiAssistant } from './syncHelpers';
import { assistantSubdomainService } from './assistantSubdomainService';

class MVPAssistantCompatibilityService {
  constructor() {
    this.initialized = false;
    this.supabase = null;
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      this.supabase = await getSupabaseClient();
      this.initialized = true;
      console.log('✅ [MVPCompatibility] Initialized successfully');
    } catch (error) {
      console.error('❌ [MVPCompatibility] Initialization failed:', error);
      throw error;
    }
  }

  /**
   * Get attorney's current assistant (supports both patterns)
   * Priority: current_assistant_id > vapi_assistant_id > first available assistant
   */
  async getCurrentAssistant(attorneyData) {
    await this.initialize();

    try {
      // 1. Try current_assistant_id (new pattern)
      if (attorneyData.current_assistant_id) {
        const assistant = await this.getAssistantDetails(attorneyData.current_assistant_id);
        if (assistant) {
          console.log('✅ [MVPCompatibility] Using current_assistant_id:', attorneyData.current_assistant_id);
          return assistant;
        }
      }

      // 2. Try vapi_assistant_id (legacy pattern)
      if (attorneyData.vapi_assistant_id && !attorneyData.vapi_assistant_id.includes('MOCK')) {
        const assistant = await this.getAssistantDetails(attorneyData.vapi_assistant_id);
        if (assistant) {
          console.log('✅ [MVPCompatibility] Using vapi_assistant_id:', attorneyData.vapi_assistant_id);
          return assistant;
        }
      }

      // 3. Try first available assistant from assistant_subdomains
      const availableAssistants = await assistantSubdomainService.getAttorneyAssistants(attorneyData.id);
      if (availableAssistants.length > 0) {
        const firstAssistant = availableAssistants[0];
        console.log('✅ [MVPCompatibility] Using first available assistant:', firstAssistant.assistant_id);
        return await this.getAssistantDetails(firstAssistant.assistant_id);
      }

      console.log('⚠️ [MVPCompatibility] No valid assistant found for attorney:', attorneyData.id);
      return null;

    } catch (error) {
      console.error('❌ [MVPCompatibility] Error getting current assistant:', error);
      return null;
    }
  }

  /**
   * Create assistant with dual-pattern support
   * Creates in both legacy and new patterns for compatibility
   */
  async createAssistantWithCompatibility(attorneyData, subdomainPreference = null) {
    await this.initialize();

    try {
      console.log('🚀 [MVPCompatibility] Creating assistant with dual-pattern support');

      // 1. Create assistant in Vapi
      const vapiAssistant = await createVapiAssistant({
        name: `${attorneyData.firm_name || 'LegalScout'} Assistant`,
        firstMessage: attorneyData.welcome_message || `Hello, I'm Scout from ${attorneyData.firm_name}. How can I help you today?`,
        instructions: attorneyData.vapi_instructions || 'You are a helpful legal assistant.',
        voice: {
          provider: '11labs',
          voiceId: attorneyData.voice_id || 'sarah'
        },
        llm: {
          provider: attorneyData.ai_model?.includes('gpt') ? 'openai' : 'anthropic',
          model: attorneyData.ai_model || 'gpt-4o'
        }
      });

      console.log('✅ [MVPCompatibility] Created Vapi assistant:', vapiAssistant.id);

      // 2. Update attorney record (LEGACY PATTERN - for existing code compatibility)
      const legacyUpdate = {
        vapi_assistant_id: vapiAssistant.id,
        current_assistant_id: vapiAssistant.id, // Also set current for new pattern
        updated_at: new Date().toISOString()
      };

      const { error: updateError } = await this.supabase
        .from('attorneys')
        .update(legacyUpdate)
        .eq('id', attorneyData.id);

      if (updateError) {
        console.error('❌ [MVPCompatibility] Failed to update attorney record:', updateError);
        // Don't throw - continue with subdomain assignment
      } else {
        console.log('✅ [MVPCompatibility] Updated attorney record (legacy pattern)');
      }

      // 3. Create subdomain mapping (NEW PATTERN - for future compatibility)
      const subdomain = subdomainPreference || attorneyData.subdomain || this.generateSubdomain(attorneyData);
      
      try {
        const subdomainMapping = await assistantSubdomainService.assignSubdomainToAssistant(
          vapiAssistant.id,
          subdomain,
          attorneyData.id,
          true // Primary assistant
        );
        console.log('✅ [MVPCompatibility] Created subdomain mapping (new pattern)');
      } catch (subdomainError) {
        console.warn('⚠️ [MVPCompatibility] Subdomain assignment failed (non-critical):', subdomainError.message);
        // Don't throw - legacy pattern still works
      }

      return {
        success: true,
        assistant: vapiAssistant,
        subdomain: subdomain,
        compatibilityMode: 'dual-pattern',
        message: `Assistant created with dual-pattern support`
      };

    } catch (error) {
      console.error('❌ [MVPCompatibility] Failed to create assistant:', error);
      throw error;
    }
  }

  /**
   * Ensure attorney has a working assistant (MVP-safe)
   * Uses existing logic but adds compatibility layer
   */
  async ensureWorkingAssistant(attorneyData) {
    await this.initialize();

    try {
      // Check if attorney already has a working assistant
      const currentAssistant = await this.getCurrentAssistant(attorneyData);
      
      if (currentAssistant) {
        console.log('✅ [MVPCompatibility] Attorney has working assistant:', currentAssistant.id);
        
        // Ensure subdomain mapping exists (for future compatibility)
        await this.ensureSubdomainMapping(attorneyData, currentAssistant.id);
        
        return {
          success: true,
          assistant: currentAssistant,
          action: 'existing',
          message: 'Using existing working assistant'
        };
      }

      // No working assistant - create one
      console.log('🔧 [MVPCompatibility] No working assistant found, creating new one');
      return await this.createAssistantWithCompatibility(attorneyData);

    } catch (error) {
      console.error('❌ [MVPCompatibility] Failed to ensure working assistant:', error);
      throw error;
    }
  }

  /**
   * Ensure subdomain mapping exists (for future compatibility)
   */
  async ensureSubdomainMapping(attorneyData, assistantId) {
    try {
      const existingMapping = await assistantSubdomainService.getAssistantBySubdomain(attorneyData.subdomain);
      
      if (!existingMapping || existingMapping.assistant_id !== assistantId) {
        await assistantSubdomainService.assignSubdomainToAssistant(
          assistantId,
          attorneyData.subdomain,
          attorneyData.id,
          true
        );
        console.log('✅ [MVPCompatibility] Ensured subdomain mapping exists');
      }
    } catch (error) {
      console.warn('⚠️ [MVPCompatibility] Could not ensure subdomain mapping (non-critical):', error.message);
    }
  }

  /**
   * Get assistant details from Vapi
   */
  async getAssistantDetails(assistantId) {
    try {
      // Use existing Vapi services to get assistant details
      const { vapiMcpService } = await import('./vapiMcpService');
      const assistants = await vapiMcpService.listAssistants();
      const assistant = assistants.find(a => a.id === assistantId);

      if (assistant) {
        return assistant;
      }

      // Fallback: return basic structure if not found in list
      return {
        id: assistantId,
        name: 'Assistant',
        // Add other assistant details as needed
      };
    } catch (error) {
      console.error('❌ [MVPCompatibility] Failed to get assistant details:', error);
      return {
        id: assistantId,
        name: 'Assistant (Details Unavailable)',
      };
    }
  }

  /**
   * MVP-Safe assistant creation that integrates with existing services
   */
  async createAssistantMVPSafe(attorneyData) {
    try {
      // Use existing vapiAssistantService for proven assistant creation
      const { vapiAssistantService } = await import('./vapiAssistantService');

      const result = await vapiAssistantService.ensureAssistantForAttorney(attorneyData);

      if (result.success) {
        // Ensure subdomain mapping exists for future compatibility
        await this.ensureSubdomainMapping(attorneyData, result.assistantId);

        return {
          success: true,
          assistant: { id: result.assistantId },
          action: result.action,
          message: result.message,
          compatibilityMode: 'mvp-safe'
        };
      }

      throw new Error(result.message || 'Failed to create assistant');
    } catch (error) {
      console.error('❌ [MVPCompatibility] MVP-safe creation failed:', error);
      throw error;
    }
  }

  /**
   * Generate subdomain from attorney data
   */
  generateSubdomain(attorneyData) {
    if (attorneyData.subdomain) return attorneyData.subdomain;
    
    const email = attorneyData.email || attorneyData.email_from_oauth || '';
    const firmName = attorneyData.firm_name || '';
    
    let subdomain = '';
    
    if (email) {
      subdomain = email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '');
    } else if (firmName) {
      subdomain = firmName.toLowerCase().replace(/[^a-z0-9]/g, '').substring(0, 20);
    } else {
      subdomain = `attorney${Date.now()}`;
    }

    return subdomain;
  }
}

// Export singleton instance
export const mvpAssistantCompatibilityService = new MVPAssistantCompatibilityService();
