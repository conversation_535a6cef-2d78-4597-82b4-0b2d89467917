/**
 * Assistant Sync Manager - Core synchronization coordinator
 * Manages data synchronization across all LegalScout dashboard components
 */

import { AssistantDataService } from './assistantDataService';

class AssistantSyncManager {
  constructor() {
    this.subscribers = new Set();
    this.currentAssistantId = null;
    this.currentAttorneyId = null;
    this.syncInProgress = false;
    this.lastSyncTime = null;
    this.realtimeSubscription = null;
    
    this.initializeRealtimeSubscriptions();
  }

  subscribe(callback) {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  notifySubscribers(eventType, data) {
    console.log(`📡 [AssistantSyncManager] Notifying ${this.subscribers.size} subscribers:`, eventType);
    this.subscribers.forEach(callback => {
      try {
        callback(eventType, data);
      } catch (error) {
        console.error('❌ [AssistantSyncManager] Error in subscriber callback:', error);
      }
    });
  }

  async initializeRealtimeSubscriptions() {
    try {
      const { supabase } = await import('../lib/supabase');
      
      this.realtimeSubscription = supabase
        .channel('assistant_sync')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'assistant_ui_configs' },
          (payload) => this.handleRealtimeUpdate('ui_config', payload)
        )
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'assistant_subdomains' },
          (payload) => this.handleRealtimeUpdate('subdomain', payload)
        )
        .subscribe();
        
      console.log('✅ [AssistantSyncManager] Real-time subscriptions initialized');
    } catch (error) {
      console.error('❌ [AssistantSyncManager] Error initializing real-time subscriptions:', error);
    }
  }

  handleRealtimeUpdate(dataType, payload) {
    if (this.doesUpdateAffectCurrentAssistant(payload)) {
      this.notifySubscribers('realtime_update', {
        dataType,
        payload,
        assistantId: this.currentAssistantId,
        timestamp: new Date().toISOString()
      });
    }
  }

  doesUpdateAffectCurrentAssistant(payload) {
    if (!this.currentAssistantId || !payload.new) return false;
    return payload.new.assistant_id === this.currentAssistantId ||
           payload.new.current_assistant_id === this.currentAssistantId ||
           payload.new.vapi_assistant_id === this.currentAssistantId;
  }

  setCurrentAssistant(assistantId, attorneyId) {
    const previousAssistantId = this.currentAssistantId;
    this.currentAssistantId = assistantId;
    this.currentAttorneyId = attorneyId;
    
    if (previousAssistantId !== assistantId) {
      this.notifySubscribers('assistant_changed', {
        previousAssistantId,
        currentAssistantId: assistantId,
        attorneyId,
        timestamp: new Date().toISOString()
      });
    }
  }

  async synchronizeAssistantSelection(attorneyId, assistantId) {
    if (this.syncInProgress) return;

    try {
      this.syncInProgress = true;
      this.setCurrentAssistant(assistantId, attorneyId);

      // Use existing AssistantDataService if available
      if (AssistantDataService.synchronizeAssistantSelection) {
        const result = await AssistantDataService.synchronizeAssistantSelection(attorneyId, assistantId);
        this.lastSyncTime = new Date().toISOString();
        
        this.notifySubscribers('sync_completed', {
          assistantId, attorneyId, result,
          timestamp: this.lastSyncTime
        });
        
        return result;
      } else {
        // Fallback to basic notification
        this.notifySubscribers('assistant_selected', {
          assistantId, attorneyId,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      this.notifySubscribers('sync_error', {
        error: error.message, assistantId, attorneyId,
        timestamp: new Date().toISOString()
      });
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  getSyncStatus() {
    return {
      currentAssistantId: this.currentAssistantId,
      currentAttorneyId: this.currentAttorneyId,
      syncInProgress: this.syncInProgress,
      lastSyncTime: this.lastSyncTime,
      subscriberCount: this.subscribers.size,
      hasRealtimeSubscription: !!this.realtimeSubscription
    };
  }
}

const assistantSyncManager = new AssistantSyncManager();
export { assistantSyncManager };
export default assistantSyncManager;
