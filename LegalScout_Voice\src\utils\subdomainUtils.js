/**
 * Subdomain Utilities
 * 
 * This file contains utility functions for working with subdomains
 * and loading subdomain configurations.
 */

import { getAttorneyConfigAsync } from '../config/attorneys';
import { getCurrentSubdomain } from './subdomainTester';

/**
 * Get the configuration for a subdomain
 * 
 * @param {string} subdomain - The subdomain to get the configuration for
 * @returns {Promise<Object>} - The subdomain configuration
 */
export const getSubdomainConfig = async (subdomain) => {
  try {
    // If no subdomain provided, get the current one
    const subdomainToUse = subdomain || getCurrentSubdomain();
    
    // Get the attorney configuration for the subdomain
    const config = await getAttorneyConfigAsync(subdomainToUse);
    
    return {
      subdomain: subdomainToUse,
      firmName: config.firmName,
      logo: config.logo,
      mascot: config.mascot,
      vapiInstructions: config.vapiInstructions,
      vapiContext: config.vapiContext,
      practiceArea: config.practiceAreas?.[0] || null,
      practiceAreas: config.practiceAreas || [],
      interactionDepositUrl: config.interactionDepositUrl || null
    };
  } catch (error) {
    console.error('Error getting subdomain config:', error);
    return null;
  }
};

/**
 * Check if a subdomain exists
 * 
 * @param {string} subdomain - The subdomain to check
 * @returns {Promise<boolean>} - Whether the subdomain exists
 */
export const subdomainExists = async (subdomain) => {
  if (!subdomain) return false;
  
  try {
    const config = await getSubdomainConfig(subdomain);
    return !!config;
  } catch (error) {
    console.error('Error checking if subdomain exists:', error);
    return false;
  }
};
