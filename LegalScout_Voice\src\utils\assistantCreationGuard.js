/**
 * Assistant Creation Guard
 * 
 * This utility prevents duplicate Vapi assistant creation by implementing
 * strict checks and guards throughout the application.
 */

import { supabase } from '../lib/supabase';

// Global state to track assistant creation attempts
const creationAttempts = new Map();
const CREATION_COOLDOWN = 30000; // 30 seconds

/**
 * Check if an attorney already has a valid assistant
 * @param {string} attorneyId - The attorney ID
 * @param {string} email - The attorney email
 * @returns {Promise<Object>} - Result with hasAssistant and assistantId
 */
export async function checkExistingAssistant(attorneyId, email) {
  try {
    console.log('[AssistantGuard] Checking existing assistant for:', { attorneyId, email });

    // First check by attorney ID
    if (attorneyId) {
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('vapi_assistant_id, firm_name')
        .eq('id', attorneyId)
        .single();

      if (!error && attorney?.vapi_assistant_id && !attorney.vapi_assistant_id.includes('mock')) {
        console.log('[AssistantG<PERSON>] ✅ Found existing assistant by ID:', attorney.vapi_assistant_id);
        return {
          hasAssistant: true,
          assistantId: attorney.vapi_assistant_id,
          source: 'attorney_id'
        };
      }
    }

    // Fallback check by email
    if (email) {
      const { data: attorneys, error } = await supabase
        .from('attorneys')
        .select('vapi_assistant_id, firm_name, id')
        .eq('email', email)
        .order('updated_at', { ascending: false });

      if (!error && attorneys?.length > 0) {
        // Find attorney with valid assistant ID
        const attorneyWithAssistant = attorneys.find(a => 
          a.vapi_assistant_id && 
          !a.vapi_assistant_id.includes('mock') &&
          !a.vapi_assistant_id.includes('duplicate')
        );

        if (attorneyWithAssistant) {
          console.log('[AssistantGuard] ✅ Found existing assistant by email:', attorneyWithAssistant.vapi_assistant_id);
          return {
            hasAssistant: true,
            assistantId: attorneyWithAssistant.vapi_assistant_id,
            source: 'email',
            attorneyId: attorneyWithAssistant.id
          };
        }
      }
    }

    console.log('[AssistantGuard] ❌ No existing assistant found');
    return {
      hasAssistant: false,
      assistantId: null,
      source: null
    };

  } catch (error) {
    console.error('[AssistantGuard] Error checking existing assistant:', error);
    return {
      hasAssistant: false,
      assistantId: null,
      source: null,
      error: error.message
    };
  }
}

/**
 * Guard against duplicate assistant creation
 * @param {string} attorneyId - The attorney ID
 * @param {string} email - The attorney email
 * @returns {Promise<Object>} - Result with canCreate and reason
 */
export async function guardAssistantCreation(attorneyId, email) {
  const guardKey = attorneyId || email;
  
  if (!guardKey) {
    return {
      canCreate: false,
      reason: 'No attorney ID or email provided',
      code: 'MISSING_IDENTIFIER'
    };
  }

  // Check cooldown period
  const lastAttempt = creationAttempts.get(guardKey);
  const now = Date.now();
  
  if (lastAttempt && (now - lastAttempt) < CREATION_COOLDOWN) {
    const remainingTime = Math.ceil((CREATION_COOLDOWN - (now - lastAttempt)) / 1000);
    return {
      canCreate: false,
      reason: `Creation attempt too recent. Wait ${remainingTime} seconds.`,
      code: 'COOLDOWN_ACTIVE'
    };
  }

  // Check if assistant already exists
  const existingCheck = await checkExistingAssistant(attorneyId, email);
  
  if (existingCheck.hasAssistant) {
    return {
      canCreate: false,
      reason: `Assistant already exists: ${existingCheck.assistantId}`,
      code: 'ASSISTANT_EXISTS',
      existingAssistantId: existingCheck.assistantId
    };
  }

  // Record this attempt
  creationAttempts.set(guardKey, now);

  return {
    canCreate: true,
    reason: 'Creation allowed',
    code: 'CREATION_ALLOWED'
  };
}

/**
 * Verify assistant exists in Vapi
 * @param {string} assistantId - The assistant ID to verify
 * @returns {Promise<boolean>} - True if assistant exists
 */
export async function verifyAssistantInVapi(assistantId) {
  try {
    // Use public key for verification
    const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
    
    if (!publicKey) {
      console.warn('[AssistantGuard] No Vapi public key available for verification');
      return false;
    }

    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      headers: {
        'Authorization': `Bearer ${publicKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      console.log('[AssistantGuard] ✅ Assistant verified in Vapi:', assistantId);
      return true;
    } else {
      console.log('[AssistantGuard] ❌ Assistant not found in Vapi:', assistantId);
      return false;
    }

  } catch (error) {
    console.error('[AssistantGuard] Error verifying assistant in Vapi:', error);
    return false;
  }
}

/**
 * Safe assistant creation wrapper
 * @param {Function} createFunction - The actual creation function
 * @param {Object} attorneyData - Attorney data
 * @returns {Promise<Object>} - Creation result
 */
export async function safeCreateAssistant(createFunction, attorneyData) {
  const { id: attorneyId, email } = attorneyData;

  console.log('[AssistantGuard] Safe creation attempt for:', { attorneyId, email });

  // Guard check
  const guardResult = await guardAssistantCreation(attorneyId, email);
  
  if (!guardResult.canCreate) {
    console.log('[AssistantGuard] 🛑 Creation blocked:', guardResult.reason);
    
    if (guardResult.code === 'ASSISTANT_EXISTS') {
      return {
        success: true,
        assistantId: guardResult.existingAssistantId,
        action: 'existing',
        message: 'Using existing assistant'
      };
    }
    
    return {
      success: false,
      error: guardResult.reason,
      code: guardResult.code
    };
  }

  try {
    console.log('[AssistantGuard] ✅ Proceeding with assistant creation...');
    const result = await createFunction(attorneyData);
    
    console.log('[AssistantGuard] ✅ Assistant created successfully:', result.id);
    return {
      success: true,
      assistantId: result.id,
      action: 'created',
      message: 'New assistant created'
    };

  } catch (error) {
    console.error('[AssistantGuard] ❌ Assistant creation failed:', error);
    
    // Clear the attempt record on failure so retry is possible
    const guardKey = attorneyId || email;
    creationAttempts.delete(guardKey);
    
    return {
      success: false,
      error: error.message,
      code: 'CREATION_FAILED'
    };
  }
}

/**
 * Clear creation attempt history (for testing)
 */
export function clearCreationHistory() {
  creationAttempts.clear();
  console.log('[AssistantGuard] Creation history cleared');
}

/**
 * Get creation attempt status
 * @param {string} guardKey - Attorney ID or email
 * @returns {Object} - Status information
 */
export function getCreationStatus(guardKey) {
  const lastAttempt = creationAttempts.get(guardKey);
  const now = Date.now();
  
  if (!lastAttempt) {
    return {
      hasAttempt: false,
      canCreate: true
    };
  }
  
  const timeSinceAttempt = now - lastAttempt;
  const canCreate = timeSinceAttempt >= CREATION_COOLDOWN;
  
  return {
    hasAttempt: true,
    lastAttempt: new Date(lastAttempt).toISOString(),
    timeSinceAttempt,
    canCreate,
    remainingCooldown: canCreate ? 0 : CREATION_COOLDOWN - timeSinceAttempt
  };
}
