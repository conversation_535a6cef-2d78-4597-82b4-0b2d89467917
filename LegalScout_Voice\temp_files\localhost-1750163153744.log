App.jsx:389 🔥 [App.jsx] App component is starting!
App.jsx:395 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/', isAuthenticated: true, hasSession: true}
App.jsx:456 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
App.jsx:463 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.491Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.491Z'}
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 Attorney object in ProfileTab: null
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
 ✅ Using OAuth user email: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
 [AutoReconciler] 🚀 Initializing auto-reconciliation
 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701', timestamp: '2025-06-17T12:25:21.554Z'}
 [DashboardNew] ✅ User authenticated: <EMAIL>
 [DashboardNew] 🔄 Loading attorney for user...
 [DashboardNew] Fetch Attorney Effect triggered.
 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
 [DashboardNew] fetchAttorneyData called.
 [DashboardNew] Attempting to load attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
DashboardNew.jsx:342 [DashboardNew] 🛡️ Checking for robust state handler...
DashboardNew.jsx:380 [DashboardNew] ⚠️ Robust state handler not available (window.resolveAttorneyState is not a function)
fetchAttorneyData @ DashboardNew.jsx:380
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:384 [DashboardNew] 🔄 Using fallback attorney loading logic...
standaloneAttorneyManagerFix.js?t=1750163096374:76 [StandaloneAttorneyManagerFix] Loading attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
AttorneyProfileManager.js:350 [AttorneyProfileManager] Loading attorney by userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701
AttorneyProfileManager.js:367 [AttorneyProfileManager] Error loading attorney by userId: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js?t=1750163096374:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=9711cfb6:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=9711cfb6:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=9711cfb6:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=9711cfb6:18137:11)
loadAttorneyByUserId @ AttorneyProfileManager.js:367
loadAttorneyForUser @ standaloneAttorneyManagerFix.js?t=1750163096374:79
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
ProfileTab.jsx:57 Attorney object in ProfileTab: null
ProfileTab.jsx:58 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
standaloneAttorneyManagerFix.js?t=1750163096374:32 [StandaloneAttorneyManagerFix] Adding subscriber
DashboardNew.jsx:145 [DashboardNew] 🚀 Initializing auto-reconciler for assistant conflicts
AutoAssistantReconciler.js:276 [AutoReconciler] 🚀 Initializing auto-reconciliation
DashboardNew.jsx:185 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
DashboardNew.jsx:186 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701', timestamp: '2025-06-17T12:25:21.558Z'}
DashboardNew.jsx:211 [DashboardNew] ✅ User authenticated: <EMAIL>
DashboardNew.jsx:226 [DashboardNew] 🔄 Loading attorney for user...
DashboardNew.jsx:238 [DashboardNew] Fetch Attorney Effect triggered.
DashboardNew.jsx:239 [DashboardNew] Dependencies: user?.id=571390ac-5a83-46b2-ad3a-18b9cf39d701, authIsLoading=false, loadAttorneyForUser exists=true
DashboardNew.jsx:487 [DashboardNew] Auth resolved, user ID present, and manager ready. Calling fetchAttorneyData.
DashboardNew.jsx:263 [DashboardNew] fetchAttorneyData called.
DashboardNew.jsx:334 [DashboardNew] Attempting to load attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
DashboardNew.jsx:339 [DashboardNew] Loading attorney for authenticated user: <EMAIL>
DashboardNew.jsx:342 [DashboardNew] 🛡️ Checking for robust state handler...
DashboardNew.jsx:380 [DashboardNew] ⚠️ Robust state handler not available (window.resolveAttorneyState is not a function)
fetchAttorneyData @ DashboardNew.jsx:380
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:384 [DashboardNew] 🔄 Using fallback attorney loading logic...
standaloneAttorneyManagerFix.js?t=1750163096374:76 [StandaloneAttorneyManagerFix] Loading attorney for user: 571390ac-5a83-46b2-ad3a-18b9cf39d701
AttorneyProfileManager.js:350 [AttorneyProfileManager] Loading attorney by userId: 571390ac-5a83-46b2-ad3a-18b9cf39d701
AttorneyProfileManager.js:367 [AttorneyProfileManager] Error loading attorney by userId: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js?t=1750163096374:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at invokePassiveEffectMountInDEV (chunk-Q72EVS5P.js?v=9711cfb6:18352:19)
    at invokeEffectsInDev (chunk-Q72EVS5P.js?v=9711cfb6:19729:19)
    at commitDoubleInvokeEffectsInDEV (chunk-Q72EVS5P.js?v=9711cfb6:19714:15)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=9711cfb6:19531:13)
loadAttorneyByUserId @ AttorneyProfileManager.js:367
loadAttorneyForUser @ standaloneAttorneyManagerFix.js?t=1750163096374:79
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
standaloneAttorneyManagerFix.js?t=1750163096374:91 [StandaloneAttorneyManagerFix] Error loading attorney: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js?t=1750163096374:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=9711cfb6:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=9711cfb6:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=9711cfb6:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=9711cfb6:18137:11)
loadAttorneyForUser @ standaloneAttorneyManagerFix.js?t=1750163096374:91
await in loadAttorneyForUser
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
standaloneAttorneyManagerFix.js?t=1750163096374:91 [StandaloneAttorneyManagerFix] Error loading attorney: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js?t=1750163096374:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at invokePassiveEffectMountInDEV (chunk-Q72EVS5P.js?v=9711cfb6:18352:19)
    at invokeEffectsInDev (chunk-Q72EVS5P.js?v=9711cfb6:19729:19)
    at commitDoubleInvokeEffectsInDEV (chunk-Q72EVS5P.js?v=9711cfb6:19714:15)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=9711cfb6:19531:13)
loadAttorneyForUser @ standaloneAttorneyManagerFix.js?t=1750163096374:91
await in loadAttorneyForUser
(anonymous) @ useStandaloneAttorney.js:172
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
useStandaloneAttorney.js:178 [useStandaloneAttorney] Error loading attorney for user: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js?t=1750163096374:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=9711cfb6:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=9711cfb6:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=9711cfb6:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=9711cfb6:18137:11)
(anonymous) @ useStandaloneAttorney.js:178
await in (anonymous)
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
useStandaloneAttorney.js:178 [useStandaloneAttorney] Error loading attorney for user: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js?t=1750163096374:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at invokePassiveEffectMountInDEV (chunk-Q72EVS5P.js?v=9711cfb6:18352:19)
    at invokeEffectsInDev (chunk-Q72EVS5P.js?v=9711cfb6:19729:19)
    at commitDoubleInvokeEffectsInDEV (chunk-Q72EVS5P.js?v=9711cfb6:19714:15)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=9711cfb6:19531:13)
(anonymous) @ useStandaloneAttorney.js:178
await in (anonymous)
fetchAttorneyData @ DashboardNew.jsx:388
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:390 [DashboardNew] Error loading attorney by user ID: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js?t=1750163096374:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at commitPassiveMountOnFiber (chunk-Q72EVS5P.js?v=9711cfb6:18184:19)
    at commitPassiveMountEffects_complete (chunk-Q72EVS5P.js?v=9711cfb6:18157:17)
    at commitPassiveMountEffects_begin (chunk-Q72EVS5P.js?v=9711cfb6:18147:15)
    at commitPassiveMountEffects (chunk-Q72EVS5P.js?v=9711cfb6:18137:11)
fetchAttorneyData @ DashboardNew.jsx:390
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:396 [DashboardNew] No attorney found by user ID, trying email: <EMAIL>
DashboardNew.jsx:390 [DashboardNew] Error loading attorney by user ID: ReferenceError: supabase is not defined
    at AttorneyProfileManager.loadAttorneyByUserId (AttorneyProfileManager.js:351:31)
    at Object.loadAttorneyForUser (standaloneAttorneyManagerFix.js?t=1750163096374:79:62)
    at useStandaloneAttorney.js:172:44
    at fetchAttorneyData (DashboardNew.jsx:388:34)
    at DashboardNew.jsx:488:7
    at commitHookEffectListMount (chunk-Q72EVS5P.js?v=9711cfb6:16936:34)
    at invokePassiveEffectMountInDEV (chunk-Q72EVS5P.js?v=9711cfb6:18352:19)
    at invokeEffectsInDev (chunk-Q72EVS5P.js?v=9711cfb6:19729:19)
    at commitDoubleInvokeEffectsInDEV (chunk-Q72EVS5P.js?v=9711cfb6:19714:15)
    at flushPassiveEffectsImpl (chunk-Q72EVS5P.js?v=9711cfb6:19531:13)
fetchAttorneyData @ DashboardNew.jsx:390
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:488
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:396 [DashboardNew] No attorney found by user ID, trying email: <EMAIL>
VeryCoolAssistants.jsx:47 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
VeryCoolAssistants.jsx:47 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.569Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.570Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
ProfileTab.jsx:92 ✅ Using OAuth user email: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: undefined, …}
ProfileTab.jsx:92 ✅ Using OAuth user email: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 [VeryCoolAssistants] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] 🚀 Initializing dashboard with simplified logic...
 [DashboardNew] 🔍 DEBUG AUTH STATE: {authIsLoading: false, hasUser: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701', timestamp: '2025-06-17T12:25:21.598Z'}
 [DashboardNew] ✅ User authenticated: <EMAIL>
 [DashboardNew] ✅ Attorney already loaded: LegalScout
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.599Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.600Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.600Z'}
 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.601Z'}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [AssistantAwareContext] DIRECT assistant change: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [StandaloneAttorneyManagerFix] Notifying subscribers: 6
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
 [StandaloneAttorneyManagerFix] Notifying subscribers: 6
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
VeryCoolAssistants.jsx:76 📋 [VeryCoolAssistants] Loaded assistants from centralized service: (2) [{…}, {…}]
VeryCoolAssistants.jsx:47 ✅ [VeryCoolAssistants] Subscribed to centralized assistant data service
assistantDataService.js:234 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:109 🎯 [EnhancedAssistantDropdown] No current assistant selected, auto-selecting first: 2f157a27-067c-439e-823c-f0a2bbdd66e0
AssistantAwareContext.jsx:364 🎯 [AssistantAwareContext] DIRECT assistant change: 2f157a27-067c-439e-823c-f0a2bbdd66e0
standaloneAttorneyManagerFix.js?t=1750163096374:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 6
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
AssistantAwareContext.jsx:389 ✅ [AssistantAwareContext] Updated attorney manager with new assistant ID
standaloneAttorneyManagerFix.js?t=1750163096374:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 6
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.642Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.643Z'}
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T11:37:08.967722+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
DashboardNew.jsx:653 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:654 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.684Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.687Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.688Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:21.689Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
VM3451 simple-preview:4 🚀 [LegalScout] Initializing environment...
VM3451 simple-preview:26 ✅ [LegalScout] Environment initialized
VM3452 simple-preview:85 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
VM3453 simple-preview:84 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
VM3455 simple-preview:4 🚀 [LegalScout] Initializing environment...
VM3455 simple-preview:26 ✅ [LegalScout] Environment initialized
VM3456 simple-preview:85 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
VM3457 simple-preview:84 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
assistantDataService.js:234 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.1d3471b7-8694-4844-b3ef-e05720693efc 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.2f157a27-067c-439e-823c-f0a2bbdd66e0 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VeryCoolAssistants.jsx:124 Error loading stats for assistant 1d3471b7-8694-4844-b3ef-e05720693efc: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:124
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:95
loadAssistants @ VeryCoolAssistants.jsx:81
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
vapiAssistantUtils.js:169 Found attorney by email: 87756a2c-a398-43f2-889a-b8815684df71
vapiAssistantUtils.js:30 Attorney already has VAPI assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:402 [DashboardNew] Attorney found by email and VAPI assistant ensured: 87756a2c-a398-43f2-889a-b8815684df71
standaloneAttorneyManagerFix.js?t=1750163096374:124 [StandaloneAttorneyManagerFix] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
standaloneAttorneyManagerFix.js?t=1750163096374:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 6
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
DashboardNew.jsx:418 [DashboardNew] Attorney loaded successfully: 87756a2c-a398-43f2-889a-b8815684df71
VeryCoolAssistants.jsx:124 Error loading stats for assistant 2f157a27-067c-439e-823c-f0a2bbdd66e0: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:124
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:95
loadAssistants @ VeryCoolAssistants.jsx:81
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:22.583Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:22.584Z'}
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T12:25:11.287607+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
DashboardNew.jsx:653 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:654 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:22.707Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:22.708Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:22.709Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:22.711Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
assistantDataService.js:234 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
vapiAssistantUtils.js:169 Found attorney by email: 87756a2c-a398-43f2-889a-b8815684df71
vapiAssistantUtils.js:30 Attorney already has VAPI assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:402 [DashboardNew] Attorney found by email and VAPI assistant ensured: 87756a2c-a398-43f2-889a-b8815684df71
standaloneAttorneyManagerFix.js?t=1750163096374:124 [StandaloneAttorneyManagerFix] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
standaloneAttorneyManagerFix.js?t=1750163096374:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 6
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
DashboardNew.jsx:418 [DashboardNew] Attorney loaded successfully: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:23.155Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:23.156Z'}
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
useAssistantAwareCopy.js:25 🔍 [useAssistantAwareCopy] URLs updated: {shareUrl: 'https://damon-2.legalscout.net', currentAssistant: '87756a2c-a398-43f2-889a-b8815684df71', isAssistantSelected: true, assistantSubdomain: 'damon'}
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T12:25:11.287607+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
DashboardNew.jsx:653 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:654 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:23.277Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:23.279Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:23.279Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:23.333Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
assistantDataService.js:234 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
EnhancedAssistantDropdown.jsx:84 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:99 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
EnhancedAssistantDropdown.jsx:246 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3581 supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
VM3581 supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
VM3581 supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
VM3581 supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
VM3513 unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
VM3808 vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
VM3808 vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
VM3791 vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
VM3791 vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
VM3775 vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
VM3655 AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM3655 AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
VM3655 AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
VM3506 standaloneAttorneyManagerFix.js:8 🔧 [StandaloneAttorneyManagerFix] Initializing...
VM3506 standaloneAttorneyManagerFix.js:156 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
VM3506 standaloneAttorneyManagerFix.js:11 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
VM3506 standaloneAttorneyManagerFix.js:135 [StandaloneAttorneyManagerFix] Initialized with current attorney: 87756a2c-a398-43f2-889a-b8815684df71
VM3506 standaloneAttorneyManagerFix.js:143 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
VM3506 standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
VM3506 standaloneAttorneyManagerFix.js:151 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
VM3506 standaloneAttorneyManagerFix.js:171 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
VM3506 standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
VM3506 standaloneAttorneyManagerFix.js:195 ✅ [StandaloneAttorneyManagerFix] Service loaded
VM3465 main.jsx:23 🚀 [LegalScout] Starting React app...
VM3465 main.jsx:32 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
VM3465 main.jsx:45 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
VM3465 main.jsx:90 ✅ [LegalScout] React app rendered successfully
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:1587 [DashboardNew] Mobile preview iframe (modal) loaded, waiting for PREVIEW_READY message
VM3501 ProductionErrorBoundary.jsx:19 [ErrorBoundary] Creating React placeholder
VM3501 ProductionErrorBoundary.jsx:23 [ErrorBoundary] Adding createContext placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useState placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useEffect placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useLayoutEffect placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useRef placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useCallback placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useMemo placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding useContext placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding forwardRef placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding createElement placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding cloneElement placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding createRef placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding Component placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding PureComponent placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding Fragment placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding Children placeholder
VM3501 ProductionErrorBoundary.jsx:55 [ErrorBoundary] Adding isValidElement placeholder
VM3501 ProductionErrorBoundary.jsx:61 [ErrorBoundary] React polyfills applied
VM3501 ProductionErrorBoundary.jsx:61 [ErrorBoundary] React polyfills applied
VM3495 App.jsx:458 🔥 [App.jsx] App component is starting!
VM3495 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM3495 App.jsx:513 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM3495 App.jsx:519 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM3495 App.jsx:458 🔥 [App.jsx] App component is starting!
VM3495 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM3495 App.jsx:513 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM3495 App.jsx:519 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM3512 AssistantAwareContext.jsx:42 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ VM3512 AssistantAwareContext.jsx:42
mountMemo @ VM3484 chunk-Q72EVS5P.js:12214
useMemo @ VM3484 chunk-Q72EVS5P.js:12538
useMemo @ VM3479 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3512 AssistantAwareContext.jsx:37
renderWithHooks @ VM3484 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM3484 chunk-Q72EVS5P.js:14946
beginWork @ VM3484 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM3484 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3484 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3484 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3484 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3484 chunk-Q72EVS5P.js:18706
workLoop @ VM3484 chunk-Q72EVS5P.js:197
flushWork @ VM3484 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3484 chunk-Q72EVS5P.js:384
VM3512 AssistantAwareContext.jsx:43 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ VM3512 AssistantAwareContext.jsx:43
mountMemo @ VM3484 chunk-Q72EVS5P.js:12214
useMemo @ VM3484 chunk-Q72EVS5P.js:12538
useMemo @ VM3479 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3512 AssistantAwareContext.jsx:37
renderWithHooks @ VM3484 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM3484 chunk-Q72EVS5P.js:14946
beginWork @ VM3484 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM3484 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3484 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3484 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3484 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3484 chunk-Q72EVS5P.js:18706
workLoop @ VM3484 chunk-Q72EVS5P.js:197
flushWork @ VM3484 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3484 chunk-Q72EVS5P.js:384
VM3512 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
VM3512 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
VM3512 AssistantAwareContext.jsx:162 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
VM3512 AssistantAwareContext.jsx:42 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ VM3512 AssistantAwareContext.jsx:42
mountMemo @ VM3484 chunk-Q72EVS5P.js:12214
useMemo @ VM3484 chunk-Q72EVS5P.js:12538
useMemo @ VM3479 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3512 AssistantAwareContext.jsx:37
renderWithHooks @ VM3484 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM3484 chunk-Q72EVS5P.js:14996
beginWork @ VM3484 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM3484 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3484 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3484 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3484 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3484 chunk-Q72EVS5P.js:18706
workLoop @ VM3484 chunk-Q72EVS5P.js:197
flushWork @ VM3484 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3484 chunk-Q72EVS5P.js:384
VM3512 AssistantAwareContext.jsx:43 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ VM3512 AssistantAwareContext.jsx:43
mountMemo @ VM3484 chunk-Q72EVS5P.js:12214
useMemo @ VM3484 chunk-Q72EVS5P.js:12538
useMemo @ VM3479 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3512 AssistantAwareContext.jsx:37
renderWithHooks @ VM3484 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM3484 chunk-Q72EVS5P.js:14996
beginWork @ VM3484 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM3484 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3484 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3484 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3484 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3484 chunk-Q72EVS5P.js:18706
workLoop @ VM3484 chunk-Q72EVS5P.js:197
flushWork @ VM3484 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3484 chunk-Q72EVS5P.js:384
VM3512 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
VM3512 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
VM3512 AssistantAwareContext.jsx:162 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
VM3574 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM3574 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM3649 supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
VM3649 supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
VM3649 supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
VM3649 supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
VM3547 unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
VM3741 vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
VM3741 vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
VM3918 vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
VM3918 vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
VM3862 vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
VM3789 AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM3789 AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
VM3789 AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
VM3492 standaloneAttorneyManagerFix.js:8 🔧 [StandaloneAttorneyManagerFix] Initializing...
VM3492 standaloneAttorneyManagerFix.js:156 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
VM3492 standaloneAttorneyManagerFix.js:11 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
VM3492 standaloneAttorneyManagerFix.js:135 [StandaloneAttorneyManagerFix] Initialized with current attorney: 87756a2c-a398-43f2-889a-b8815684df71
VM3492 standaloneAttorneyManagerFix.js:143 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
VM3492 standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
VM3492 standaloneAttorneyManagerFix.js:151 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
VM3492 standaloneAttorneyManagerFix.js:171 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
VM3492 standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
VM3492 standaloneAttorneyManagerFix.js:195 ✅ [StandaloneAttorneyManagerFix] Service loaded
VM3459 main.jsx:23 🚀 [LegalScout] Starting React app...
VM3459 main.jsx:32 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
VM3459 main.jsx:45 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
VM3459 main.jsx:90 ✅ [LegalScout] React app rendered successfully
VM3808 vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
VM3808 vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
VM3538 SimplePreviewPage.jsx:82 SimplePreviewPage: Starting config load...
VM3538 SimplePreviewPage.jsx:83 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
VM3538 SimplePreviewPage.jsx:101 SimplePreviewPage: Loading assistant config for subdomain: damon
VM3538 SimplePreviewPage.jsx:61 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
VM3538 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM3584 useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
VM3584 useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
VM3506 standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
VM3512 AssistantAwareContext.jsx:59 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
VM3512 AssistantAwareContext.jsx:69 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
VM3495 App.jsx:630 🚀 [App] Initializing with safe subdomain detection...
VM3511 AuthContext.jsx:52 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
VM3538 SimplePreviewPage.jsx:82 SimplePreviewPage: Starting config load...
VM3538 SimplePreviewPage.jsx:83 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
VM3538 SimplePreviewPage.jsx:101 SimplePreviewPage: Loading assistant config for subdomain: damon
VM3538 SimplePreviewPage.jsx:61 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
VM3538 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM3584 useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
VM3584 useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
VM3506 standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
VM3512 AssistantAwareContext.jsx:59 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
VM3512 AssistantAwareContext.jsx:69 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
VM3495 App.jsx:630 🚀 [App] Initializing with safe subdomain detection...
VM3511 AuthContext.jsx:52 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 [DashboardNew] Mobile preview iframe (panel) loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] Creating React placeholder
 [ErrorBoundary] Adding createContext placeholder
 [ErrorBoundary] Adding useState placeholder
 [ErrorBoundary] Adding useEffect placeholder
 [ErrorBoundary] Adding useLayoutEffect placeholder
 [ErrorBoundary] Adding useRef placeholder
 [ErrorBoundary] Adding useCallback placeholder
 [ErrorBoundary] Adding useMemo placeholder
 [ErrorBoundary] Adding useContext placeholder
 [ErrorBoundary] Adding forwardRef placeholder
 [ErrorBoundary] Adding createElement placeholder
 [ErrorBoundary] Adding cloneElement placeholder
 [ErrorBoundary] Adding createRef placeholder
 [ErrorBoundary] Adding Component placeholder
 [ErrorBoundary] Adding PureComponent placeholder
 [ErrorBoundary] Adding Fragment placeholder
 [ErrorBoundary] Adding Children placeholder
 [ErrorBoundary] Adding isValidElement placeholder
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM3546 AssistantAwareContext.jsx:42 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ VM3546 AssistantAwareContext.jsx:42
mountMemo @ VM3498 chunk-Q72EVS5P.js:12214
useMemo @ VM3498 chunk-Q72EVS5P.js:12538
useMemo @ VM3493 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3546 AssistantAwareContext.jsx:37
renderWithHooks @ VM3498 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM3498 chunk-Q72EVS5P.js:14946
beginWork @ VM3498 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM3498 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3498 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3498 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3498 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3498 chunk-Q72EVS5P.js:18706
workLoop @ VM3498 chunk-Q72EVS5P.js:197
flushWork @ VM3498 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3498 chunk-Q72EVS5P.js:384
VM3546 AssistantAwareContext.jsx:43 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ VM3546 AssistantAwareContext.jsx:43
mountMemo @ VM3498 chunk-Q72EVS5P.js:12214
useMemo @ VM3498 chunk-Q72EVS5P.js:12538
useMemo @ VM3493 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3546 AssistantAwareContext.jsx:37
renderWithHooks @ VM3498 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM3498 chunk-Q72EVS5P.js:14946
beginWork @ VM3498 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM3498 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3498 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3498 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3498 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3498 chunk-Q72EVS5P.js:18706
workLoop @ VM3498 chunk-Q72EVS5P.js:197
flushWork @ VM3498 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3498 chunk-Q72EVS5P.js:384
VM3546 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
VM3546 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
VM3546 AssistantAwareContext.jsx:162 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
VM3546 AssistantAwareContext.jsx:42 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ VM3546 AssistantAwareContext.jsx:42
mountMemo @ VM3498 chunk-Q72EVS5P.js:12214
useMemo @ VM3498 chunk-Q72EVS5P.js:12538
useMemo @ VM3493 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3546 AssistantAwareContext.jsx:37
renderWithHooks @ VM3498 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM3498 chunk-Q72EVS5P.js:14996
beginWork @ VM3498 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM3498 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3498 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3498 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3498 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3498 chunk-Q72EVS5P.js:18706
workLoop @ VM3498 chunk-Q72EVS5P.js:197
flushWork @ VM3498 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3498 chunk-Q72EVS5P.js:384
VM3546 AssistantAwareContext.jsx:43 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ VM3546 AssistantAwareContext.jsx:43
mountMemo @ VM3498 chunk-Q72EVS5P.js:12214
useMemo @ VM3498 chunk-Q72EVS5P.js:12538
useMemo @ VM3493 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3546 AssistantAwareContext.jsx:37
renderWithHooks @ VM3498 chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ VM3498 chunk-Q72EVS5P.js:14996
beginWork @ VM3498 chunk-Q72EVS5P.js:15934
beginWork$1 @ VM3498 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3498 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3498 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3498 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3498 chunk-Q72EVS5P.js:18706
workLoop @ VM3498 chunk-Q72EVS5P.js:197
flushWork @ VM3498 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3498 chunk-Q72EVS5P.js:384
VM3546 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
VM3546 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
VM3546 AssistantAwareContext.jsx:162 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
VM3609 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM3609 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
VM3899 assistantSyncManager.js:52 ✅ [AssistantSyncManager] Real-time subscriptions initialized
AuthContext.jsx:131 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
AuthContext.jsx:134 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:141 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:152 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
VM3512 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
VM3512 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
VM3512 AssistantAwareContext.jsx:182 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ VM3512 AssistantAwareContext.jsx:182
updateMemo @ VM3484 chunk-Q72EVS5P.js:12230
useMemo @ VM3484 chunk-Q72EVS5P.js:12746
useMemo @ VM3479 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3512 AssistantAwareContext.jsx:153
renderWithHooks @ VM3484 chunk-Q72EVS5P.js:11568
updateFunctionComponent @ VM3484 chunk-Q72EVS5P.js:14602
beginWork @ VM3484 chunk-Q72EVS5P.js:15944
beginWork$1 @ VM3484 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3484 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3484 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3484 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3484 chunk-Q72EVS5P.js:18706
workLoop @ VM3484 chunk-Q72EVS5P.js:197
flushWork @ VM3484 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3484 chunk-Q72EVS5P.js:384
VM3512 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
VM3512 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
VM3512 AssistantAwareContext.jsx:182 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ VM3512 AssistantAwareContext.jsx:182
updateMemo @ VM3484 chunk-Q72EVS5P.js:12230
useMemo @ VM3484 chunk-Q72EVS5P.js:12746
useMemo @ VM3479 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3512 AssistantAwareContext.jsx:153
renderWithHooks @ VM3484 chunk-Q72EVS5P.js:11568
updateFunctionComponent @ VM3484 chunk-Q72EVS5P.js:14607
beginWork @ VM3484 chunk-Q72EVS5P.js:15944
beginWork$1 @ VM3484 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3484 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3484 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3484 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3484 chunk-Q72EVS5P.js:18706
workLoop @ VM3484 chunk-Q72EVS5P.js:197
flushWork @ VM3484 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3484 chunk-Q72EVS5P.js:384
VM3574 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM3512 AssistantAwareContext.jsx:59 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
VM3512 AssistantAwareContext.jsx:66 ✅ [AssistantAwareContext] Loading assistant data for: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3741 vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
VM3741 vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
VM3538 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM3538 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM3592 SimplePreviewPage.jsx:82 SimplePreviewPage: Starting config load...
VM3592 SimplePreviewPage.jsx:83 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
VM3592 SimplePreviewPage.jsx:101 SimplePreviewPage: Loading assistant config for subdomain: damon
VM3592 SimplePreviewPage.jsx:61 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
VM3592 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM3666 useStandaloneAttorney.js:29 [useStandaloneAttorney] Manager found, initializing hook...
VM3666 useStandaloneAttorney.js:78 [useStandaloneAttorney] Setting up manager subscription
VM3492 standaloneAttorneyManagerFix.js:32 [StandaloneAttorneyManagerFix] Adding subscriber
VM3546 AssistantAwareContext.jsx:59 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
VM3546 AssistantAwareContext.jsx:69 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
VM3480 App.jsx:630 🚀 [App] Initializing with safe subdomain detection...
VM3545 AuthContext.jsx:52 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
VM3592 SimplePreviewPage.jsx:82 SimplePreviewPage: Starting config load...
VM3592 SimplePreviewPage.jsx:83 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
VM3592 SimplePreviewPage.jsx:101 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:24.789Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:24.790Z'}
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
VM3495 App.jsx:710 🏁 [App] Initialization complete
VM3987 productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
VM3987 productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
VM3495 App.jsx:641 ✅ Production environment initialized
VM3495 App.jsx:78 Supabase config initialization (fallback)
VM3495 App.jsx:82 Supabase config verification (fallback)
VM3495 App.jsx:649 ✅ Supabase configured successfully
VM3574 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM3495 App.jsx:662 🔍 [App] Subdomain detected: default
VM3495 App.jsx:667 🏠 [App] Localhost detected - treating as main domain
VM3495 App.jsx:710 🏁 [App] Initialization complete
VM3495 App.jsx:458 🔥 [App.jsx] App component is starting!
VM3495 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM3495 App.jsx:513 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM3495 App.jsx:519 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM3495 App.jsx:458 🔥 [App.jsx] App component is starting!
VM3495 App.jsx:461 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
VM3495 App.jsx:513 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
VM3495 App.jsx:519 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
VM3512 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
VM3512 AssistantAwareContext.jsx:172 ⏳ [AssistantAwareContext] Assistant subdomain loading...
VM3512 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
VM3512 AssistantAwareContext.jsx:172 ⏳ [AssistantAwareContext] Assistant subdomain loading...
VM3574 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM3574 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
EnhancedAssistantDropdown.jsx:211 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
VM3947 assistantSyncManager.js:52 ✅ [AssistantSyncManager] Real-time subscriptions initialized
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
VM3546 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
VM3546 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
VM3546 AssistantAwareContext.jsx:182 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ VM3546 AssistantAwareContext.jsx:182
updateMemo @ VM3498 chunk-Q72EVS5P.js:12230
useMemo @ VM3498 chunk-Q72EVS5P.js:12746
useMemo @ VM3493 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3546 AssistantAwareContext.jsx:153
renderWithHooks @ VM3498 chunk-Q72EVS5P.js:11568
updateFunctionComponent @ VM3498 chunk-Q72EVS5P.js:14602
beginWork @ VM3498 chunk-Q72EVS5P.js:15944
beginWork$1 @ VM3498 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3498 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3498 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3498 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3498 chunk-Q72EVS5P.js:18706
workLoop @ VM3498 chunk-Q72EVS5P.js:197
flushWork @ VM3498 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3498 chunk-Q72EVS5P.js:384
VM3546 AssistantAwareContext.jsx:46 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
VM3546 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
VM3546 AssistantAwareContext.jsx:182 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ VM3546 AssistantAwareContext.jsx:182
updateMemo @ VM3498 chunk-Q72EVS5P.js:12230
useMemo @ VM3498 chunk-Q72EVS5P.js:12746
useMemo @ VM3493 chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ VM3546 AssistantAwareContext.jsx:153
renderWithHooks @ VM3498 chunk-Q72EVS5P.js:11568
updateFunctionComponent @ VM3498 chunk-Q72EVS5P.js:14607
beginWork @ VM3498 chunk-Q72EVS5P.js:15944
beginWork$1 @ VM3498 chunk-Q72EVS5P.js:19781
performUnitOfWork @ VM3498 chunk-Q72EVS5P.js:19226
workLoopSync @ VM3498 chunk-Q72EVS5P.js:19165
renderRootSync @ VM3498 chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ VM3498 chunk-Q72EVS5P.js:18706
workLoop @ VM3498 chunk-Q72EVS5P.js:197
flushWork @ VM3498 chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ VM3498 chunk-Q72EVS5P.js:384
EnhancedAssistantDropdown.jsx:78 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
ProfileTab.jsx:57 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T12:25:11.287607+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
ProfileTab.jsx:58 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:24.896Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:24.896Z'}
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
ProfileTab.jsx:71 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
ProfileTab.jsx:87 ✅ Using attorney email from database: <EMAIL>
ProfileTab.jsx:124 ✅ Email successfully populated: <EMAIL>
VM3592 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
VM3592 SimplePreviewPage.jsx:182 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
 ✅ [AssistantAwareContext] Loading assistant data for: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using fallback iframe communication
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:25.057Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:25.058Z'}
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [DashboardNew] Using fallback iframe communication
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:25.230Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:25.231Z'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 Attorney object in ProfileTab: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T12:25:11.287607+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
 User object in ProfileTab: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 🔍 Email sources debug: {userEmail: '<EMAIL>', userMetadataEmail: '<EMAIL>', userIdentityEmail: '<EMAIL>', userIdentityProviderData: 'google', attorneyEmail: '<EMAIL>', …}
 ✅ Using attorney email from database: <EMAIL>
 ✅ Email successfully populated: <EMAIL>
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 [DashboardNew] Using fallback iframe communication
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using fallback iframe communication
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 ✅ [Supabase-Fixed] Client test passed
 ✅ [Supabase-Fixed] Client initialized and tested successfully
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 ✅ [AssistantAwareContext] Using assistant name from UI config: Damon's Assistant Assistant
 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', subdomain: 'damon-2', assistantName: "Damon's Assistant Assistant", hasSubdomain: true, hasName: true}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 ✅ [Supabase-Fixed] Client test passed
 ✅ [Supabase-Fixed] Client initialized and tested successfully
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: Damon's Assistant
 titleText: Damon's Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
VM3542 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3989 simpleSubdomainService.js:226 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
VM3546 AssistantAwareContext.jsx:101 ✅ [AssistantAwareContext] Using assistant name from UI config: Damon's Assistant Assistant
VM3546 AssistantAwareContext.jsx:124 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', subdomain: 'damon-2', assistantName: "Damon's Assistant Assistant", hasSubdomain: true, hasName: true}
VM3546 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
VM3546 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
VM3546 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
VM3546 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3538 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM3538 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM3538 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM3622 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM3622 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM3622 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM3622 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM3622 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3622 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
VM3622 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3622 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
VM3538 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM3538 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM3538 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM3622 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM3622 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM3622 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM3622 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM3622 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3622 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
VM3622 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3622 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
VM3622 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
VM3622 EnhancedPreviewNew.jsx:402 Using custom logoUrl: data:image/jpeg;base64,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
VM3622 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
VM3622 EnhancedPreviewNew.jsx:402 Using custom logoUrl: data:image/jpeg;base64,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
VM3542 Button.jsx:220 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
VM3575 imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
VM3575 imageStorage.js:133 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: data:image/jpeg;base64,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
 primaryColor: #10b981
 secondaryColor: #059669
 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
 vapiAssistantId: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:25.771Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:25.772Z'}
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:25.943Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:25.944Z'}
 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Found 2 iframes on the page
 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 [EnhancedPreview] Message source: parent
VM3622 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM3622 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM3622 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3622 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
VM3622 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3622 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
VM3725 EnhancedPreviewNew.jsx:124 [EnhancedPreview] Component mounted and ready to receive messages
VM3725 EnhancedPreviewNew.jsx:125 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
VM3725 EnhancedPreviewNew.jsx:322 EnhancedPreview: Sent ready message to parent
VM3725 EnhancedPreviewNew.jsx:369 [EnhancedPreviewNew] State updated:
VM3725 EnhancedPreviewNew.jsx:370 firmName: Damon's Assistant
VM3725 EnhancedPreviewNew.jsx:371 titleText: Damon's Assistant
VM3725 EnhancedPreviewNew.jsx:372 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3725 EnhancedPreviewNew.jsx:373 primaryColor: #2563eb
VM3725 EnhancedPreviewNew.jsx:374 secondaryColor: #1e40af
VM3725 EnhancedPreviewNew.jsx:375 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
VM3725 EnhancedPreviewNew.jsx:376 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
VM3725 EnhancedPreviewNew.jsx:377 voiceId: sarah
VM3725 EnhancedPreviewNew.jsx:378 voiceProvider: playht
VM3725 EnhancedPreviewNew.jsx:379 chatActive: false
VM3725 EnhancedPreviewNew.jsx:124 [EnhancedPreview] Component mounted and ready to receive messages
VM3725 EnhancedPreviewNew.jsx:125 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
VM3725 EnhancedPreviewNew.jsx:322 EnhancedPreview: Sent ready message to parent
VM3725 EnhancedPreviewNew.jsx:369 [EnhancedPreviewNew] State updated:
VM3725 EnhancedPreviewNew.jsx:370 firmName: Damon's Assistant
VM3725 EnhancedPreviewNew.jsx:371 titleText: Damon's Assistant
VM3725 EnhancedPreviewNew.jsx:372 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3725 EnhancedPreviewNew.jsx:373 primaryColor: #2563eb
VM3725 EnhancedPreviewNew.jsx:374 secondaryColor: #1e40af
VM3725 EnhancedPreviewNew.jsx:375 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
VM3725 EnhancedPreviewNew.jsx:376 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
VM3725 EnhancedPreviewNew.jsx:377 voiceId: sarah
VM3725 EnhancedPreviewNew.jsx:378 voiceProvider: playht
VM3725 EnhancedPreviewNew.jsx:379 chatActive: false
VM3986 simpleSubdomainService.js:238 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
VM3986 simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
VM3538 SimplePreviewPage.jsx:68 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
VM3538 SimplePreviewPage.jsx:104 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
VM3538 SimplePreviewPage.jsx:134 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
VM3538 SimplePreviewPage.jsx:145 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
VM3622 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM3622 EnhancedPreviewNew.jsx:402 Using custom logoUrl: data:image/jpeg;base64,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
VM3622 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM3622 EnhancedPreviewNew.jsx:402 Using custom logoUrl: data:image/jpeg;base64,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
VM3542 Button.jsx:220 Button component received mascot URL: data:image/jpeg;base64,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
VM3575 imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
VM3575 imageStorage.js:133 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM3611 imageStorage.js:139 Image is an absolute URL
VM3596 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3989 simpleSubdomainService.js:238 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
VM3989 simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
VM3592 SimplePreviewPage.jsx:68 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
VM3592 SimplePreviewPage.jsx:104 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
VM3592 SimplePreviewPage.jsx:134 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
VM3592 SimplePreviewPage.jsx:145 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
DashboardNew.jsx:914 Found 2 iframes on the page
DashboardNew.jsx:927 Found preview iframe: http://localhost:5179/simple-preview?subdomain=damon&theme=dark&loadFromSupabase=true&useEnhancedPreview=true
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.155Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.156Z'}
VM3725 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM3725 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3725 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM3725 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3596 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3611 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM3611 imageStorage.js:139 Image is an absolute URL
VM3596 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3596 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3611 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM3611 imageStorage.js:139 Image is an absolute URL
VM3596 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM3538 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM3538 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM3538 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM3622 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM3622 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM3622 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM3622 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM3622 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3622 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
VM3622 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3622 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText (from firmName), attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, welcomeMessage, logoUrl, vapiInstructions, vapiContext, vapiAssistantId, theme
VM3622 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
VM3622 EnhancedPreviewNew.jsx:402 Using custom logoUrl: data:image/jpeg;base64,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
VM3622 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
VM3622 EnhancedPreviewNew.jsx:402 Using custom logoUrl: data:image/jpeg;base64,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
VM3542 Button.jsx:220 Button component received mascot URL: data:image/jpeg;base64,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
VM3575 imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
VM3575 imageStorage.js:133 Image is a data URL
VM3542 Button.jsx:234 Processed mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
VM3542 Button.jsx:220 Button component received mascot URL: data:image/jpeg;base64,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
VM3575 imageStorage.js:121 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
VM3575 imageStorage.js:133 Image is a data URL
VM3542 Button.jsx:234 Processed mascot URL: data:image/jpeg;base64,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
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.286Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.287Z'}
VM3789 AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] Tab changed to: agent
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.429Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.430Z'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Loading assistant data for ID: 87756a2c-a398-43f2-889a-b8815684df71
 [AgentTab] Loading assistant-specific configuration for: 87756a2c-a398-43f2-889a-b8815684df71
 [AssistantConfigService] Loading config for assistant: 87756a2c-a398-43f2-889a-b8815684df71
 [AgentTab] Loading ALL settings from Vapi assistant: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [AgentTab] 🧹 Clearing assistant state to prevent data bleeding
 [DashboardNew] Calling updateAttorney with: {primary_color: '#2563eb', secondary_color: '#1e40af', welcome_message: '', logo_url: '', mascot: '', …}
 [AttorneyProfileManager] Updating attorney in Supabase: 87756a2c-a398-43f2-889a-b8815684df71
 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [AssistantInfoSection] Loading assistant data for ID: 87756a2c-a398-43f2-889a-b8815684df71
 [AgentTab] Loading assistant-specific configuration for: 87756a2c-a398-43f2-889a-b8815684df71
 [AssistantConfigService] Loading config for assistant: 87756a2c-a398-43f2-889a-b8815684df71
 [AgentTab] Loading ALL settings from Vapi assistant: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [AgentTab] 🧹 Clearing assistant state to prevent data bleeding
 [DashboardNew] Calling updateAttorney with: {primary_color: '#2563eb', secondary_color: '#1e40af', welcome_message: '', logo_url: '', mascot: '', …}
 [AttorneyProfileManager] Updating attorney in Supabase: 87756a2c-a398-43f2-889a-b8815684df71
 [AgentTab] Loading assistants using Vapi MCP Server for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [12:25:26] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
 [12:25:26] [VapiMcpService] Using fast loading mode for dashboard - direct API to avoid CORS issues 
 [EnhancedVapiMcpService] Initializing StreamableHTTP MCP client...
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.473Z'}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Using fallback iframe communication
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.475Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.476Z'}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Using fallback iframe communication
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.478Z'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM3993 simple-preview:4 🚀 [LegalScout] Initializing environment...
VM3993 simple-preview:26 ✅ [LegalScout] Environment initialized
VM3994 simple-preview:85 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
VM3995 simple-preview:84 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
vapiMcpService.js:497 [VapiMcpService] UUID format detected as assistant ID: 87756a2c-a398-43f2-889a-b8815684df71
getAssistant @ vapiMcpService.js:497
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:498 [VapiMcpService] This might be a Supabase attorney ID instead of a Vapi assistant ID
getAssistant @ vapiMcpService.js:498
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:525 [VapiMcpService] Getting assistant: 87756a2c-a398-43f2-889a-b8815684df71
vapiMcpService.js:608 [VapiMcpService] Error getting assistant: Error: Not connected
    at @modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6616:16
    at new Promise (<anonymous>)
    at Client.request (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6613:12)
    at Client.callTool (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6934:31)
    at VapiMcpService.getAssistant (vapiMcpService.js:588:42)
    at async loadAssistantData (VM3844 AssistantInfoSection.jsx:69:25)
getAssistant @ vapiMcpService.js:608
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:615 [VapiMcpService] MCP failed, trying direct API call for assistant: 87756a2c-a398-43f2-889a-b8815684df71
getAssistant @ vapiMcpService.js:615
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:689 [VapiMcpService] Listing assistants
vapiMcpService.js:732 [VapiMcpService] Error listing assistants: Error: Not connected
    at @modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6616:16
    at new Promise (<anonymous>)
    at Client.request (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6613:12)
    at Client.callTool (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6934:31)
    at VapiMcpService.listAssistants (vapiMcpService.js:712:42)
    at async loadAvailableAssistants (VM3735 AgentTab.jsx:1488:29)
listAssistants @ vapiMcpService.js:732
await in listAssistants
loadAvailableAssistants @ AgentTab.jsx:2040
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:2098
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
AgentTab.jsx:2078 [AgentTab] Error loading assistants from Vapi MCP: Error: Not connected
    at @modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6616:16
    at new Promise (<anonymous>)
    at Client.request (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6613:12)
    at Client.callTool (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6934:31)
    at VapiMcpService.listAssistants (vapiMcpService.js:712:42)
    at async loadAvailableAssistants (VM3735 AgentTab.jsx:1488:29)
loadAvailableAssistants @ AgentTab.jsx:2078
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:2098
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
AgentTab.jsx:2082 [AgentTab] Using fallback assistant due to MCP error
vapiMcpService.js:497 [VapiMcpService] UUID format detected as assistant ID: 87756a2c-a398-43f2-889a-b8815684df71
getAssistant @ vapiMcpService.js:497
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:498 [VapiMcpService] This might be a Supabase attorney ID instead of a Vapi assistant ID
getAssistant @ vapiMcpService.js:498
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:525 [VapiMcpService] Getting assistant: 87756a2c-a398-43f2-889a-b8815684df71
vapiMcpService.js:608 [VapiMcpService] Error getting assistant: Error: Not connected
    at @modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6616:16
    at new Promise (<anonymous>)
    at Client.request (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6613:12)
    at Client.callTool (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6934:31)
    at VapiMcpService.getAssistant (vapiMcpService.js:588:42)
    at async loadAssistantData (VM3844 AssistantInfoSection.jsx:69:25)
getAssistant @ vapiMcpService.js:608
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:615 [VapiMcpService] MCP failed, trying direct API call for assistant: 87756a2c-a398-43f2-889a-b8815684df71
getAssistant @ vapiMcpService.js:615
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:689 [VapiMcpService] Listing assistants
vapiMcpService.js:732 [VapiMcpService] Error listing assistants: Error: Not connected
    at @modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6616:16
    at new Promise (<anonymous>)
    at Client.request (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6613:12)
    at Client.callTool (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6934:31)
    at VapiMcpService.listAssistants (vapiMcpService.js:712:42)
    at async loadAvailableAssistants (VM3735 AgentTab.jsx:1488:29)
listAssistants @ vapiMcpService.js:732
await in listAssistants
loadAvailableAssistants @ AgentTab.jsx:2040
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:2098
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
AgentTab.jsx:2078 [AgentTab] Error loading assistants from Vapi MCP: Error: Not connected
    at @modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6616:16
    at new Promise (<anonymous>)
    at Client.request (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6613:12)
    at Client.callTool (@modelcontextprotocol_sdk_client_index__js.js?v=9711cfb6:6934:31)
    at VapiMcpService.listAssistants (vapiMcpService.js:712:42)
    at async loadAvailableAssistants (VM3735 AgentTab.jsx:1488:29)
loadAvailableAssistants @ AgentTab.jsx:2078
await in loadAvailableAssistants
(anonymous) @ AgentTab.jsx:2098
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
AgentTab.jsx:2082 [AgentTab] Using fallback assistant due to MCP error
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
EnhancedVapiMcpService.js:207 [EnhancedVapiMcpService] Skipping MCP connection - using direct API only
EnhancedVapiMcpService.js:210 [EnhancedVapiMcpService] ✅ StreamableHTTP MCP client connected
vapiLogger.js:103 [12:25:26] [VapiMcpService] Retrieving assistant {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
EnhancedVapiMcpService.js:254 [EnhancedVapiMcpService] MCP tool call get_assistant failed: MCP client not initialized
makeStreamableHTTPRequest @ EnhancedVapiMcpService.js:254
(anonymous) @ EnhancedVapiMcpService.js:349
withRetry @ EnhancedVapiMcpService.js:317
getAssistant @ EnhancedVapiMcpService.js:344
getCompleteAssistantData @ VapiDirectApiService.js:124
loadVapiAssistantSettings @ AgentTab.jsx:855
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:901
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
EnhancedVapiMcpService.js:355 [EnhancedVapiMcpService] Streamable-HTTP failed, using direct API: MCP client not initialized
(anonymous) @ EnhancedVapiMcpService.js:355
await in (anonymous)
withRetry @ EnhancedVapiMcpService.js:317
getAssistant @ EnhancedVapiMcpService.js:344
getCompleteAssistantData @ VapiDirectApiService.js:124
loadVapiAssistantSettings @ AgentTab.jsx:855
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:901
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
EnhancedVapiMcpService.js:207 [EnhancedVapiMcpService] Skipping MCP connection - using direct API only
EnhancedVapiMcpService.js:210 [EnhancedVapiMcpService] ✅ StreamableHTTP MCP client connected
vapiLogger.js:103 [12:25:26] [VapiMcpService] Retrieving assistant {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
EnhancedVapiMcpService.js:254 [EnhancedVapiMcpService] MCP tool call get_assistant failed: MCP client not initialized
makeStreamableHTTPRequest @ EnhancedVapiMcpService.js:254
(anonymous) @ EnhancedVapiMcpService.js:349
withRetry @ EnhancedVapiMcpService.js:317
getAssistant @ EnhancedVapiMcpService.js:344
getCompleteAssistantData @ VapiDirectApiService.js:124
loadVapiAssistantSettings @ AgentTab.jsx:855
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:901
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
EnhancedVapiMcpService.js:355 [EnhancedVapiMcpService] Streamable-HTTP failed, using direct API: MCP client not initialized
(anonymous) @ EnhancedVapiMcpService.js:355
await in (anonymous)
withRetry @ EnhancedVapiMcpService.js:317
getAssistant @ EnhancedVapiMcpService.js:344
getCompleteAssistantData @ VapiDirectApiService.js:124
loadVapiAssistantSettings @ AgentTab.jsx:855
await in loadVapiAssistantSettings
(anonymous) @ AgentTab.jsx:901
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&assistant_id=eq.87756a2c-a398-43f2-889a-b8815684df71&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71 406 (Not Acceptable)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
VM3655 AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
VM3655 AttorneyProfileManager.js:1503 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM3655 AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
assistantConfigService.js:145 [AssistantConfigService] No config found for assistant
AgentTab.jsx:337 [AgentTab] Loaded assistant configuration: null
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
vapiMcpService.js:617 
            
            
           GET https://api.vapi.ai/assistant/87756a2c-a398-43f2-889a-b8815684df71 401 (Unauthorized)
getAssistant @ vapiMcpService.js:617
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:637 [VapiMcpService] Assistant not found, throwing proper error instead of mock data
getAssistant @ vapiMcpService.js:637
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:642 [VapiMcpService] UUID format detected, might be attorney ID instead of assistant ID: 87756a2c-a398-43f2-889a-b8815684df71
getAssistant @ vapiMcpService.js:642
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
AssistantInfoSection.jsx:111 [AssistantInfoSection] ❌ Error loading assistant data: Error: Assistant not found: 87756a2c-a398-43f2-889a-b8815684df71. This appears to be a UUID format - please verify this is a valid Vapi assistant ID, not an attorney ID.
    at VapiMcpService.getAssistant (vapiMcpService.js:643:17)
    at async loadAssistantData (VM4162 AssistantInfoSection.jsx:69:25)
loadAssistantData @ AssistantInfoSection.jsx:111
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
AgentTab.jsx:958 [AgentTab] Loaded assistant-specific configuration: {id: 'fc789cda-85e0-46cf-9053-248a863a3229', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', firm_name: null, logo_url: null, …}
AgentTab.jsx:994 [AgentTab] ⚠️ No banner image found for this assistant
DashboardNew.jsx:778 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:850 [DashboardNew] Only non-Vapi fields changed: logo_url, mascot
DashboardNew.jsx:851 [DashboardNew] Skipping Vapi sync for UI-only changes
DashboardNew.jsx:855 [DashboardNew] Calling updateAttorney with: {logo_url: '', mascot: ''}
AttorneyProfileManager.js:1435 [AttorneyProfileManager] Updating attorney in Supabase: 87756a2c-a398-43f2-889a-b8815684df71
AgentTab.jsx:1004 [AgentTab] Form data initialized from assistant configuration
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.853Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.855Z'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.943Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:26.945Z'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
vapiMcpService.js:617 
            
            
           GET https://api.vapi.ai/assistant/87756a2c-a398-43f2-889a-b8815684df71 401 (Unauthorized)
getAssistant @ vapiMcpService.js:617
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:637 [VapiMcpService] Assistant not found, throwing proper error instead of mock data
getAssistant @ vapiMcpService.js:637
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiMcpService.js:642 [VapiMcpService] UUID format detected, might be attorney ID instead of assistant ID: 87756a2c-a398-43f2-889a-b8815684df71
getAssistant @ vapiMcpService.js:642
await in getAssistant
loadAssistantData @ AssistantInfoSection.jsx:83
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
AssistantInfoSection.jsx:111 [AssistantInfoSection] ❌ Error loading assistant data: Error: Assistant not found: 87756a2c-a398-43f2-889a-b8815684df71. This appears to be a UUID format - please verify this is a valid Vapi assistant ID, not an attorney ID.
    at VapiMcpService.getAssistant (vapiMcpService.js:643:17)
    at async loadAssistantData (VM4162 AssistantInfoSection.jsx:69:25)
loadAssistantData @ AssistantInfoSection.jsx:111
await in loadAssistantData
(anonymous) @ AssistantInfoSection.jsx:65
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
vapiLogger.js:103 [12:25:27] [VapiMcpService] Assistant verified in Vapi {id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', name: "Damon's Assistant Assistant"}
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&assistant_id=eq.87756a2c-a398-43f2-889a-b8815684df71&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71 406 (Not Acceptable)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
AgentTab.jsx:958 [AgentTab] Loaded assistant-specific configuration: {id: 'fc789cda-85e0-46cf-9053-248a863a3229', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', firm_name: null, logo_url: null, …}
AgentTab.jsx:994 [AgentTab] ⚠️ No banner image found for this assistant
DashboardNew.jsx:778 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:850 [DashboardNew] Only non-Vapi fields changed: logo_url, mascot
DashboardNew.jsx:851 [DashboardNew] Skipping Vapi sync for UI-only changes
DashboardNew.jsx:855 [DashboardNew] Calling updateAttorney with: {logo_url: '', mascot: ''}
AttorneyProfileManager.js:1435 [AttorneyProfileManager] Updating attorney in Supabase: 87756a2c-a398-43f2-889a-b8815684df71
AgentTab.jsx:1004 [AgentTab] Form data initialized from assistant configuration
assistantConfigService.js:145 [AssistantConfigService] No config found for assistant
AgentTab.jsx:337 [AgentTab] Loaded assistant configuration: null
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:27.322Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:27.322Z'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
VM4062 supabase.js:333 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
VM4062 supabase.js:122 🚀 [Supabase-Fixed] Initializing client...
VM4062 supabase.js:66 🔧 [Supabase-Fixed] Creating client for environment: {isDevelopment: true, isProduction: false, baseUrl: 'http://localhost:5179', hasUrl: true, hasKey: true}
VM4062 supabase.js:104 ✅ [Supabase-Fixed] Client created successfully
VM4024 unifiedAuthService.js:249 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
VM4154 vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
VM4154 vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
VM4192 vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
VM4192 vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
VM4180 vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
VM4118 AttorneyProfileManager.js:1516 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM4118 AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
VM4118 AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
VM4015 standaloneAttorneyManagerFix.js:8 🔧 [StandaloneAttorneyManagerFix] Initializing...
VM4015 standaloneAttorneyManagerFix.js:156 🔧 [StandaloneAttorneyManagerFix] Starting initialization...
VM4015 standaloneAttorneyManagerFix.js:11 🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...
VM4015 standaloneAttorneyManagerFix.js:135 [StandaloneAttorneyManagerFix] Initialized with current attorney: 87756a2c-a398-43f2-889a-b8815684df71
VM4015 standaloneAttorneyManagerFix.js:143 [StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...
VM4015 standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
VM4015 standaloneAttorneyManagerFix.js:151 ✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created
VM4015 standaloneAttorneyManagerFix.js:171 🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!
VM4015 standaloneAttorneyManagerFix.js:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 0
VM4015 standaloneAttorneyManagerFix.js:195 ✅ [StandaloneAttorneyManagerFix] Service loaded
VM3997 main.jsx:23 🚀 [LegalScout] Starting React app...
VM3997 main.jsx:32 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
VM3997 main.jsx:45 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
VM3997 main.jsx:90 ✅ [LegalScout] React app rendered successfully
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Dashboard preview iframe loaded, waiting for PREVIEW_READY message
 [ErrorBoundary] Creating React placeholder
 [ErrorBoundary] Adding createContext placeholder
 [ErrorBoundary] Adding useState placeholder
 [ErrorBoundary] Adding useEffect placeholder
 [ErrorBoundary] Adding useLayoutEffect placeholder
 [ErrorBoundary] Adding useRef placeholder
 [ErrorBoundary] Adding useCallback placeholder
 [ErrorBoundary] Adding useMemo placeholder
 [ErrorBoundary] Adding useContext placeholder
 [ErrorBoundary] Adding forwardRef placeholder
 [ErrorBoundary] Adding createElement placeholder
 [ErrorBoundary] Adding cloneElement placeholder
 [ErrorBoundary] Adding createRef placeholder
 [ErrorBoundary] Adding Component placeholder
 [ErrorBoundary] Adding PureComponent placeholder
 [ErrorBoundary] Adding Fragment placeholder
 [ErrorBoundary] Adding Children placeholder
 [ErrorBoundary] Adding isValidElement placeholder
 [ErrorBoundary] React polyfills applied
 [ErrorBoundary] React polyfills applied
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/simple-preview', isAuthenticated: false, hasSession: false}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14946
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🚨 [AssistantAwareContext] Attorney ID detected as assistant ID: undefined
(anonymous) @ AssistantAwareContext.jsx:42
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🚨 [AssistantAwareContext] This indicates a data integrity issue - attorney ID should not be used as assistant ID
(anonymous) @ AssistantAwareContext.jsx:43
mountMemo @ chunk-Q72EVS5P.js:12214
useMemo @ chunk-Q72EVS5P.js:12538
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:37
renderWithHooks @ chunk-Q72EVS5P.js:11568
mountIndeterminateComponent @ chunk-Q72EVS5P.js:14996
beginWork @ chunk-Q72EVS5P.js:15934
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, attorney_id: undefined, candidateId: undefined, …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: null, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] No assistant selected - using placeholder
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
 [VapiLoader] ✅ Vapi SDK validation successful
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.007Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.008Z'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 SimplePreviewPage: Starting config load...
 SimplePreviewPage: URL search params: {subdomain: 'damon', theme: 'dark', loadFromSupabase: 'true', useEnhancedPreview: 'true'}
 SimplePreviewPage: Loading assistant config for subdomain: damon
 SimplePreviewPage: Loading assistant config using simple service for subdomain: damon
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager found, initializing hook...
 [useStandaloneAttorney] Setting up manager subscription
 [StandaloneAttorneyManagerFix] Adding subscriber
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: null, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
 🚀 [App] Initializing with safe subdomain detection...
 🔐 [AuthContext-P1] Starting BASIC auth initialization (no attorney loading)...
 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', name: "Damon's Assistant Assistant", hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
 🌐 Loaded assistant subdomain: damon-2
 Loaded assistant subdomains: (3) [{…}, {…}, {…}]
 [12:25:28] [VapiMcpService] Assistant verified in Vapi {id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', name: "Damon's Assistant Assistant"}
 ✅ [VapiDirectApiService] Using direct API data (complete)
 📋 [AgentTab] Found complete assistant data: {firstMessage: "Hello! I'm an AI assistant from Damon's Assistant....", hasSystemPrompt: true, systemPromptLength: 74, voice: {…}, source: 'direct-api'}
 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are a helpful legal assistant. Be professional, accurate, and helpful.'}
 [AttorneyProfileManager] Updating attorney in Supabase: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
 ✅ [AssistantSyncManager] Real-time subscriptions initialized
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.130Z'}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.132Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.133Z'}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.135Z'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14602
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔍 [AssistantAwareContext] Current assistant ID resolution: {forceAssistantId: null, current_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorney_id: '87756a2c-a398-43f2-889a-b8815684df71', candidateId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', …}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
(anonymous) @ AssistantAwareContext.jsx:182
updateMemo @ chunk-Q72EVS5P.js:12230
useMemo @ chunk-Q72EVS5P.js:12746
useMemo @ chunk-2N3A5BUM.js:1094
AssistantAwareProvider @ AssistantAwareContext.jsx:153
renderWithHooks @ chunk-Q72EVS5P.js:11568
updateFunctionComponent @ chunk-Q72EVS5P.js:14607
beginWork @ chunk-Q72EVS5P.js:15944
beginWork$1 @ chunk-Q72EVS5P.js:19781
performUnitOfWork @ chunk-Q72EVS5P.js:19226
workLoopSync @ chunk-Q72EVS5P.js:19165
renderRootSync @ chunk-Q72EVS5P.js:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js:18706
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔍 [VapiDirectApiService] Retrieved complete assistant data: {id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', name: "Damon's Assistant Assistant", hasFirstMessage: true, hasInstructions: true, voice: {…}, …}
 ✅ [VapiDirectApiService] Using direct API data (complete)
 📋 [AgentTab] Found complete assistant data: {firstMessage: "Hello! I'm an AI assistant from Damon's Assistant....", hasSystemPrompt: true, systemPromptLength: 74, voice: {…}, source: 'direct-api'}
 [DashboardNew] Calling updateAttorney with: {vapi_instructions: 'You are a helpful legal assistant. Be professional, accurate, and helpful.'}
 [AttorneyProfileManager] Updating attorney in Supabase: 87756a2c-a398-43f2-889a-b8815684df71
 ✅ [AgentTab] ALL settings loaded from Vapi (complete data)
 🌐 Loaded assistant subdomain: damon-2
 Loaded assistant subdomains: (3) [{…}, {…}, {…}]
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.230Z'}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.232Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.234Z'}
 Updated preview config: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.235Z'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
 ✅ [AssistantAwareContext] Loading assistant data for: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ⏳ [AssistantAwareContext] Assistant subdomain loading...
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 🔐 [AuthContext-P1] Auth state changed: SIGNED_IN
 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
 [DashboardNew] Using fallback iframe communication
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/dashboard', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.299Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.300Z'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [DashboardNew] Using fallback iframe communication
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔍 [SimpleSubdomain] Loading config for: damon
 🔍 [SimpleSubdomain] Looking up assistant ID for: damon
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔧 [ProductionEnvironment] Initializing production environment...
 ✅ [ProductionEnvironment] Environment variables initialized in window object
 ✅ Production environment initialized
 Supabase config initialization (fallback)
 Supabase config verification (fallback)
 ✅ Supabase configured successfully
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🔍 [App] Subdomain detected: default
 🏠 [App] Localhost detected - treating as main domain
 🏁 [App] Initialization complete
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: '', theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#10b981', …}
 Using custom logoUrl: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,/9j/4QC8RXhpZgAASUkqAAgAAAAGABIBAwABAAAAAQAAABoBBQABAAAAVgAAABsBBQABAAAAXgAAACgBAwABAAAAAgAAABMCAwABAAAAAQAAAGmHBAABAAAAZgAAAAAAAABIAAAAAQAAAEgAAAABAAAABgAAkAcABAAAADAyMTABkQcABAAAAAECAwAAoAcABAAAADAxMDABoAMAAQAAAP//AAACoAQAAQAAAEALAAADoAQAAQAAAAAJAAAAAAAA/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8AAEQgJAAtAAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A4WiiiucAooooAKKKKACiiigAooqegCCiiigAooooAKKKKACiiigAooooAKKKKACiij7PQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAV6sUUUAFV6sUUAFV6sVXoAKKKKACrFV6KACiiigAooooAKKKKACiiigAqxB/Z9xBP59xP5/8Ay71Xnv8A7P8A6ioPt/8ApH7+gDvPA1joHn+ff2/2ib/l3+0V6P8A9cP9RXiVjb3FvcQXH/LC4uK9tnt/sHkW89x9orhkFAKKPtFFYHeFWKr1YrYwJ6gnqeqE9Ym9Ag/5YVQg/wCPj7R/y3pdVuLi3t/3FULG/wBQt7CDz7f7RWx0F+f/AEf/AK4VQvr/AP54Vf8As9x/y3/5eKJ7e3+z/Z6AKEH/AB7/APHvU/2f7RcfuP8Al3osbC4uLifz/wDl4rdsdI+z/wCkQUewD24aFcXH2f8Af1fg/wCPiepvIgz59LXQc17liGpqhhqatzBkNTUVDQG4TUUVXrAYk1Q0UVzm5BR/x8UfaKrz3Fc/1gC/UFEFx9oo/wBIrcAooopgFV5/+PerFV76gDJvriuUvriukvriuTvv9IuK8nqevQMK+osannt6ggt69DoewT1RnqeqU1OK1ArVrWP+j1RqetWwNasn7P8A6RRP/o9a2h2H9rf8vHkfSstUDK9jYfaL+ul+wfZ4Kv6VoFvYf6+p57euWuef7cwq1vs/7j/phVCe3osb/wD5d5/9RXOAQXH2j/X29ZPkfZ7ir89x/o9ZXn+fVJM6C79o+z1rf2v+4rjZvtFX7K4orYdWuc56DpV//o9av2iuN0ufFdXY3H2iClRq2dmcFegTzioLK3/f1f8As9H2et/YnN7boFJDB58+aWeq8FxXShk97Bm3rCNiJ4ZvetXVL/HFUbSbIrz61ah7c3oXsYc/+gW/7isqb9/XUzWMH7/j/X9axILH/Tp4P+eFY1XZ3O9O5Xsbe4t7j9xXWT/6Rb1QgsP39a0FvUW9sYVwsav1Xgt6v/Z66KFA4KzM0W+JqteSMVNOoFZU97XT7FUTVJ1jIvJ/InqlBqwnnxS3tv8Av/tFUP8Aj3uK57nspaHS/wDLvV2GqNjcfaKsGfyKDgZeog/0eqE9/wCRVGfXsVv7cw+rnSm44rCvr/7P/r6uRXH+j1x+tf6+satXmdjahR1IJ9e/0j9xSf2959YXkUZ8iGn7CNtD0DurHUK6SC4/0evL7HUK7rSrj/QIKj+CcFegdJ51UZ7j7PViCq89v9or0OhwJWMm3v8Az7it6CeqkFlDAeBVsdKwo6G1aSexX+0VY8+jyKK6DEngqeoKnrY5xIan/wCWNQ0nn10XE0eG/EDwj/wjWqi4gH/EruP9R/0wrma+i9c0i317SZ9Pn/5eK+dL6wuLC/n0+f8A19vcVvuYhRRRQAUUUUHOFFFFABRRRQAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRXOAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVPP/wBMKgooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooroAKKKK5wCiiig6AoorrPCnhn7R/xML+3/cf8u9vR7cPYFDw34Y+3z/2hf/8AHhb/APkxXZz3/wBnt57eD/R6J/s9vB/x71hX2oVwOv7Y76FAoz3H2f8A0eCqH2f7RU9j/qJ7j/lvR/pFB0FGe3+z1B5H7ir1QU7h9XIKvf2T/o/2ie4qCD/SLir0/wDo9Fw+rmV9nrc8N6f9ov8A/j3+0T1DBb+fXoPg6wt7e3+0f8t6xdezsB2VlbwW9vxRMc33kVPD/rqIf9dNPXajznuLPB5H+kUf8u9XGiEo5qnPQYJliipv+WNQ1uFyeioKKDAKoX1X6oT0VzoKFWKKK4Dcr1Y+z0UUAV/s9WKKKACiiigwK/2ej7PViij2BuV/s9H2erFV6ACiiisAIKoz2/2ir1QT1z1zoILG3/0itX7RWV9oqelQ/cga0FFV4KsV6BzhVeieoPtFYgT/AGiqP2+ie4qhPXNXrnRQoGtY3/2ip65T7R9nrdsf9IrnoVgr0C9RVj7PRXoHOFV6sUUAV/s9H2erFFAB9nqvViq9MCxRR9oqvPcVsBfoqhBcUfaKPbgQX1ZP2etae4qCe3rz6/746KAWNvWtWTBcf6RWtBXRhznrk9FFFdBgTwVYqhU9dAFiiiitznCiiigAooooAr0VYqvQAVYqvRQBYoqvRQAUUUUAFWKr1YoOgKKKKAJ6lqKig5znvHt+tj4NvmE3kZHlBvrXh09e5+MtCGv6HPb/APLeA+dB9a8FnroAr0UUVzgFFFFdAEFFT1BXOAUUUUAFFFFABRRRXQAUUUUAFFFFABRRRQAUUUVzgFFFFABRRRQAUUUUAFFFFdABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUFT0AFFQUUAFFFT0AFFFFAEFT0UUAFQVPRQAVBRU9AF+iiiucAooooAKKKKACiiigAooooAKKKKACiiigCeoKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiq9AFiiiigAoqvRQAVYqvVigCvRRRQAUUUUAFFFFABRRRQAUUUUAFFFdZ4U8Bahr7fb7+3+z2P/pRQBhaFpGoa/f/AGew/wC3i4/5969J0r4ZaPYf6Rf+ff10ljYafp9v9nsLeC3g/wCnerH/AB8QT2//AD8Vj7c6D571a4t7iefyP9H/AOnesqrGoWJsb+a3PafFV62Oc3fDmseRB/Z99befb16DoX2jyP8AlvcQf8u9xXn9jpH2i3+0QVPper6hYefcQXH2f/lhcVxV17YD1aC4/wCe9X6yNDivr/w5/aufmPOK0LH/AI965joL0FWIKoT3FvYWE9xP/qLep7G/t7+wg1CD/UXFbgX6yZ7ip57iubsbj7fb/aPtH7i4oNzWgv7f/j3nuIKgg/0ieesm+0i4sJ/tEFxPV6D/AI+P3/8Ao89B0F+f7Rb3EH/PCp763t4P9fVH7Rcf9fFXf7JuLgwTz3FAXLGlwefU+lz/AOkT29L/AKR/yw/5d6ng/wCveug52T/8vFWKrwVYoMCaklpaSWugnqRUUUUiivUE9T1RnrCuAUUUVzG5XnqD/j4qeeoK8o6Agt/s9X4Lj7RVeiD/AI+K3oAWIKnoqCeu45wnuPs/kVXvvs9xb/uKJ6o/aPs9YPEHQqBhar/o9YM9b2q3FcpPP++riSuz16An/LxU/wBnrJ+0fv6sfb/tFxXcegV56o1qz2/2ioIIIIJ/38FbgUZ6PtFTz2/2i4/cVB9noAWe486LmtzQriucNbelW9FYDu7Gtb7P+4rC0q4/0itW+/0ewrgPHrbmFfXH/LvVGC3onqv9orA6EWL7/SIKoT2/kQfaKPtFX5/s9xYfuKex0GD9np2Ps81bkFv9ogo/smj266gT6Xf4q/Y39x9vqvBYfZ7it63sfIrn66HNXN2D/j3qeqEFX69XDnhEE9V6sUVRuZ97Bm3qGxt6nnuKnhrzvYfvzfoTRW/7mqU1h+/rWorv9gc/trFCCwqx9nq9UFdHsDD24VPRUBn8igCteHya5XVL7Fxit29n8+sK+t/tFeTXZ6uHVlqE9v8A6PWTfW/+j10sFv8A6PVD7BXOjq9uZMGr/Z6nn17z/wDl3qxPYVQn0j/l4groAgvr+4uKn0K3+0T/AGiiDSLi4rdg0j7Pb0C9sa0H/HvXGa7cfZ7+usg/0eua123+0T0GFEwp7iqE9a09hVH7BXQjpKMH+vruvDlx9oggrmoNJ8+uz0Kw+z29Y4h3Jrm7BViq8FWK7TyQqv8AaKgvr/7Pb
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 Button component received mascot URL: data:image/jpeg;base64,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
 Processing image URL or ID: data:image/jpeg;base64,[643428 chars]
 Image is a data URL
 Processed mascot URL: data:image/jpeg;base64,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
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
 🔍 [EnhancedAssistantDropdown] Loading assistants using centralized service for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.521Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.522Z'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 📋 [AssistantDataService] Returning cached data for attorney: 87756a2c-a398-43f2-889a-b8815684df71
 📋 [EnhancedAssistantDropdown] Loaded assistants from centralized service: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Assistant list ready: (2) [{…}, {…}]
 ✅ [EnhancedAssistantDropdown] Subscribed to centralized assistant data service
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.568Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.569Z'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [DashboardNew] Using fallback iframe communication
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.598Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.599Z'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.619Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:28.621Z'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM4005 App.jsx:519 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM4058 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM4058 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM4022 AuthContext.jsx:68 🔐 [AuthContext-P1] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
VM4022 AuthContext.jsx:70 🔐 [AuthContext-P1] Found OAuth email: <EMAIL>
VM4022 AuthContext.jsx:77 🔐 [AuthContext-P1] ✅ Basic authentication complete - attorney data will be loaded separately
VM4022 AuthContext.jsx:92 🔐 [AuthContext-P1] ✅ Basic auth initialization complete, setting loading to false
VM4005 App.jsx:458 🔥 [App.jsx] App component is starting!
VM4005 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM4005 App.jsx:513 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM4005 App.jsx:519 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM4005 App.jsx:458 🔥 [App.jsx] App component is starting!
VM4005 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM4005 App.jsx:513 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM4005 App.jsx:519 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM4058 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM4058 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM4022 AuthContext.jsx:102 🔐 [AuthContext-P1] Auth state changed: INITIAL_SESSION
VM4022 AuthContext.jsx:104 🔐 [AuthContext-P1] OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
VM4022 AuthContext.jsx:106 🔐 [AuthContext-P1] Found OAuth email (auth change): <EMAIL>
VM4022 AuthContext.jsx:113 🔐 [AuthContext-P1] ✅ Auth state updated - attorney data will be loaded separately
VM4005 App.jsx:458 🔥 [App.jsx] App component is starting!
VM4005 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM4005 App.jsx:513 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM4005 App.jsx:519 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM4005 App.jsx:458 🔥 [App.jsx] App component is starting!
VM4005 App.jsx:461 🔥 [App.jsx] Auth state: {user: true, userEmail: '<EMAIL>', pathname: '/simple-preview', isAuthenticated: true, hasSession: true}
VM4005 App.jsx:513 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: true, userEmail: '<EMAIL>', subdomain: 'default', hostname: 'localhost'}
VM4005 App.jsx:519 🔍 [App.jsx] REDIRECT DECISION: {hasUser: true, userEmail: '<EMAIL>', redirectingTo: '/dashboard'}
VM4058 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM4058 subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
VM4263 simpleSubdomainService.js:124 ✅ [SimpleSubdomain] Found assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
VM4263 simpleSubdomainService.js:226 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
VM4062 supabase.js:150 ✅ [Supabase-Fixed] Client test passed
VM4062 supabase.js:129 ✅ [Supabase-Fixed] Client initialized and tested successfully
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4047 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM4047 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM4047 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4047 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM4047 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM4047 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM4263 simpleSubdomainService.js:238 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
VM4263 simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
VM4047 SimplePreviewPage.jsx:68 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
VM4047 SimplePreviewPage.jsx:104 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
VM4047 SimplePreviewPage.jsx:134 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
VM4047 SimplePreviewPage.jsx:145 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:124 [EnhancedPreview] Component mounted and ready to receive messages
VM4071 EnhancedPreviewNew.jsx:125 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
VM4071 EnhancedPreviewNew.jsx:322 EnhancedPreview: Sent ready message to parent
VM4071 EnhancedPreviewNew.jsx:369 [EnhancedPreviewNew] State updated:
VM4071 EnhancedPreviewNew.jsx:370 firmName: Damon's Assistant
VM4071 EnhancedPreviewNew.jsx:371 titleText: Damon's Assistant
VM4071 EnhancedPreviewNew.jsx:372 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:373 primaryColor: #2563eb
VM4071 EnhancedPreviewNew.jsx:374 secondaryColor: #1e40af
VM4071 EnhancedPreviewNew.jsx:375 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
VM4071 EnhancedPreviewNew.jsx:376 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
VM4071 EnhancedPreviewNew.jsx:377 voiceId: sarah
VM4071 EnhancedPreviewNew.jsx:378 voiceProvider: playht
VM4071 EnhancedPreviewNew.jsx:379 chatActive: false
VM4071 EnhancedPreviewNew.jsx:124 [EnhancedPreview] Component mounted and ready to receive messages
VM4071 EnhancedPreviewNew.jsx:125 [EnhancedPreview] Initial assistant ID: 1d3471b7-8694-4844-b3ef-e05720693efc
VM4071 EnhancedPreviewNew.jsx:322 EnhancedPreview: Sent ready message to parent
VM4071 EnhancedPreviewNew.jsx:369 [EnhancedPreviewNew] State updated:
VM4071 EnhancedPreviewNew.jsx:370 firmName: Damon's Assistant
VM4071 EnhancedPreviewNew.jsx:371 titleText: Damon's Assistant
VM4071 EnhancedPreviewNew.jsx:372 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:373 primaryColor: #2563eb
VM4071 EnhancedPreviewNew.jsx:374 secondaryColor: #1e40af
VM4071 EnhancedPreviewNew.jsx:375 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
VM4071 EnhancedPreviewNew.jsx:376 vapiAssistantId: 1d3471b7-8694-4844-b3ef-e05720693efc
VM4071 EnhancedPreviewNew.jsx:377 voiceId: sarah
VM4071 EnhancedPreviewNew.jsx:378 voiceProvider: playht
VM4071 EnhancedPreviewNew.jsx:379 chatActive: false
EnhancedAssistantDropdown.jsx:162 [EnhancedAssistantDropdown] Setting valid current assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:29.115Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:29.116Z'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:29.156Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:29.157Z'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
 🔍 [SimpleSubdomain] Getting Vapi config for: 1d3471b7-8694-4844-b3ef-e05720693efc
 🌐 Loaded assistant subdomains: {50e13a9e-22dd-4fe8-a03e-de627c5206c1: 'assistant', 1d3471b7-8694-4844-b3ef-e05720693efc: 'damon', 2f157a27-067c-439e-823c-f0a2bbdd66e0: 'damon-2'}
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM4071 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM4071 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM4071 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM4071 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4071 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
VM4071 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4071 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, vapiInstructions, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
VM4047 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM4047 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM4047 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM4071 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM4071 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM4071 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM4071 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM4071 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4071 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
VM4071 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4071 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, vapiInstructions, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: "Damon's Assistant Assistant", theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: "Damon's Assistant Assistant", theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:322 EnhancedPreview: Sent ready message to parent
VM4071 EnhancedPreviewNew.jsx:369 [EnhancedPreviewNew] State updated:
VM4071 EnhancedPreviewNew.jsx:370 firmName: LegalScout
VM4071 EnhancedPreviewNew.jsx:371 titleText: Damon's Assistant Assistant
VM4071 EnhancedPreviewNew.jsx:372 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:373 primaryColor: #2563eb
VM4071 EnhancedPreviewNew.jsx:374 secondaryColor: #1e40af
VM4071 EnhancedPreviewNew.jsx:375 vapiInstructions: You are a helpful legal assistant. Be professional, accurate, and helpful.
VM4071 EnhancedPreviewNew.jsx:376 vapiAssistantId: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4071 EnhancedPreviewNew.jsx:377 voiceId: echo
VM4071 EnhancedPreviewNew.jsx:378 voiceProvider: openai
VM4071 EnhancedPreviewNew.jsx:379 chatActive: false
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:29.474Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:29.475Z'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:29.517Z'}
 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:29.518Z'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
AssistantInfoSection.jsx:55 [AssistantInfoSection] Assistant ID validation: {rawId: '87756a2c-a398-43f2-889a-b8815684df71', isValid: true, finalId: '87756a2c-a398-43f2-889a-b8815684df71', source: 'context'}
VM4263 simpleSubdomainService.js:238 ✅ [SimpleSubdomain] Vapi assistant loaded: Damon's Assistant
VM4263 simpleSubdomainService.js:75 ✅ [SimpleSubdomain] Config loaded for damon: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', hasUIConfig: true, hasCallConfig: true}
VM4047 SimplePreviewPage.jsx:68 SimplePreviewPage: Successfully loaded assistant config: {subdomain: 'damon', firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
VM4047 SimplePreviewPage.jsx:104 SimplePreviewPage: Assistant config loaded successfully: {firmName: "Damon's Assistant", assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', loadedVia: 'simple_subdomain_service'}
VM4047 SimplePreviewPage.jsx:134 SimplePreviewPage: Config merged with assistant data: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc'}
VM4047 SimplePreviewPage.jsx:145 SimplePreviewPage: Final config: {firmName: "Damon's Assistant", primaryColor: '#2563eb', vapi_assistant_id: '1d3471b7-8694-4844-b3ef-e05720693efc', subdomain: 'damon'}
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: "Damon's Assistant", titleText: '', theme: 'dark', vapiAssistantId: '1d3471b7-8694-4844-b3ef-e05720693efc', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4118 AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4047 SimplePreviewPage.jsx:158 🎯 [SimplePreviewPage] Received message from parent: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM4047 SimplePreviewPage.jsx:160 🎯 [SimplePreviewPage] Processing config update from parent: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM4047 SimplePreviewPage.jsx:162 🎯 [SimplePreviewPage] Config updated successfully
VM4071 EnhancedPreviewNew.jsx:129 [EnhancedPreview] Received ANY message: {type: 'UPDATE_PREVIEW_CONFIG', config: {…}}
VM4071 EnhancedPreviewNew.jsx:130 [EnhancedPreview] Message source: parent
VM4071 EnhancedPreviewNew.jsx:131 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
VM4071 EnhancedPreviewNew.jsx:135 [EnhancedPreview] Received customization updates: {firmName: 'LegalScout', attorneyName: 'Damon Kost', practiceAreas: Array(0), state: undefined, practiceDescription: 'Providing comprehensive legal services with expert…in business law, contracts, and legal technology.', …}
VM4071 EnhancedPreviewNew.jsx:136 [EnhancedPreview] Assistant ID in customizations: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4071 EnhancedPreviewNew.jsx:137 [EnhancedPreview] All assistant ID related fields: {vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantId: undefined}
VM4071 EnhancedPreviewNew.jsx:233 EnhancedPreview: Updated Vapi assistant ID to: 2f157a27-067c-439e-823c-f0a2bbdd66e0
VM4071 EnhancedPreviewNew.jsx:268 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, vapiInstructions, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: "Damon's Assistant Assistant", theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4071 EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: {firmName: 'LegalScout', titleText: "Damon's Assistant Assistant", theme: 'dark', vapiAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', primaryColor: '#2563eb', …}
VM4071 EnhancedPreviewNew.jsx:402 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4051 Button.jsx:220 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4059 imageStorage.js:121 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
VM4059 imageStorage.js:139 Image is an absolute URL
VM4051 Button.jsx:234 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/1d3471b7-8694-4844-b3ef-e05720693efc/logo_1750154491064.png
VM4023 AssistantAwareContext.jsx:101 ✅ [AssistantAwareContext] Using assistant name from UI config: Damon's Assistant Assistant
VM4023 AssistantAwareContext.jsx:124 ✅ [AssistantAwareContext] Loaded assistant data: {assistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', subdomain: 'damon-2', assistantName: "Damon's Assistant Assistant", hasSubdomain: true, hasName: true}
VM4023 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
VM4023 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
VM4023 AssistantAwareContext.jsx:154 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: '2f157a27-067c-439e-823c-f0a2bbdd66e0', assistantSubdomain: 'damon-2', attorneySubdomain: 'damon', hasAssistantSelected: true, loadingAssistantData: false}
VM4023 AssistantAwareContext.jsx:199 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://damon-2.legalscout.net', embedUrl: 'https://damon-2.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=damon-2&loadFromSupabase…&assistantId=2f157a27-067c-439e-823c-f0a2bbdd66e0', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://damon-2.legalscout.net'}
VM4118 AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: '2f157a27-067c-439e-823c-f0a2bbdd66e0'}
VM4118 AttorneyProfileManager.js:1503 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
VM4118 AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
DashboardNew.jsx:480 [DashboardNew] Loading timeout reached, forcing loading state to false
(anonymous) @ DashboardNew.jsx:480
setTimeout
(anonymous) @ DashboardNew.jsx:478
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:19356
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.87756a2c-a398-43f2-889a-b8815684df71&select=* 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
AttorneyProfileManager.js:1483 [AttorneyProfileManager] Error updating attorney in Supabase: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
updateAttorneyInSupabase @ AttorneyProfileManager.js:1483
await in updateAttorneyInSupabase
updateAttorney @ standaloneAttorneyManagerFix.js?t=1750163096374:106
(anonymous) @ useStandaloneAttorney.js:127
handlePreviewConfigUpdate @ DashboardNew.jsx:857
clearAssistantState @ AgentTab.jsx:926
(anonymous) @ AgentTab.jsx:908
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
standaloneAttorneyManagerFix.js?t=1750163096374:113 [StandaloneAttorneyManagerFix] Error updating attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
updateAttorney @ standaloneAttorneyManagerFix.js?t=1750163096374:113
await in updateAttorney
(anonymous) @ useStandaloneAttorney.js:127
handlePreviewConfigUpdate @ DashboardNew.jsx:857
clearAssistantState @ AgentTab.jsx:926
(anonymous) @ AgentTab.jsx:908
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
useStandaloneAttorney.js:131 [useStandaloneAttorney] Error updating attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
(anonymous) @ useStandaloneAttorney.js:131
await in (anonymous)
handlePreviewConfigUpdate @ DashboardNew.jsx:857
clearAssistantState @ AgentTab.jsx:926
(anonymous) @ AgentTab.jsx:908
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
DashboardNew.jsx:861 [DashboardNew] Failed to update attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
handlePreviewConfigUpdate @ DashboardNew.jsx:861
await in handlePreviewConfigUpdate
clearAssistantState @ AgentTab.jsx:926
(anonymous) @ AgentTab.jsx:908
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.470Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.470Z'}
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.87756a2c-a398-43f2-889a-b8815684df71&select=* 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
AttorneyProfileManager.js:1483 [AttorneyProfileManager] Error updating attorney in Supabase: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
updateAttorneyInSupabase @ AttorneyProfileManager.js:1483
await in updateAttorneyInSupabase
updateAttorney @ standaloneAttorneyManagerFix.js?t=1750163096374:106
(anonymous) @ useStandaloneAttorney.js:127
handlePreviewConfigUpdate @ DashboardNew.jsx:857
loadAssistantConfiguration @ AgentTab.jsx:996
await in loadAssistantConfiguration
(anonymous) @ AgentTab.jsx:910
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
standaloneAttorneyManagerFix.js?t=1750163096374:113 [StandaloneAttorneyManagerFix] Error updating attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
updateAttorney @ standaloneAttorneyManagerFix.js?t=1750163096374:113
await in updateAttorney
(anonymous) @ useStandaloneAttorney.js:127
handlePreviewConfigUpdate @ DashboardNew.jsx:857
loadAssistantConfiguration @ AgentTab.jsx:996
await in loadAssistantConfiguration
(anonymous) @ AgentTab.jsx:910
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
useStandaloneAttorney.js:131 [useStandaloneAttorney] Error updating attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
(anonymous) @ useStandaloneAttorney.js:131
await in (anonymous)
handlePreviewConfigUpdate @ DashboardNew.jsx:857
loadAssistantConfiguration @ AgentTab.jsx:996
await in loadAssistantConfiguration
(anonymous) @ AgentTab.jsx:910
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
DashboardNew.jsx:861 [DashboardNew] Failed to update attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
handlePreviewConfigUpdate @ DashboardNew.jsx:861
await in handlePreviewConfigUpdate
loadAssistantConfiguration @ AgentTab.jsx:996
await in loadAssistantConfiguration
(anonymous) @ AgentTab.jsx:910
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=9711cfb6:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=9711cfb6:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=9711cfb6:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=9711cfb6:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.599Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.600Z'}
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.87756a2c-a398-43f2-889a-b8815684df71&select=* 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
AttorneyProfileManager.js:1483 [AttorneyProfileManager] Error updating attorney in Supabase: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
updateAttorneyInSupabase @ AttorneyProfileManager.js:1483
await in updateAttorneyInSupabase
updateAttorney @ standaloneAttorneyManagerFix.js?t=1750163096374:106
(anonymous) @ useStandaloneAttorney.js:127
handlePreviewConfigUpdate @ DashboardNew.jsx:857
clearAssistantState @ AgentTab.jsx:926
(anonymous) @ AgentTab.jsx:908
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
standaloneAttorneyManagerFix.js?t=1750163096374:113 [StandaloneAttorneyManagerFix] Error updating attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
updateAttorney @ standaloneAttorneyManagerFix.js?t=1750163096374:113
await in updateAttorney
(anonymous) @ useStandaloneAttorney.js:127
handlePreviewConfigUpdate @ DashboardNew.jsx:857
clearAssistantState @ AgentTab.jsx:926
(anonymous) @ AgentTab.jsx:908
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
useStandaloneAttorney.js:131 [useStandaloneAttorney] Error updating attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
(anonymous) @ useStandaloneAttorney.js:131
await in (anonymous)
handlePreviewConfigUpdate @ DashboardNew.jsx:857
clearAssistantState @ AgentTab.jsx:926
(anonymous) @ AgentTab.jsx:908
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
DashboardNew.jsx:861 [DashboardNew] Failed to update attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
handlePreviewConfigUpdate @ DashboardNew.jsx:861
await in handlePreviewConfigUpdate
clearAssistantState @ AgentTab.jsx:926
(anonymous) @ AgentTab.jsx:908
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.638Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.638Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.774Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.774Z'}
@supabase_supabase-js.js?v=9711cfb6:3900 
            
            
           PATCH https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?id=eq.87756a2c-a398-43f2-889a-b8815684df71&select=* 400 (Bad Request)
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3900
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3921
fulfilled @ @supabase_supabase-js.js?v=9711cfb6:3873
Promise.then
step @ @supabase_supabase-js.js?v=9711cfb6:3886
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3888
__awaiter6 @ @supabase_supabase-js.js?v=9711cfb6:3870
(anonymous) @ @supabase_supabase-js.js?v=9711cfb6:3911
then @ @supabase_supabase-js.js?v=9711cfb6:89
AttorneyProfileManager.js:1483 [AttorneyProfileManager] Error updating attorney in Supabase: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
updateAttorneyInSupabase @ AttorneyProfileManager.js:1483
await in updateAttorneyInSupabase
updateAttorney @ standaloneAttorneyManagerFix.js?t=1750163096374:106
(anonymous) @ useStandaloneAttorney.js:127
handlePreviewConfigUpdate @ DashboardNew.jsx:857
loadAssistantConfiguration @ AgentTab.jsx:996
await in loadAssistantConfiguration
(anonymous) @ AgentTab.jsx:910
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
standaloneAttorneyManagerFix.js?t=1750163096374:113 [StandaloneAttorneyManagerFix] Error updating attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
updateAttorney @ standaloneAttorneyManagerFix.js?t=1750163096374:113
await in updateAttorney
(anonymous) @ useStandaloneAttorney.js:127
handlePreviewConfigUpdate @ DashboardNew.jsx:857
loadAssistantConfiguration @ AgentTab.jsx:996
await in loadAssistantConfiguration
(anonymous) @ AgentTab.jsx:910
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
useStandaloneAttorney.js:131 [useStandaloneAttorney] Error updating attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
(anonymous) @ useStandaloneAttorney.js:131
await in (anonymous)
handlePreviewConfigUpdate @ DashboardNew.jsx:857
loadAssistantConfiguration @ AgentTab.jsx:996
await in loadAssistantConfiguration
(anonymous) @ AgentTab.jsx:910
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
DashboardNew.jsx:861 [DashboardNew] Failed to update attorney: {code: 'PGRST204', details: null, hint: null, message: "Could not find the 'mascot' column of 'attorneys' in the schema cache"}
handlePreviewConfigUpdate @ DashboardNew.jsx:861
await in handlePreviewConfigUpdate
loadAssistantConfiguration @ AgentTab.jsx:996
await in loadAssistantConfiguration
(anonymous) @ AgentTab.jsx:910
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=9711cfb6:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=9711cfb6:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=9711cfb6:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=9711cfb6:19475
commitRootImpl @ chunk-Q72EVS5P.js?v=9711cfb6:19444
commitRoot @ chunk-Q72EVS5P.js?v=9711cfb6:19305
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18923
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=9711cfb6:9135
(anonymous) @ chunk-Q72EVS5P.js?v=9711cfb6:18655
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.956Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:32.956Z'}
AttorneyProfileManager.js:1480 [AttorneyProfileManager] Updated attorney in Supabase successfully
standaloneAttorneyManagerFix.js?t=1750163096374:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 6
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
DashboardNew.jsx:858 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T12:25:11.287607+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.238Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.239Z'}
DashboardNew.jsx:653 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:654 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.239Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.240Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.240Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.241Z'}
DashboardNew.jsx:914 Found 0 iframes on the page
DashboardNew.jsx:968 No suitable preview iframe found or contentWindow not accessible
(anonymous) @ DashboardNew.jsx:968
setTimeout
sendConfigToPreviewIframe @ DashboardNew.jsx:903
(anonymous) @ DashboardNew.jsx:687
basicStateReducer @ chunk-Q72EVS5P.js?v=9711cfb6:11723
updateReducer @ chunk-Q72EVS5P.js?v=9711cfb6:11814
updateState @ chunk-Q72EVS5P.js?v=9711cfb6:12041
useState @ chunk-Q72EVS5P.js?v=9711cfb6:12773
useState @ chunk-2N3A5BUM.js?v=9711cfb6:1066
DashboardNew @ DashboardNew.jsx:74
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14602
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:914 Found 0 iframes on the page
DashboardNew.jsx:968 No suitable preview iframe found or contentWindow not accessible
(anonymous) @ DashboardNew.jsx:968
setTimeout
sendConfigToPreviewIframe @ DashboardNew.jsx:903
(anonymous) @ DashboardNew.jsx:687
basicStateReducer @ chunk-Q72EVS5P.js?v=9711cfb6:11723
updateReducer @ chunk-Q72EVS5P.js?v=9711cfb6:11814
updateState @ chunk-Q72EVS5P.js?v=9711cfb6:12041
useState @ chunk-Q72EVS5P.js?v=9711cfb6:12773
useState @ chunk-2N3A5BUM.js?v=9711cfb6:1066
DashboardNew @ DashboardNew.jsx:74
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14607
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
AttorneyProfileManager.js:1480 [AttorneyProfileManager] Updated attorney in Supabase successfully
standaloneAttorneyManagerFix.js?t=1750163096374:46 [StandaloneAttorneyManagerFix] Notifying subscribers: 6
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:88 [useStandaloneAttorney] Manager updated attorney: 87756a2c-a398-43f2-889a-b8815684df71
DashboardNew.jsx:858 [DashboardNew] Attorney updated successfully: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-17T12:25:11.287607+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.735Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.735Z'}
DashboardNew.jsx:653 [DashboardNew] Attorney state updated, updating previewConfig.
DashboardNew.jsx:654 [DashboardNew] Attorney Vapi Assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.736Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.736Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.737Z'}
DashboardNew.jsx:684 [DashboardNew] Updated preview config with assistant ID: 2f157a27-067c-439e-823c-f0a2bbdd66e0
DashboardNew.jsx:900 [DashboardNew] Using fallback iframe communication
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:34.737Z'}
DashboardNew.jsx:914 Found 0 iframes on the page
DashboardNew.jsx:968 No suitable preview iframe found or contentWindow not accessible
(anonymous) @ DashboardNew.jsx:968
setTimeout
sendConfigToPreviewIframe @ DashboardNew.jsx:903
(anonymous) @ DashboardNew.jsx:687
basicStateReducer @ chunk-Q72EVS5P.js?v=9711cfb6:11723
updateReducer @ chunk-Q72EVS5P.js?v=9711cfb6:11814
updateState @ chunk-Q72EVS5P.js?v=9711cfb6:12041
useState @ chunk-Q72EVS5P.js?v=9711cfb6:12773
useState @ chunk-2N3A5BUM.js?v=9711cfb6:1066
DashboardNew @ DashboardNew.jsx:74
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14602
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:914 Found 0 iframes on the page
DashboardNew.jsx:968 No suitable preview iframe found or contentWindow not accessible
(anonymous) @ DashboardNew.jsx:968
setTimeout
sendConfigToPreviewIframe @ DashboardNew.jsx:903
(anonymous) @ DashboardNew.jsx:687
basicStateReducer @ chunk-Q72EVS5P.js?v=9711cfb6:11723
updateReducer @ chunk-Q72EVS5P.js?v=9711cfb6:11814
updateState @ chunk-Q72EVS5P.js?v=9711cfb6:12041
useState @ chunk-Q72EVS5P.js?v=9711cfb6:12773
useState @ chunk-2N3A5BUM.js?v=9711cfb6:1066
DashboardNew @ DashboardNew.jsx:74
renderWithHooks @ chunk-Q72EVS5P.js?v=9711cfb6:11568
updateFunctionComponent @ chunk-Q72EVS5P.js?v=9711cfb6:14607
beginWork @ chunk-Q72EVS5P.js?v=9711cfb6:15944
beginWork$1 @ chunk-Q72EVS5P.js?v=9711cfb6:19781
performUnitOfWork @ chunk-Q72EVS5P.js?v=9711cfb6:19226
workLoopSync @ chunk-Q72EVS5P.js?v=9711cfb6:19165
renderRootSync @ chunk-Q72EVS5P.js?v=9711cfb6:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=9711cfb6:18706
workLoop @ chunk-Q72EVS5P.js?v=9711cfb6:197
flushWork @ chunk-Q72EVS5P.js?v=9711cfb6:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=9711cfb6:384
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:35.546Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:35.547Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:36.046Z'}
DashboardNew.jsx:43 [DashboardNew] 🔍 useAuth() results: {user: true, userEmail: '<EMAIL>', authIsLoading: false, timestamp: '2025-06-17T12:25:36.046Z'}
