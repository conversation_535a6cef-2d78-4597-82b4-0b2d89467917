/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../environments";
import * as core from "../../../../core";
import * as Vapi from "../../../index";
export declare namespace Logs {
    interface Options {
        environment?: core.Supplier<environments.VapiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        fetcher?: core.FetchFunction;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string>;
    }
}
export declare class Logs {
    protected readonly _options: Logs.Options;
    constructor(_options: Logs.Options);
    /**
     * @param {Vapi.LogsGetRequest} request
     * @param {Logs.RequestOptions} requestOptions - Request-specific configuration.
     */
    get(request?: Vapi.LogsGetRequest, requestOptions?: Logs.RequestOptions): Promise<core.Page<Vapi.Log>>;
    /**
     * @param {Vapi.LoggingControllerLogsDeleteQueryRequest} request
     * @param {Logs.RequestOptions} requestOptions - Request-specific configuration.
     */
    loggingControllerLogsDeleteQuery(request?: Vapi.LoggingControllerLogsDeleteQueryRequest, requestOptions?: Logs.RequestOptions): Promise<void>;
    protected _getAuthorizationHeader(): Promise<string>;
}
