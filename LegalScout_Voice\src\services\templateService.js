/**
 * Template Service
 *
 * Manages assistant templates for Vapi configuration
 */

import { supabase } from '../lib/supabase';
import {
  DEFAULT_STRUCTURED_DATA_SCHEMA,
  DEFAULT_SUCCESS_EVALUATION_PROMPT,
  DEFAULT_SUMMARY_PROMPT,
  DEFAULT_STRUCTURED_DATA_PROMPT,
  getTemplateByPracticeArea
} from '../config/defaultTemplates';

/**
 * Get all templates
 * @param {Object} options - Query options
 * @param {boolean} options.includePublic - Whether to include public templates
 * @param {string} options.attorneyId - Filter by attorney ID
 * @param {string} options.category - Filter by category
 * @returns {Promise<Array>} Array of templates
 */
export const getTemplates = async (options = {}) => {
  try {
    const { includePublic = true, attorneyId, category } = options;

    let query = supabase
      .from('assistant_templates')
      .select('*');

    // Apply filters
    if (attorneyId) {
      query = query.eq('attorney_id', attorneyId);
    }

    if (category) {
      query = query.eq('category', category);
    }

    if (includePublic) {
      if (attorneyId) {
        // Get templates owned by the attorney OR public templates
        query = query.or(`attorney_id.eq.${attorneyId},is_public.eq.true`);
      } else {
        // Include public templates
        query = query.or('is_public.eq.true');
      }
    }

    // Order by creation date
    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting templates:', error);
    throw error;
  }
};

/**
 * Get a template by ID
 * @param {string} templateId - Template ID
 * @returns {Promise<Object>} Template object
 */
export const getTemplateById = async (templateId) => {
  try {
    const { data, error } = await supabase
      .from('assistant_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting template:', error);
    throw error;
  }
};

/**
 * Create a new template
 * @param {Object} template - Template data
 * @returns {Promise<Object>} Created template
 */
export const createTemplate = async (template) => {
  try {
    const { data, error } = await supabase
      .from('assistant_templates')
      .insert(template)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating template:', error);
    throw error;
  }
};

/**
 * Update a template
 * @param {string} templateId - Template ID
 * @param {Object} updates - Template updates
 * @returns {Promise<Object>} Updated template
 */
export const updateTemplate = async (templateId, updates) => {
  try {
    const { data, error } = await supabase
      .from('assistant_templates')
      .update(updates)
      .eq('id', templateId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating template:', error);
    throw error;
  }
};

/**
 * Delete a template
 * @param {string} templateId - Template ID
 * @returns {Promise<boolean>} Success status
 */
export const deleteTemplate = async (templateId) => {
  try {
    const { error } = await supabase
      .from('assistant_templates')
      .delete()
      .eq('id', templateId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
};

/**
 * Save current assistant configuration as a template
 * @param {Object} attorney - Attorney object
 * @param {Object} assistant - Assistant object from Vapi
 * @param {Object} options - Template options
 * @param {string} options.name - Template name
 * @param {string} options.description - Template description
 * @param {string} options.category - Template category
 * @param {boolean} options.isPublic - Whether the template is public
 * @returns {Promise<Object>} Created template
 */
export const saveAsTemplate = async (attorney, assistant, options) => {
  try {
    const { name, description, category, isPublic = false } = options;

    // Create template object
    const template = {
      name,
      description,
      attorney_id: attorney.id,
      category,
      is_public: isPublic,

      // Template configuration
      success_prompt: attorney.success_evaluation_prompt,
      summary_prompt: attorney.summary_prompt,
      structured_data_prompt: attorney.structured_data_prompt,
      structured_data_schema: attorney.structured_data_schema,

      // Assistant configuration
      assistant_config: assistant
    };

    return createTemplate(template);
  } catch (error) {
    console.error('Error saving as template:', error);
    throw error;
  }
};

/**
 * Apply a template to an attorney
 * @param {Object} attorney - Attorney object to update
 * @param {string} templateId - Template ID to apply
 * @returns {Promise<Object>} Updated attorney object
 */
export const applyTemplate = async (attorney, templateId) => {
  try {
    // Get the template
    const template = await getTemplateById(templateId);

    if (!template) {
      throw new Error('Template not found');
    }

    // Update attorney with template values
    const updates = {
      success_evaluation_prompt: template.success_prompt,
      summary_prompt: template.summary_prompt,
      structured_data_prompt: template.structured_data_prompt,
      structured_data_schema: template.structured_data_schema,
      updated_at: new Date().toISOString()
    };

    // Update attorney in Supabase
    const { data, error } = await supabase
      .from('attorneys')
      .update(updates)
      .eq('id', attorney.id)
      .select()
      .single();

    if (error) throw error;

    return {
      attorney: data,
      template,
      assistantConfig: template.assistant_config
    };
  } catch (error) {
    console.error('Error applying template:', error);
    throw error;
  }
};

/**
 * Get default template values
 * @param {string} practiceArea - Practice area to get template for
 * @returns {Object} Default template values
 */
export const getDefaultTemplateValues = (practiceArea) => {
  // Get practice area specific template if available
  const template = getTemplateByPracticeArea(practiceArea);

  return {
    structured_data_schema: template.structured_data_schema || DEFAULT_STRUCTURED_DATA_SCHEMA,
    success_evaluation_prompt: template.success_prompt || DEFAULT_SUCCESS_EVALUATION_PROMPT,
    summary_prompt: template.summary_prompt || DEFAULT_SUMMARY_PROMPT,
    structured_data_prompt: template.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT
  };
};
