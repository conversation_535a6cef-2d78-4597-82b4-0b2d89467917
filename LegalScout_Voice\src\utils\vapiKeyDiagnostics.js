/**
 * Vapi API Key Runtime Diagnostics
 * 
 * This utility provides comprehensive diagnostics for Vapi API key issues
 * that can be run in both development and production environments.
 */

import { getVapiApiKey, getVapiConfig } from '../config/vapiConfig.js';

/**
 * Comprehensive diagnostic report for Vapi API key configuration
 */
export const runVapiKeyDiagnostics = async () => {
  const report = {
    timestamp: new Date().toISOString(),
    environment: getEnvironmentInfo(),
    environmentVariables: getEnvironmentVariables(),
    apiKeys: getApiKeyInfo(),
    configValidation: validateConfiguration(),
    networkTests: await runNetworkTests(),
    recommendations: []
  };

  // Generate recommendations based on findings
  report.recommendations = generateRecommendations(report);

  return report;
};

/**
 * Get environment information
 */
function getEnvironmentInfo() {
  const info = {
    nodeEnv: null,
    mode: null,
    hostname: null,
    userAgent: null,
    isProduction: false,
    isDevelopment: false,
    platform: null
  };

  // Node.js environment
  if (typeof process !== 'undefined') {
    info.nodeEnv = process.env.NODE_ENV;
    info.platform = 'node';
  }

  // Browser environment
  if (typeof window !== 'undefined') {
    info.hostname = window.location?.hostname;
    info.userAgent = navigator?.userAgent;
    info.platform = 'browser';
  }

  // Vite environment
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    info.mode = import.meta.env.MODE;
  }

  // Determine environment type
  info.isDevelopment = 
    info.nodeEnv === 'development' || 
    info.mode === 'development' || 
    info.hostname === 'localhost';
  
  info.isProduction = 
    info.nodeEnv === 'production' || 
    info.mode === 'production' || 
    (info.hostname && info.hostname !== 'localhost');

  return info;
}

/**
 * Get all relevant environment variables
 */
function getEnvironmentVariables() {
  const vars = {};
  
  const envVarNames = [
    'VITE_VAPI_PUBLIC_KEY',
    'VITE_VAPI_SECRET_KEY',
    'VITE_VAPI_PRIVATE_KEY',
    'VAPI_PUBLIC_KEY',
    'VAPI_SECRET_KEY',
    'VAPI_PRIVATE_KEY',
    'VAPI_TOKEN',
    'NODE_ENV',
    'MODE'
  ];

  envVarNames.forEach(varName => {
    let value = null;

    // Try import.meta.env first
    if (typeof import.meta !== 'undefined' && import.meta.env) {
      value = import.meta.env[varName];
    }

    // Try process.env
    if (!value && typeof process !== 'undefined' && process.env) {
      value = process.env[varName];
    }

    // Try window (runtime injection)
    if (!value && typeof window !== 'undefined') {
      value = window[varName];
    }

    vars[varName] = {
      available: !!value,
      source: value ? getValueSource(varName) : null,
      preview: value ? value.substring(0, 8) + '...' : null
    };
  });

  return vars;
}

/**
 * Determine where an environment variable value came from
 */
function getValueSource(varName) {
  if (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env[varName]) {
    return 'import.meta.env';
  }
  if (typeof process !== 'undefined' && process.env && process.env[varName]) {
    return 'process.env';
  }
  if (typeof window !== 'undefined' && window[varName]) {
    return 'window';
  }
  return 'unknown';
}

/**
 * Get API key information from the configuration
 */
function getApiKeyInfo() {
  try {
    const config = getVapiConfig();
    
    return {
      publicKey: {
        value: config.publicKey ? config.publicKey.substring(0, 8) + '...' : null,
        available: !!config.publicKey,
        isCorrect: config.publicKey === '310f0d43-27c2-47a5-a76d-e55171d024f7'
      },
      secretKey: {
        value: config.secretKey ? config.secretKey.substring(0, 8) + '...' : null,
        available: !!config.secretKey,
        isCorrect: config.secretKey === '6734febc-fc65-4669-93b0-929b31ff6564'
      },
      clientKey: {
        value: getVapiApiKey('client'),
        preview: getVapiApiKey('client') ? getVapiApiKey('client').substring(0, 8) + '...' : null
      },
      serverKey: {
        value: getVapiApiKey('server'),
        preview: getVapiApiKey('server') ? getVapiApiKey('server').substring(0, 8) + '...' : null
      }
    };
  } catch (error) {
    return {
      error: error.message,
      stack: error.stack
    };
  }
}

/**
 * Validate the current configuration
 */
function validateConfiguration() {
  const validation = {
    hasPublicKey: false,
    hasSecretKey: false,
    keysMatch: false,
    configValid: false,
    errors: []
  };

  try {
    const config = getVapiConfig();
    
    validation.hasPublicKey = !!config.publicKey;
    validation.hasSecretKey = !!config.secretKey;
    
    if (!validation.hasPublicKey) {
      validation.errors.push('Public key is missing');
    }
    
    if (!validation.hasSecretKey) {
      validation.errors.push('Secret key is missing');
    }
    
    // Check if keys are correct
    if (config.publicKey !== '310f0d43-27c2-47a5-a76d-e55171d024f7') {
      validation.errors.push('Public key does not match expected value');
    }
    
    if (config.secretKey !== '6734febc-fc65-4669-93b0-929b31ff6564') {
      validation.errors.push('Secret key does not match expected value');
    }
    
    validation.keysMatch = 
      config.publicKey === '310f0d43-27c2-47a5-a76d-e55171d024f7' &&
      config.secretKey === '6734febc-fc65-4669-93b0-929b31ff6564';
    
    validation.configValid = validation.hasPublicKey && validation.hasSecretKey && validation.keysMatch;
    
  } catch (error) {
    validation.errors.push(`Configuration error: ${error.message}`);
  }

  return validation;
}

/**
 * Run network tests to verify API connectivity
 */
async function runNetworkTests() {
  const tests = {
    publicKeyTest: null,
    secretKeyTest: null,
    phoneNumbersTest: null
  };

  const publicKey = getVapiApiKey('client');
  const secretKey = getVapiApiKey('server');

  // Test public key (should fail for server operations)
  if (publicKey) {
    try {
      const response = await fetch('https://api.vapi.ai/phone-number', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${publicKey}`,
          'Content-Type': 'application/json'
        }
      });

      tests.publicKeyTest = {
        status: response.status,
        ok: response.ok,
        statusText: response.statusText,
        expected: 'Should fail with 401 for server operations'
      };
    } catch (error) {
      tests.publicKeyTest = {
        error: error.message,
        expected: 'Should fail with 401 for server operations'
      };
    }
  }

  // Test secret key (should succeed for server operations)
  if (secretKey) {
    try {
      const response = await fetch('https://api.vapi.ai/phone-number', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${secretKey}`,
          'Content-Type': 'application/json'
        }
      });

      tests.secretKeyTest = {
        status: response.status,
        ok: response.ok,
        statusText: response.statusText,
        expected: 'Should succeed with 200 for server operations'
      };

      if (response.ok) {
        const data = await response.json();
        tests.phoneNumbersTest = {
          success: true,
          phoneNumberCount: Array.isArray(data) ? data.length : 0,
          data: Array.isArray(data) ? data.map(p => ({ id: p.id, number: p.number })) : null
        };
      }
    } catch (error) {
      tests.secretKeyTest = {
        error: error.message,
        expected: 'Should succeed with 200 for server operations'
      };
    }
  }

  return tests;
}

/**
 * Generate recommendations based on diagnostic findings
 */
function generateRecommendations(report) {
  const recommendations = [];

  // Environment variable issues
  if (!report.environmentVariables.VITE_VAPI_PUBLIC_KEY?.available) {
    recommendations.push({
      type: 'error',
      message: 'VITE_VAPI_PUBLIC_KEY environment variable is missing',
      action: 'Add VITE_VAPI_PUBLIC_KEY=310f0d43-27c2-47a5-a76d-e55171d024f7 to your environment variables'
    });
  }

  if (!report.environmentVariables.VITE_VAPI_SECRET_KEY?.available) {
    recommendations.push({
      type: 'error',
      message: 'VITE_VAPI_SECRET_KEY environment variable is missing',
      action: 'Add VITE_VAPI_SECRET_KEY=6734febc-fc65-4669-93b0-929b31ff6564 to your environment variables'
    });
  }

  // API key validation issues
  if (!report.apiKeys.publicKey?.isCorrect) {
    recommendations.push({
      type: 'error',
      message: 'Public API key is incorrect',
      action: 'Verify that the public key matches: 310f0d43-27c2-47a5-a76d-e55171d024f7'
    });
  }

  if (!report.apiKeys.secretKey?.isCorrect) {
    recommendations.push({
      type: 'error',
      message: 'Secret API key is incorrect',
      action: 'Verify that the secret key matches: 6734febc-fc65-4669-93b0-929b31ff6564'
    });
  }

  // Network test issues
  if (report.networkTests.secretKeyTest && !report.networkTests.secretKeyTest.ok) {
    recommendations.push({
      type: 'error',
      message: `Secret key test failed: ${report.networkTests.secretKeyTest.status} ${report.networkTests.secretKeyTest.statusText}`,
      action: 'Check if the secret key is being used correctly for server operations'
    });
  }

  if (report.networkTests.publicKeyTest && report.networkTests.publicKeyTest.ok) {
    recommendations.push({
      type: 'warning',
      message: 'Public key succeeded for server operations (unexpected)',
      action: 'This might indicate key confusion - public keys should not work for server operations'
    });
  }

  // Production-specific recommendations
  if (report.environment.isProduction) {
    recommendations.push({
      type: 'info',
      message: 'Running in production environment',
      action: 'Ensure environment variables are properly set in your deployment platform (Vercel, etc.)'
    });
  }

  return recommendations;
}

/**
 * Console-friendly diagnostic report
 */
export const logDiagnosticReport = async () => {
  console.log('🔍 Running Vapi API Key Diagnostics...');
  
  const report = await runVapiKeyDiagnostics();
  
  console.log('\n📊 Diagnostic Report:');
  console.log('='.repeat(50));
  
  console.log('\n🌍 Environment:', report.environment);
  console.log('\n🔑 API Keys:', report.apiKeys);
  console.log('\n✅ Validation:', report.configValidation);
  console.log('\n🌐 Network Tests:', report.networkTests);
  
  console.log('\n💡 Recommendations:');
  report.recommendations.forEach((rec, index) => {
    const icon = rec.type === 'error' ? '❌' : rec.type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${icon} ${index + 1}. ${rec.message}`);
    console.log(`   Action: ${rec.action}`);
  });
  
  console.log('\n' + '='.repeat(50));
  
  return report;
};

export default {
  runVapiKeyDiagnostics,
  logDiagnosticReport
};
