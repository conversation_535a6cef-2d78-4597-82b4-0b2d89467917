"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VapiTimeoutError = exports.VapiError = exports.VapiEnvironment = exports.VapiClient = exports.Vapi = void 0;
exports.Vapi = __importStar(require("./api"));
var Client_1 = require("./Client");
Object.defineProperty(exports, "VapiClient", { enumerable: true, get: function () { return Client_1.VapiClient; } });
var environments_1 = require("./environments");
Object.defineProperty(exports, "VapiEnvironment", { enumerable: true, get: function () { return environments_1.VapiEnvironment; } });
var errors_1 = require("./errors");
Object.defineProperty(exports, "VapiError", { enumerable: true, get: function () { return errors_1.VapiError; } });
Object.defineProperty(exports, "VapiTimeoutError", { enumerable: true, get: function () { return errors_1.VapiTimeoutError; } });
