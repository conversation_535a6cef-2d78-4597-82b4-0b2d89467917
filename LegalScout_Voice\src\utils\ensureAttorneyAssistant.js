/**
 * Utility function to ensure an attorney has a valid Vapi assistant
 * 
 * This utility provides a simple interface for ensuring that an attorney
 * has a valid Vapi assistant ID, creating one if needed.
 */

import { supabase } from '../lib/supabase';
import { vapiAssistantManager } from '../services/vapiAssistantManager';
import { mcpConfig } from '../config/mcp.config';

/**
 * Initialize the Vapi assistant manager
 * @param {Object} options - Configuration options
 * @returns {Promise<boolean>} - Whether initialization was successful
 */
export const initializeVapiAssistantManager = async (options = {}) => {
  try {
    // Get API key from options or config
    const apiKey = options.apiKey || 
                  mcpConfig.voice.vapi.secretKey || 
                  mcpConfig.voice.vapi.publicKey;
    
    // Initialize with direct API if key is provided
    if (apiKey) {
      return await vapiAssistantManager.initialize({
        directApiKey: apiKey
      });
    }
    
    // Otherwise use MCP
    return await vapiAssistantManager.initialize({
      mcpUrl: options.mcpUrl || mcpConfig.voice.vapi.mcpUrl
    });
  } catch (error) {
    console.error('Error initializing Vapi assistant manager:', error);
    return false;
  }
};

/**
 * Ensure an attorney has a valid Vapi assistant ID
 * @param {string} attorneyId - The attorney ID
 * @returns {Promise<Object>} - The attorney with a valid assistant ID
 */
export const ensureAttorneyAssistant = async (attorneyId) => {
  try {
    // Get attorney from Supabase
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorneyId)
      .single();
    
    if (error) {
      throw error;
    }
    
    if (!attorney) {
      throw new Error(`Attorney with ID ${attorneyId} not found`);
    }
    
    // Initialize Vapi assistant manager if needed
    if (!vapiAssistantManager.mcpConnected && !vapiAssistantManager.useDirect) {
      await initializeVapiAssistantManager();
    }
    
    // Ensure attorney has a valid assistant ID
    return await vapiAssistantManager.ensureAssistant(attorney);
  } catch (error) {
    console.error(`Error ensuring assistant for attorney ${attorneyId}:`, error);
    throw error;
  }
};

/**
 * Ensure an attorney has a valid Vapi assistant ID by email
 * @param {string} email - The attorney email
 * @returns {Promise<Object>} - The attorney with a valid assistant ID
 */
export const ensureAttorneyAssistantByEmail = async (email) => {
  try {
    // Get attorney from Supabase
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', email)
      .single();
    
    if (error) {
      throw error;
    }
    
    if (!attorney) {
      throw new Error(`Attorney with email ${email} not found`);
    }
    
    // Initialize Vapi assistant manager if needed
    if (!vapiAssistantManager.mcpConnected && !vapiAssistantManager.useDirect) {
      await initializeVapiAssistantManager();
    }
    
    // Ensure attorney has a valid assistant ID
    return await vapiAssistantManager.ensureAssistant(attorney);
  } catch (error) {
    console.error(`Error ensuring assistant for attorney with email ${email}:`, error);
    throw error;
  }
};

/**
 * Ensure an attorney has a valid Vapi assistant ID by subdomain
 * @param {string} subdomain - The attorney subdomain
 * @returns {Promise<Object>} - The attorney with a valid assistant ID
 */
export const ensureAttorneyAssistantBySubdomain = async (subdomain) => {
  try {
    // Get attorney from Supabase
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('subdomain', subdomain)
      .single();
    
    if (error) {
      throw error;
    }
    
    if (!attorney) {
      throw new Error(`Attorney with subdomain ${subdomain} not found`);
    }
    
    // Initialize Vapi assistant manager if needed
    if (!vapiAssistantManager.mcpConnected && !vapiAssistantManager.useDirect) {
      await initializeVapiAssistantManager();
    }
    
    // Ensure attorney has a valid assistant ID
    return await vapiAssistantManager.ensureAssistant(attorney);
  } catch (error) {
    console.error(`Error ensuring assistant for attorney with subdomain ${subdomain}:`, error);
    throw error;
  }
};

/**
 * Sync an attorney's Vapi assistant with the latest data from Supabase
 * @param {string} attorneyId - The attorney ID
 * @returns {Promise<Object>} - The sync result
 */
export const syncAttorneyAssistant = async (attorneyId) => {
  try {
    // Get attorney from Supabase
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorneyId)
      .single();
    
    if (error) {
      throw error;
    }
    
    if (!attorney) {
      throw new Error(`Attorney with ID ${attorneyId} not found`);
    }
    
    // Initialize Vapi assistant manager if needed
    if (!vapiAssistantManager.mcpConnected && !vapiAssistantManager.useDirect) {
      await initializeVapiAssistantManager();
    }
    
    // Sync the assistant
    return await vapiAssistantManager.syncAssistant(attorney);
  } catch (error) {
    console.error(`Error syncing assistant for attorney ${attorneyId}:`, error);
    throw error;
  }
};
