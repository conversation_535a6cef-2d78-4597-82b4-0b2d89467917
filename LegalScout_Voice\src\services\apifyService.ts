import { ApifyClient } from 'apify-client';

/**
 * Service for interacting with the Apify API to search for attorneys
 */
export class ApifyService {
  private client: ApifyClient;
  private actorId = '1lSvMAaRcadrM1Vgv';
  
  constructor() {
    this.client = new ApifyClient({
      token: '**********************************************',
    });
  }
  
  /**
   * Search for attorneys based on location and practice area
   * @param location The location to search around
   * @param practiceArea The type of legal practice
   * @param radius Search radius in miles
   */
  async searchAttorneys(location: string, practiceArea: string, radius = 25) {
    try {
      const input = {
        queries: [`${practiceArea} attorney near ${location}`],
        maxPagesPerQuery: 1,
        includeReviews: true,
        includeContactInformation: true,
        includeOpeningHours: false,
        includePeopleAlsoSearch: false,
        maxCrawledPlacesPerSearch: 10,
        enrichPlaceUrls: true,
        language: "en",
        searchCountry: "US",
        searchCoordinates: null,
        searchRadius: radius * 1609.34, // Convert miles to meters
      };
      
      const response = await this.client.actor(this.actorId).call(input);
      return this.formatResults(response);
    } catch (error) {
      console.error('Apify API error:', error);
      throw new Error('Failed to search for attorneys');
    }
  }
  
  /**
   * Format the API results into a consistent attorney format
   */
  private formatResults(response: any) {
    // Process results here
    return [];
  }
}

// Create a singleton instance
export const apifyService = new ApifyService(); 