/**
 * Website Scraper Utility
 *
 * Uses AI Meta MCP server to extract branding information
 * from law firm websites for configuring LegalScout agents.
 *
 * This implementation uses the web evaluation tools from the AI Meta MCP server
 * to scrape attorney websites and extract profile information.
 *
 * Enhanced with 1-click attorney configuration capabilities using:
 * - Firecrawl for advanced content extraction
 * - OpenAI for intelligent data processing
 * - Automated Vapi assistant configuration
 */

/**
 * Enhanced 1-click attorney configuration
 *
 * @param {string} url - The website URL to scrape
 * @param {Object} options - Configuration options
 * @param {boolean} options.useFirecrawl - Use Firecrawl for enhanced extraction
 * @param {boolean} options.useAI - Use AI for intelligent processing
 * @param {boolean} options.autoConfig - Automatically configure Vapi assistant
 * @returns {Promise<object>} - Complete attorney configuration
 */
export const oneClickAttorneyConfig = async (url, options = {}) => {
  const { useFirecrawl = true, useAI = true, autoConfig = true } = options;

  try {
    // Step 1: Enhanced web scraping with multiple methods
    const scrapedData = await enhancedWebScraping(url, { useFirecrawl, useAI });

    // Step 2: AI-powered data processing and enhancement
    const processedData = useAI ? await aiEnhanceData(scrapedData) : scrapedData;

    // Step 3: Generate complete attorney configuration
    const attorneyConfig = await generateAttorneyConfiguration(processedData);

    // Step 4: Auto-configure Vapi assistant if requested
    if (autoConfig) {
      attorneyConfig.vapiAssistant = await autoConfigureVapiAssistant(attorneyConfig);
    }

    return attorneyConfig;
  } catch (error) {
    console.error('1-click attorney configuration failed:', error);
    throw error;
  }
};

/**
 * Main scraper function to extract information from a law firm website
 *
 * @param {string} url - The website URL to scrape
 * @returns {Promise<object>} - Extracted website data
 */
export const scrapeWebsite = async (url) => {
  try {
    // Step 1: Validate and format the URL
    const formattedUrl = formatUrl(url);

    // Step 2: Create the evaluation script for the AI Meta MCP server
    const evaluationScript = `
      // Extract attorney profile information
      function extractAttorneyProfile() {
        const doc = document;
        const result = {
          firmName: '',
          attorneyName: '',
          practiceAreas: [],
          description: '',
          contactInfo: {
            phone: '',
            email: '',
            address: ''
          },
          socialMedia: {},
          visualElements: {
            logo: '',
            colors: {
              primary: '',
              secondary: '',
              accent: '',
              background: '',
              text: ''
            },
            fonts: {
              heading: '',
              body: ''
            },
            images: []
          },
          welcomeMessage: '',
          informationGathering: '',
          buttonText: '',
          state: '',
          subdomain: ''
        };

        try {
          // Extract firm name
          const possibleFirmNameElements = [
            doc.querySelector('header h1, header .logo, #header h1, #header .logo'),
            doc.querySelector('.firm-name, .logo-text, .site-title, .brand'),
            doc.querySelector('h1'),
            doc.querySelector('title')
          ].filter(Boolean);

          if (possibleFirmNameElements.length > 0) {
            result.firmName = possibleFirmNameElements[0].textContent.trim();
          }

          // Extract attorney name
          const possibleAttorneyElements = [
            doc.querySelector('.attorney-name, .lawyer-name, .profile-name'),
            doc.querySelector('h1:not(.firm-name), h2:not(.firm-name)'),
            doc.querySelector('header h2, #header h2')
          ].filter(Boolean);

          if (possibleAttorneyElements.length > 0) {
            result.attorneyName = possibleAttorneyElements[0].textContent.trim();
          }

          // Extract practice areas
          const practiceAreaElements = Array.from(
            doc.querySelectorAll('.practice-areas li, .areas-of-practice li, .services li, .practice-areas a, .areas-of-practice a')
          );

          if (practiceAreaElements.length > 0) {
            result.practiceAreas = practiceAreaElements.map(el => el.textContent.trim());
          } else {
            // Try to find practice areas in headings
            const headings = Array.from(doc.querySelectorAll('h2, h3'));
            const practiceHeadings = headings.filter(h =>
              /practice|areas|services/i.test(h.textContent)
            );

            if (practiceHeadings.length > 0) {
              // Get the next element after the heading
              const practiceList = practiceHeadings[0].nextElementSibling;
              if (practiceList && practiceList.tagName === 'UL') {
                result.practiceAreas = Array.from(practiceList.querySelectorAll('li'))
                  .map(li => li.textContent.trim());
              }
            }
          }

          // Extract description
          const possibleDescriptionElements = [
            doc.querySelector('.about-us p, .about p, .bio p, .profile p, .description p'),
            doc.querySelector('main p, #main p, .main p'),
            doc.querySelector('p')
          ].filter(Boolean);

          if (possibleDescriptionElements.length > 0) {
            result.description = possibleDescriptionElements[0].textContent.trim();
          }

          // Extract contact information
          // Phone
          const phoneRegex = /\\(\\d{3}\\)\\s?\\d{3}-\\d{4}|\\d{3}-\\d{3}-\\d{4}/;
          const phoneElements = Array.from(doc.querySelectorAll('a[href^="tel:"], .phone, .contact .phone, footer .phone'));

          if (phoneElements.length > 0) {
            result.contactInfo.phone = phoneElements[0].textContent.trim();
          } else {
            // Try to find phone in text
            const allText = doc.body.textContent;
            const phoneMatch = allText.match(phoneRegex);
            if (phoneMatch) {
              result.contactInfo.phone = phoneMatch[0];
            }
          }

          // Email
          const emailElements = Array.from(doc.querySelectorAll('a[href^="mailto:"], .email, .contact .email, footer .email'));

          if (emailElements.length > 0) {
            result.contactInfo.email = emailElements[0].textContent.trim();
          }

          // Address
          const addressElements = Array.from(doc.querySelectorAll('.address, .contact .address, footer .address, address'));

          if (addressElements.length > 0) {
            result.contactInfo.address = addressElements[0].textContent.trim();
          }

          // Social media
          const socialLinks = Array.from(doc.querySelectorAll('a[href*="facebook.com"], a[href*="twitter.com"], a[href*="linkedin.com"], a[href*="instagram.com"]'));

          socialLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href.includes('facebook.com')) {
              result.socialMedia.facebook = href;
            } else if (href.includes('twitter.com')) {
              result.socialMedia.twitter = href;
            } else if (href.includes('linkedin.com')) {
              result.socialMedia.linkedin = href;
            } else if (href.includes('instagram.com')) {
              result.socialMedia.instagram = href;
            }
          });

          // Extract logo URL
          const logoElement = doc.querySelector('header img, #header img, .logo img, .site-logo img');
          if (logoElement) {
            const logoSrc = logoElement.getAttribute('src');
            if (logoSrc) {
              // Convert relative URL to absolute
              const logoUrl = new URL(logoSrc, window.location.origin).href;
              result.visualElements.logo = logoUrl;
            }
          }

          // Extract colors
          const computedStyles = {
            headerBg: window.getComputedStyle(doc.querySelector('header, #header') || doc.body).backgroundColor,
            headerColor: window.getComputedStyle(doc.querySelector('header, #header') || doc.body).color,
            buttonBg: window.getComputedStyle(doc.querySelector('button, .button, a.btn, .cta, .btn-primary') || doc.body).backgroundColor,
            buttonColor: window.getComputedStyle(doc.querySelector('button, .button, a.btn, .cta, .btn-primary') || doc.body).color,
            linkColor: window.getComputedStyle(doc.querySelector('a') || doc.body).color,
            accentColor: window.getComputedStyle(doc.querySelector('.accent, .highlight, .featured, .cta, .btn-secondary') || doc.body).backgroundColor,
            bodyBg: window.getComputedStyle(doc.body).backgroundColor,
            bodyColor: window.getComputedStyle(doc.body).color,
            navBg: window.getComputedStyle(doc.querySelector('nav, .nav, .navbar, .navigation') || doc.body).backgroundColor,
            footerBg: window.getComputedStyle(doc.querySelector('footer, .footer') || doc.body).backgroundColor
          };

          // Use the most prominent colors
          const colors = Object.entries(computedStyles)
            .filter(([_, value]) => value !== 'transparent' && value !== 'rgba(0, 0, 0, 0)')
            .map(([key, value]) => ({ key, value }));

          if (colors.length > 0) {
            // Primary color (from header or buttons)
            const primaryColorCandidates = colors.filter(c =>
              c.key === 'headerBg' || c.key === 'buttonBg' || c.key === 'navBg'
            );
            if (primaryColorCandidates.length > 0) {
              result.visualElements.colors.primary = primaryColorCandidates[0].value;
            }

            // Secondary color (from links)
            const secondaryColorCandidates = colors.filter(c =>
              c.key === 'linkColor' || c.key === 'buttonColor'
            );
            if (secondaryColorCandidates.length > 0) {
              result.visualElements.colors.secondary = secondaryColorCandidates[0].value;
            }

            // Accent color
            const accentColorCandidates = colors.filter(c =>
              c.key === 'accentColor' || c.key === 'footerBg'
            );
            if (accentColorCandidates.length > 0) {
              result.visualElements.colors.accent = accentColorCandidates[0].value;
            } else if (secondaryColorCandidates.length > 0) {
              // Use secondary as accent if no accent color found
              result.visualElements.colors.accent = secondaryColorCandidates[0].value;
            }

            // Background color
            const bgColorCandidates = colors.filter(c =>
              c.key === 'bodyBg'
            );
            if (bgColorCandidates.length > 0) {
              result.visualElements.colors.background = bgColorCandidates[0].value;
            }

            // Text color
            const textColorCandidates = colors.filter(c =>
              c.key === 'bodyColor' || c.key === 'headerColor'
            );
            if (textColorCandidates.length > 0) {
              result.visualElements.colors.text = textColorCandidates[0].value;
            }
          }

          // Extract fonts
          const fontElements = {
            heading: doc.querySelector('h1, h2, .heading, .title'),
            body: doc.querySelector('p, .body, article, main')
          };

          if (fontElements.heading) {
            const headingStyle = window.getComputedStyle(fontElements.heading);
            result.visualElements.fonts.heading = headingStyle.fontFamily;
          }

          if (fontElements.body) {
            const bodyStyle = window.getComputedStyle(fontElements.body);
            result.visualElements.fonts.body = bodyStyle.fontFamily;
          }

          // Extract background images
          const backgroundElements = [
            doc.querySelector('header, #header, .hero, .banner, .jumbotron'),
            doc.querySelector('body'),
            doc.querySelector('main, .main-content')
          ].filter(Boolean);

          backgroundElements.forEach(element => {
            const style = window.getComputedStyle(element);
            const backgroundImage = style.backgroundImage;

            if (backgroundImage && backgroundImage !== 'none') {
              // Extract URL from the background-image property
              const urlMatch = backgroundImage.match(/url\\(['"]?([^'"\\)]+)['"]?\\)/);
              if (urlMatch && urlMatch[1]) {
                const imageUrl = new URL(urlMatch[1], window.location.origin).href;
                result.visualElements.images.push({
                  url: imageUrl,
                  type: 'background',
                  element: element.tagName.toLowerCase()
                });
              }
            }
          });

          // Extract other images
          const contentImages = Array.from(doc.querySelectorAll('img')).filter(img => {
            const rect = img.getBoundingClientRect();
            // Only include reasonably sized images
            return rect.width > 200 && rect.height > 100;
          });

          contentImages.forEach(img => {
            const src = img.getAttribute('src');
            if (src) {
              const imageUrl = new URL(src, window.location.origin).href;
              result.visualElements.images.push({
                url: imageUrl,
                type: 'content',
                alt: img.getAttribute('alt') || ''
              });
            }
          });

          // Generate welcome message
          result.welcomeMessage = generateWelcomeMessage(result);

          // Generate information gathering prompt
          result.informationGathering = generateInformationGatheringPrompt(result);

          // Generate button text
          result.buttonText = generateButtonText(result);

          // Generate suggested subdomain
          result.subdomain = generateSubdomain(result.firmName);

          // Extract state from address if available
          if (result.contactInfo && result.contactInfo.address) {
            const stateMatch = result.contactInfo.address.match(/[A-Z]{2}/);
            if (stateMatch) {
              result.state = stateMatch[0];
            }
          }

          // Generate a suggested prompt based on the extracted data
          result.suggestedPrompt = generatePromptFromData(result);
        } catch (error) {
          console.error('Error extracting attorney profile:', error);
        }

        return result;
      }

      // Helper function to generate a welcome message
      function generateWelcomeMessage(data) {
        let message = '';

        if (data.firmName) {
          message = 'Welcome to ' + data.firmName + '! ';
        } else {
          message = 'Welcome to our legal assistant! ';
        }

        if (data.practiceAreas && data.practiceAreas.length > 0) {
          if (data.practiceAreas.length === 1) {
            message += 'We specialize in ' + data.practiceAreas[0] + '. ';
          } else if (data.practiceAreas.length === 2) {
            message += 'We specialize in ' + data.practiceAreas[0] + ' and ' + data.practiceAreas[1] + '. ';
          } else {
            const lastArea = data.practiceAreas[data.practiceAreas.length - 1];
            const otherAreas = data.practiceAreas.slice(0, -1).join(', ');
            message += 'We specialize in ' + otherAreas + ', and ' + lastArea + '. ';
          }
        }

        message += 'How can we assist you today?';

        return message;
      }

      // Helper function to generate an information gathering prompt
      function generateInformationGatheringPrompt(data) {
        let prompt = 'To better assist you, I need to gather some information. ';

        if (data.practiceAreas && data.practiceAreas.length > 0) {
          prompt += 'Could you please tell me about your situation';

          // Add specific questions based on practice areas
          if (data.practiceAreas.some(area => /personal injury|accident|injury/i.test(area))) {
            prompt += ', including when and how the injury occurred';
          } else if (data.practiceAreas.some(area => /family|divorce/i.test(area))) {
            prompt += ' and what family law matter you need assistance with';
          } else if (data.practiceAreas.some(area => /criminal|defense/i.test(area))) {
            prompt += ' and what legal charges or issues you're facing';
          } else if (data.practiceAreas.some(area => /estate|will|trust/i.test(area))) {
            prompt += ' and what estate planning needs you have';
          } else if (data.practiceAreas.some(area => /business|corporate/i.test(area))) {
            prompt += ' and what business legal matters you need help with';
          } else if (data.practiceAreas.some(area => /immigration/i.test(area))) {
            prompt += ' and what immigration matter you need assistance with';
          }

          prompt += '?';
        } else {
          prompt += 'Could you please tell me about your legal situation and how we might be able to help you?';
        }

        return prompt;
      }

      // Helper function to generate button text
      function generateButtonText(data) {
        // Default button text
        let buttonText = 'Start Consultation';

        // Customize based on practice areas if available
        if (data.practiceAreas && data.practiceAreas.length > 0) {
          if (data.practiceAreas.some(area => /personal injury|accident|injury/i.test(area))) {
            buttonText = 'Discuss Your Case';
          } else if (data.practiceAreas.some(area => /family|divorce/i.test(area))) {
            buttonText = 'Get Family Law Help';
          } else if (data.practiceAreas.some(area => /criminal|defense/i.test(area))) {
            buttonText = 'Get Legal Defense';
          } else if (data.practiceAreas.some(area => /estate|will|trust/i.test(area))) {
            buttonText = 'Plan Your Estate';
          } else if (data.practiceAreas.some(area => /business|corporate/i.test(area))) {
            buttonText = 'Business Consultation';
          }
        }

        return buttonText;
      }

      // Helper function to generate a subdomain from firm name
      function generateSubdomain(firmName) {
        if (!firmName) return '';

        // Remove special characters, spaces, and common legal terms
        return firmName
          .toLowerCase()
          .replace(/[^a-z0-9]/g, '')
          .replace(/law(firm|office|group)?|llp|llc|pc|pa|associates|legal/g, '')
          .substring(0, 20); // Limit to 20 characters
      }

      // Helper function to generate a prompt from the extracted data
      function generatePromptFromData(data) {
        let prompt = '';

        if (data.firmName) {
          prompt += 'You are an AI assistant for ' + data.firmName + '. ';
        } else {
          prompt += 'You are an AI legal assistant. ';
        }

        if (data.practiceAreas && data.practiceAreas.length > 0) {
          prompt += 'You specialize in ' + data.practiceAreas.join(', ') + '. ';
        }

        prompt += 'Your role is to gather information from potential clients about their legal needs and provide helpful information about the firm\\'s services. ';

        if (data.description) {
          prompt += 'About the firm: ' + data.description + ' ';
        }

        if (data.contactInfo && (data.contactInfo.phone || data.contactInfo.email)) {
          prompt += 'You can offer to connect the client with an attorney';
          if (data.contactInfo.phone) {
            prompt += ' at ' + data.contactInfo.phone;
          }
          if (data.contactInfo.email) {
            prompt += ' or via email at ' + data.contactInfo.email;
          }
          prompt += '. ';
        }

        prompt += 'Be professional, courteous, and helpful at all times.';

        return prompt;
      }

      return extractAttorneyProfile();
    `;

    // Step 3: Call the AI Meta MCP server API
    const response = await fetch('/api/ai-meta-mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'web_evaluate',
        arguments: {
          url: formattedUrl,
          evaluationScript,
          options: {
            timeout: 15000,
          },
        },
      }),
    });

    if (!response.ok) {
      throw new Error('API error: ' + response.status);
    }

    const data = await response.json();

    // Parse the result
    if (data && data.content && data.content[0] && data.content[0].text) {
      try {
        const parsedResult = JSON.parse(data.content[0].text);

        if (parsedResult.success && parsedResult.result) {
          // Map the extracted data to our expected format
          const extractedData = {
            // Basic information
            firmName: parsedResult.result.firmName || 'Unknown Firm',
            attorneyName: parsedResult.result.attorneyName || '',

            // Visual elements
            logo: {
              url: parsedResult.result.visualElements?.logo || '',
              width: 200, // Default values
              height: 80,
              backgroundColor: parsedResult.result.visualElements?.colors?.background || '#ffffff',
              textColor: parsedResult.result.visualElements?.colors?.text || '#000000'
            },
            colors: {
              primary: parsedResult.result.visualElements?.colors?.primary || '#123456',
              secondary: parsedResult.result.visualElements?.colors?.secondary || '#789abc',
              accent: parsedResult.result.visualElements?.colors?.accent || '#def012',
              background: parsedResult.result.visualElements?.colors?.background || '#ffffff',
              text: parsedResult.result.visualElements?.colors?.text || '#333333'
            },
            backgroundImages: parsedResult.result.visualElements?.images?.filter(img => img.type === 'background')?.map(img => img.url) || [],

            // Content analysis
            practiceAreas: parsedResult.result.practiceAreas || [],
            attorneys: [
              {
                name: parsedResult.result.attorneyName || 'Unknown Attorney',
                title: 'Attorney',
                profileImage: '',
                specialties: parsedResult.result.practiceAreas || [],
                education: ''
              }
            ],
            address: {
              street: '',
              city: '',
              state: parsedResult.result.state || '',
              zip: ''
            },
            contactInfo: parsedResult.result.contactInfo || {},
            contentAnalysis: {
              keyPhrases: [],
              services: parsedResult.result.practiceAreas || [],
              clientFocus: []
            },

            // Configuration values
            welcomeMessage: parsedResult.result.welcomeMessage || 'Welcome! How can I assist you today?',
            informationGathering: parsedResult.result.informationGathering || 'Could you tell me about your legal situation?',
            buttonText: parsedResult.result.buttonText || 'Start Consultation',
            subdomain: parsedResult.result.subdomain || '',

            // Fonts
            fonts: {
              heading: parsedResult.result.visualElements?.fonts?.heading || '',
              body: parsedResult.result.visualElements?.fonts?.body || ''
            },

            // Generate agent prompt
            suggestedPrompt: parsedResult.result.suggestedPrompt || generatePrompt(parsedResult.result)
          };

          // Try to parse the address
          if (parsedResult.result.contactInfo?.address) {
            const addressParts = parsedResult.result.contactInfo.address.split(',');
            if (addressParts.length >= 2) {
              extractedData.address.street = addressParts[0].trim();
              extractedData.address.city = addressParts[1].trim();

              // Try to extract state and zip
              if (addressParts.length >= 3) {
                const stateZipParts = addressParts[2].trim().split(' ');
                if (stateZipParts.length >= 2) {
                  extractedData.address.state = stateZipParts[0].trim();
                  extractedData.address.zip = stateZipParts[1].trim();
                }
              }
            }
          }

          return extractedData;
        } else {
          throw new Error(parsedResult.error || 'Failed to extract profile information');
        }
      } catch (parseError) {
        console.error('Error parsing result:', parseError);
        throw new Error('Invalid response format');
      }
    } else {
      throw new Error('Invalid response format');
    }
  } catch (error) {
    console.error('Error scraping website:', error);
    throw new Error('Failed to scrape website: ' + error.message);
  }
};

/**
 * Validate and format URL to ensure it has proper protocol
 */
const formatUrl = (url) => {
  if (!url) throw new Error('URL is required');

  // Add https:// if no protocol specified
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return 'https://' + url;
  }

  return url;
};

/**
 * Extract firm name from website
 * Look for logo alt text, header text, or title
 */
const extractFirmName = async (/* page */) => {
  // Implementation would:
  // 1. Check for logo alt text
  // 2. Look for main heading (h1) in header
  // 3. Parse title tag content
  // 4. Use heuristics to clean up text (remove "Law Firm", "LLP", etc.)
  return "Extracted Firm Name";
};

/**
 * Extract logo information
 * Find primary logo image and its details
 */
const extractLogo = async (/* page */) => {
  // Implementation would:
  // 1. Find logo in header (common selectors like .logo, #logo, etc.)
  // 2. Get image src, width, height
  // 3. Capture logo background and foreground colors
  return {
    url: "logo.png",
    width: 200,
    height: 80,
    backgroundColor: "#ffffff",
    textColor: "#000000"
  };
};

/**
 * Extract color scheme from website
 * Analyze dominant colors in header, footer, buttons, etc.
 */
const extractColors = async (/* page */) => {
  // Implementation would:
  // 1. Extract CSS variables if available
  // 2. Get background colors from header, content areas, footer
  // 3. Get text colors from headings, paragraphs
  // 4. Get accent colors from buttons, links, highlights
  // 5. Use color analysis to identify primary/secondary/accent
  return {
    primary: "#123456",
    secondary: "#789abc",
    accent: "#def012",
    background: "#ffffff",
    text: "#333333"
  };
};

/**
 * Extract large background images from the website
 */
const extractBackgroundImages = async (/* page */) => {
  // Implementation would:
  // 1. Find hero/banner sections
  // 2. Extract background-image CSS properties
  // 3. Get img tags with large dimensions
  // 4. Filter for high-quality images only
  return [
    {
      url: "background1.jpg",
      position: "hero",
      size: "large"
    }
  ];
};

/**
 * Extract practice areas mentioned on the website
 */
const extractPracticeAreas = async (/* page */) => {
  // Implementation would:
  // 1. Look for common practice area section selectors
  // 2. Extract list items or section headers
  // 3. Match against known practice area terms
  // 4. Clean and normalize terms
  return ["Practice Area 1", "Practice Area 2"];
};

/**
 * Extract attorney information
 */
const extractAttorneyInfo = async (/* page */) => {
  // Implementation would:
  // 1. Find attorney profiles/team section
  // 2. Extract name, title, image for each attorney
  // 3. Extract specialties and education if available
  return [
    {
      name: "Attorney Name",
      title: "Title",
      profileImage: "attorney.jpg",
      specialties: ["Specialty 1"],
      education: "Law School"
    }
  ];
};

/**
 * Extract office address information
 */
const extractAddress = async (/* page */) => {
  // Implementation would:
  // 1. Look for address in footer or contact section
  // 2. Parse address components
  // 3. Use regex to identify state code
  return {
    street: "123 Main St",
    city: "City",
    state: "NY",
    zip: "12345"
  };
};

/**
 * Analyze website content for key information
 */
const analyzeContent = async (/* page */) => {
  // Implementation would:
  // 1. Extract all page text
  // 2. Identify key phrases and marketing language
  // 3. Extract service offerings
  // 4. Identify client focus
  return {
    keyPhrases: ["key phrase 1"],
    services: ["service 1"],
    clientFocus: ["client type 1"]
  };
};

/**
 * Generate a suggested prompt for the LegalScout agent
 * based on extracted content
 */
const generatePrompt = async (extractedData) => {
  // Implementation would:
  // 1. Combine firm name, practice areas, key phrases
  // 2. Structure into a natural-sounding prompt
  // 3. Include specific instructions for gathering client information
  return "Generated prompt based on website content";
};

/**
 * Save extracted data to a database (mock function for demo)
 * In production, this would connect to a real database
 */
export const saveExtractedData = async (data, userId) => {
  // This is a mock implementation for the demo
  console.log('Demo mode: Would save data to database', { data, userId });
  return { success: true, message: 'Data saved (demo mode)' };
};

/**
 * Enhanced web scraping using multiple methods
 */
const enhancedWebScraping = async (url, options = {}) => {
  const { useFirecrawl = true } = options;

  try {
    // Method 1: Use existing AI Meta MCP scraping
    const basicData = await scrapeWebsite(url);

    // Method 2: Use Firecrawl for enhanced extraction if available
    let firecrawlData = {};
    if (useFirecrawl) {
      try {
        firecrawlData = await scrapeWithFirecrawl(url);
      } catch (error) {
        console.warn('Firecrawl extraction failed, using basic data:', error.message);
      }
    }

    // Merge and enhance the data
    return mergeScrapedData(basicData, firecrawlData);
  } catch (error) {
    console.error('Enhanced web scraping failed:', error);
    throw error;
  }
};

/**
 * Use Firecrawl for advanced content extraction
 */
const scrapeWithFirecrawl = async (url) => {
  try {
    const response = await fetch('/api/firecrawl-search', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: `site:${new URL(url).hostname} attorney law firm profile`,
        numResults: 1,
        format: 'detailed',
        legalFocus: true
      })
    });

    if (!response.ok) {
      throw new Error(`Firecrawl API error: ${response.status}`);
    }

    const data = await response.json();
    return extractFirecrawlData(data);
  } catch (error) {
    console.error('Firecrawl scraping error:', error);
    throw error;
  }
};

/**
 * Extract structured data from Firecrawl response
 */
const extractFirecrawlData = (firecrawlResponse) => {
  const results = firecrawlResponse.results || [];
  if (results.length === 0) return {};

  const result = results[0];
  return {
    enhancedDescription: result.summary || result.content?.substring(0, 500),
    structuredContent: result.structured_data || {},
    metadata: result.metadata || {},
    additionalImages: result.images || [],
    socialProfiles: result.social_links || {}
  };
};

/**
 * Merge data from multiple scraping sources
 */
const mergeScrapedData = (basicData, firecrawlData) => {
  return {
    ...basicData,
    // Enhanced description from Firecrawl
    description: firecrawlData.enhancedDescription || basicData.description,
    // Additional metadata
    metadata: firecrawlData.metadata || {},
    // Enhanced social profiles
    socialMedia: {
      ...basicData.socialMedia,
      ...firecrawlData.socialProfiles
    },
    // Additional images
    visualElements: {
      ...basicData.visualElements,
      images: [
        ...(basicData.visualElements?.images || []),
        ...(firecrawlData.additionalImages || [])
      ]
    },
    // Raw Firecrawl data for AI processing
    rawFirecrawlData: firecrawlData
  };
};

export default {
  scrapeWebsite,
  oneClickAttorneyConfig,
  saveExtractedData
};