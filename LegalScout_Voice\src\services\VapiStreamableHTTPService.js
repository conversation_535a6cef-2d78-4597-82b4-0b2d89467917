/**
 * Vapi Streamable HTTP Service
 * 
 * Production-ready implementation using streamable-HTTP transport
 * Based on: https://docs.vapi.ai/sdk/mcp-server#remote-streamable-http
 * 
 * "Remote (streamable-HTTP) - Recommended for production"
 */

class VapiStreamableHTTPService {
  constructor() {
    this.apiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
    this.publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
    this.mcpEndpoint = '/api/vapi-mcp-server'; // Use local proxy to avoid CORS
    this.apiEndpoint = 'https://api.vapi.ai';
  }

  /**
   * Make streamable-HTTP request to Vapi MCP server
   * Following production pattern from docs
   */
  async makeStreamableHTTPRequest(toolName, arguments = {}) {
    try {
      console.log(`[VapiStreamableHTTP] Making ${toolName} request...`);
      
      const response = await fetch(this.mcpEndpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: Date.now(),
          method: 'tools/call',
          params: {
            name: toolName,
            arguments: arguments
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.error) {
        throw new Error(`MCP Error: ${result.error.message}`);
      }

      console.log(`[VapiStreamableHTTP] ✅ ${toolName} successful`);
      return this.parseToolResponse(result.result);
      
    } catch (error) {
      console.warn(`[VapiStreamableHTTP] ${toolName} failed, using direct API:`, error.message);
      return null;
    }
  }

  /**
   * Get assistant using streamable-HTTP (production pattern)
   */
  async getAssistant(assistantId) {
    try {
      // Try streamable-HTTP first
      const mcpResult = await this.makeStreamableHTTPRequest('get_assistant', { assistantId });
      if (mcpResult) {
        return mcpResult;
      }

      // Fallback to direct API
      console.log('[VapiStreamableHTTP] Using direct API fallback...');
      const response = await fetch(`${this.apiEndpoint}/assistant/${assistantId}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const assistant = await response.json();
      console.log('[VapiStreamableHTTP] ✅ Assistant retrieved via direct API');
      return assistant;

    } catch (error) {
      console.error('[VapiStreamableHTTP] Error getting assistant:', error);
      throw error;
    }
  }

  /**
   * List assistants using streamable-HTTP
   */
  async listAssistants() {
    try {
      // Try streamable-HTTP first
      const mcpResult = await this.makeStreamableHTTPRequest('list_assistants', {});
      if (mcpResult) {
        return mcpResult;
      }

      // Fallback to direct API
      console.log('[VapiStreamableHTTP] Using direct API fallback...');
      const response = await fetch(`${this.apiEndpoint}/assistant`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const assistants = await response.json();
      console.log('[VapiStreamableHTTP] ✅ Assistants listed via direct API');
      return assistants;

    } catch (error) {
      console.error('[VapiStreamableHTTP] Error listing assistants:', error);
      throw error;
    }
  }

  /**
   * Create call using streamable-HTTP
   */
  async createCall(callConfig) {
    try {
      // Try streamable-HTTP first
      const mcpResult = await this.makeStreamableHTTPRequest('create_call', callConfig);
      if (mcpResult) {
        return mcpResult;
      }

      // Fallback to direct API
      console.log('[VapiStreamableHTTP] Using direct API fallback...');
      const response = await fetch(`${this.apiEndpoint}/call`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(callConfig)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const call = await response.json();
      console.log('[VapiStreamableHTTP] ✅ Call created via direct API');
      return call;

    } catch (error) {
      console.error('[VapiStreamableHTTP] Error creating call:', error);
      throw error;
    }
  }

  /**
   * Update assistant using streamable-HTTP
   */
  async updateAssistant(assistantId, updateData) {
    try {
      // Direct API for updates (more reliable)
      const response = await fetch(`${this.apiEndpoint}/assistant/${assistantId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const assistant = await response.json();
      console.log('[VapiStreamableHTTP] ✅ Assistant updated via direct API');
      return assistant;

    } catch (error) {
      console.error('[VapiStreamableHTTP] Error updating assistant:', error);
      throw error;
    }
  }

  /**
   * Parse tool response from MCP
   */
  parseToolResponse(response) {
    if (!response?.content) return response;
    
    const textItem = response.content.find(item => item.type === 'text');
    if (textItem?.text) {
      try {
        return JSON.parse(textItem.text);
      } catch {
        return textItem.text;
      }
    }
    return response;
  }

  /**
   * Test connection health
   */
  async testConnection() {
    try {
      console.log('[VapiStreamableHTTP] Testing connection...');
      
      // Test streamable-HTTP
      const mcpTest = await this.makeStreamableHTTPRequest('list_assistants', {});
      if (mcpTest) {
        console.log('[VapiStreamableHTTP] ✅ Streamable-HTTP working');
        return { streamableHTTP: true, directAPI: true };
      }

      // Test direct API
      const response = await fetch(`${this.apiEndpoint}/assistant`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        console.log('[VapiStreamableHTTP] ✅ Direct API working');
        return { streamableHTTP: false, directAPI: true };
      }

      throw new Error('Both connections failed');

    } catch (error) {
      console.error('[VapiStreamableHTTP] Connection test failed:', error);
      return { streamableHTTP: false, directAPI: false, error: error.message };
    }
  }

  /**
   * Get Web SDK config for client-side voice interface
   * Following: https://docs.vapi.ai/quickstart/web
   */
  getWebSDKConfig(assistantId) {
    return {
      publicKey: this.publicKey, // Use public key for client-side
      assistant: assistantId,
      config: {
        transcriber: {
          provider: "deepgram",
          model: "nova-2"
        },
        voice: {
          provider: "11labs",
          voiceId: "sarah"
        }
      }
    };
  }

  /**
   * Generate attorney-ready voice interface link
   */
  generateVoiceInterfaceLink(assistantId, subdomain = null) {
    const baseUrl = subdomain ? 
      `https://${subdomain}.legalscout.net` : 
      'https://legalscout.net';
    
    return `${baseUrl}/simple-preview?assistantId=${assistantId}&transport=streamableHTTP`;
  }

  /**
   * Check if attorney can be immediately up and running
   */
  async checkAttorneyReadiness(assistantId) {
    try {
      const connectionTest = await this.testConnection();
      const assistant = await this.getAssistant(assistantId);
      
      const readiness = {
        apiConnection: connectionTest.directAPI || connectionTest.streamableHTTP,
        assistantExists: !!assistant,
        assistantConfigured: !!(assistant?.firstMessage && assistant?.model?.messages?.[0]?.content),
        voiceInterfaceReady: true, // Always ready with public key
        readyToGo: false
      };

      readiness.readyToGo = readiness.apiConnection && readiness.assistantExists && readiness.assistantConfigured;

      if (readiness.readyToGo) {
        readiness.link = this.generateVoiceInterfaceLink(assistantId);
        console.log('[VapiStreamableHTTP] ✅ Attorney is ready to go!');
      }

      return readiness;

    } catch (error) {
      console.error('[VapiStreamableHTTP] Readiness check failed:', error);
      return {
        apiConnection: false,
        assistantExists: false,
        assistantConfigured: false,
        voiceInterfaceReady: false,
        readyToGo: false,
        error: error.message
      };
    }
  }
}

// Create singleton instance
export const vapiStreamableHTTPService = new VapiStreamableHTTPService();
export default vapiStreamableHTTPService;
