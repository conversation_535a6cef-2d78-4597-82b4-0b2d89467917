/**
 * VAPI Assistant Utilities
 *
 * This file contains utility functions for working with VAPI assistants.
 */

import { supabase } from '../lib/supabase';

/**
 * Ensures that an attorney has a VAPI assistant ID
 * If the attorney doesn't have a VAPI assistant ID, creates one and updates the attorney record
 *
 * @param {Object} attorney - The attorney object
 * @returns {Promise<Object>} - The updated attorney object with a VAPI assistant ID
 */
export const ensureVapiAssistant = async (attorney) => {
  if (!attorney) {
    console.error('ensureVapiAssistant: Attorney object is required');
    throw new Error('Attorney object is required');
  }

  // Validate that attorney has a valid ID
  if (!attorney.id) {
    console.error('ensureVapiAssistant: Attorney must have a valid ID');
    throw new Error('Attorney must have a valid ID');
  }

  // If the attorney already has a VAPI assistant ID, return the attorney
  if (attorney.vapi_assistant_id) {
    console.log(`Attorney already has VAPI assistant ID: ${attorney.vapi_assistant_id}`);
    return attorney;
  }

  console.log('Creating VAPI assistant for attorney...');

  try {
    // Check if MCP is available
    if (!window.mcp) {
      console.warn('MCP not available, keeping existing assistant ID if available');

      // If attorney already has an assistant ID, keep it
      if (attorney.vapi_assistant_id) {
        console.log('Attorney already has assistant ID, keeping it:', attorney.vapi_assistant_id);
        return attorney;
      }

      // No assistant ID exists, create a new one through Vapi
      console.log('No existing assistant ID, creating new assistant through Vapi');
      console.log('Attorney object before creation:', { id: attorney.id, email: attorney.email, vapi_assistant_id: attorney.vapi_assistant_id });
    }

    // Create assistant configuration using correct Vapi API format
    const assistantConfig = {
      name: `${attorney.firm_name || 'Your Law Firm'} Legal Assistant`,
      firstMessage: attorney.welcome_message ||
        `Hello, I'm Scout from ${attorney.firm_name || 'Your Law Firm'}. How can I help you today?`,
      firstMessageMode: "assistant-speaks-first",
      model: {
        provider: "openai",
        model: attorney.ai_model || "gpt-4o",
        messages: [
          {
            role: "system",
            content: attorney.vapi_instructions ||
              `You are a legal assistant for ${attorney.firm_name || 'Your Law Firm'}. Help potential clients understand their legal needs and collect relevant information for consultation.`
          }
        ]
      },
      voice: {
        provider: attorney.voice_provider || "11labs",
        voiceId: attorney.voice_id || "sarah"
      },
      transcriber: {
        provider: "deepgram",
        model: "nova-3"
      }
    };

    // Create the assistant using direct API call with explicit private key
    console.log('Creating assistant with config:', assistantConfig);

    // Use the private key directly for assistant creation
    const privateKey = '6734febc-fc65-4669-93b0-929b31ff6564';

    console.log('Using private key for assistant creation:', privateKey.substring(0, 8) + '...');

    const response = await fetch('https://api.vapi.ai/assistant', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${privateKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(assistantConfig)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Vapi API error:', response.status, errorText);
      throw new Error(`Vapi API error: ${response.status} - ${errorText}`);
    }

    const assistant = await response.json();

    if (!assistant || !assistant.id) {
      throw new Error('Failed to create assistant - no ID returned from Vapi');
    }

    console.log(`Created VAPI assistant: ${assistant.id}`);

    // Update attorney with assistant ID
    const updatedAttorney = {
      ...attorney,
      vapi_assistant_id: assistant.id,
      updated_at: new Date().toISOString()
    };

    // Save to Supabase
    const { error: updateError } = await supabase
      .from('attorneys')
      .update({ vapi_assistant_id: assistant.id })
      .eq('id', attorney.id);

    if (updateError) {
      console.error('Error updating attorney with assistant ID:', updateError);
      throw new Error(`Failed to save assistant ID to database: ${updateError.message}`);
    }

    console.log('Successfully created and saved assistant:', assistant.id);
    return updatedAttorney;
  } catch (error) {
    console.error('Error creating VAPI assistant:', error);
    throw error;
  }
};

/**
 * Finds an attorney by email and ensures they have a VAPI assistant ID
 *
 * @param {string} email - The attorney's email
 * @returns {Promise<Object>} - The attorney object with a VAPI assistant ID
 */
export const findAttorneyByEmailAndEnsureVapiAssistant = async (email) => {
  if (!email) {
    console.error('findAttorneyByEmailAndEnsureVapiAssistant: Email is required');
    throw new Error('Email is required');
  }

  try {
    // Try fetching attorney by email - handle duplicates by getting the most recent one
    const { data: attorneys, error: fetchError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', email)
      .order('updated_at', { ascending: false });

    if (fetchError) {
      console.error('Error fetching attorney by email:', fetchError);
      throw new Error(`Supabase fetch error: ${fetchError.message}`);
    }

    // Take the first (most recent) attorney if multiple exist
    const attorney = attorneys && attorneys.length > 0 ? attorneys[0] : null;

    if (attorneys && attorneys.length > 1) {
      console.warn(`Found ${attorneys.length} attorneys for email ${email}, using the most recent one (ID: ${attorney.id})`);
    }

    if (attorney) {
      console.log(`Found attorney by email: ${attorney.id}`);

      // Ensure the attorney has a VAPI assistant ID
      return await ensureVapiAssistant(attorney);
    } else {
      console.log(`No attorney found for email: ${email}`);

      // FIXED: Don't create new attorneys automatically - this was causing duplicates
      // Let the calling code handle attorney creation if needed
      return null;
    }
  } catch (error) {
    console.error('Error in findAttorneyByEmailAndEnsureVapiAssistant:', error);
    throw error;
  }
};

export default {
  ensureVapiAssistant,
  findAttorneyByEmailAndEnsureVapiAssistant
};
