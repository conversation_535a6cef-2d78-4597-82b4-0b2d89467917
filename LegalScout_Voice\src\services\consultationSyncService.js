/**
 * Consultation Sync Service
 *
 * Service to sync Vapi call data and create consultation records
 */

import { supabase } from '../lib/supabase';

/**
 * Sync recent Vapi calls and create consultation records
 * @param {Array} vapiCalls - Array of Vapi call objects
 * @returns {Promise<Object>} - Result of the sync operation
 */
export async function syncVapiCallsToConsultations(vapiCalls) {
  try {
    const results = {
      success: 0,
      errors: 0,
      details: []
    };

    for (const call of vapiCalls) {
      try {
        const result = await createConsultationFromCall(call);
        if (result.success) {
          results.success++;
          results.details.push({
            callId: call.id,
            status: 'success',
            consultationId: result.data?.id
          });
        } else {
          results.errors++;
          results.details.push({
            callId: call.id,
            status: 'error',
            error: result.error
          });
        }
      } catch (error) {
        results.errors++;
        results.details.push({
          callId: call.id,
          status: 'error',
          error: error.message
        });
      }
    }

    return {
      success: true,
      results
    };
  } catch (error) {
    console.error('Error syncing Vapi calls:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Create a consultation record from a Vapi call
 * @param {Object} call - Vapi call object
 * @returns {Promise<Object>} - Result of the operation
 */
async function createConsultationFromCall(call) {
  try {
    // Get attorney ID from assistant ID
    const attorneyId = await getAttorneyIdFromAssistantId(call.assistantId);

    if (!attorneyId) {
      // Use default attorney if no match found
      const defaultAttorneyId = '00000000-0000-0000-0000-000000000000';
      console.warn(`No attorney found for assistant ${call.assistantId}, using default attorney`);

      // Update the default attorney to use this assistant ID
      await supabase
        .from('attorneys')
        .update({ vapi_assistant_id: call.assistantId })
        .eq('id', defaultAttorneyId);

      return await createConsultationRecord(call, defaultAttorneyId);
    }

    return await createConsultationRecord(call, attorneyId);
  } catch (error) {
    console.error('Error creating consultation from call:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get attorney ID from assistant ID
 * @param {string} assistantId - The Vapi assistant ID
 * @returns {Promise<string|null>} - The attorney ID or null if not found
 */
async function getAttorneyIdFromAssistantId(assistantId) {
  try {
    const { data, error } = await supabase
      .from('attorneys')
      .select('id')
      .eq('vapi_assistant_id', assistantId)
      .single();

    if (error || !data) {
      return null;
    }

    return data.id;
  } catch (error) {
    console.error('Error getting attorney ID from assistant ID:', error);
    return null;
  }
}

/**
 * Create a consultation record from call data
 * @param {Object} call - The call data from Vapi
 * @param {string} attorneyId - The attorney ID
 * @returns {Promise<Object>} - The result of the operation
 */
async function createConsultationRecord(call, attorneyId) {
  try {
    // Check if consultation already exists for this call
    const { data: existing } = await supabase
      .from('consultations')
      .select('id')
      .eq('metadata->call_id', call.id)
      .single();

    if (existing) {
      return {
        success: true,
        data: existing,
        message: 'Consultation already exists'
      };
    }

    // Extract client information (mock data for now since we don't have detailed call data)
    const clientInfo = extractClientInfoFromCall(call);

    // Create consultation record
    const consultationRecord = {
      attorney_id: attorneyId,
      client_name: clientInfo.name || 'Anonymous Client',
      client_email: clientInfo.email,
      client_phone: clientInfo.phone,
      summary: generateSummaryFromCall(call),
      transcript: generateTranscriptFromCall(call),
      duration: call.duration || 0,
      practice_area: clientInfo.practice_area,
      location: clientInfo.location,
      location_data: clientInfo.location_data || {},
      metadata: {
        call_id: call.id,
        assistant_id: call.assistantId,
        vapi_status: call.status,
        vapi_end_reason: call.endedReason,
        created_at: call.createdAt,
        updated_at: call.updatedAt,
        ...clientInfo.metadata
      },
      status: 'new'
    };

    // Store in Supabase
    const { data, error } = await supabase
      .from('consultations')
      .insert(consultationRecord)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Error creating consultation record:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Extract client information from call data
 * @param {Object} call - The call data from Vapi
 * @returns {Object} - The extracted client information
 */
function extractClientInfoFromCall(call) {
  // For now, return mock data since we don't have detailed call information
  // In a real implementation, this would parse tool executions and transcripts
  return {
    name: null,
    email: null,
    phone: null,
    practice_area: null,
    location: null,
    location_data: {},
    metadata: {}
  };
}

/**
 * Generate a summary from call data
 * @param {Object} call - The call data from Vapi
 * @returns {string} - The generated summary
 */
function generateSummaryFromCall(call) {
  const duration = call.duration ? Math.round(call.duration / 60) : 0;
  const endReason = call.endedReason || 'unknown';

  return `Call ended with reason: ${endReason}. Duration: ${duration} minutes. Status: ${call.status}.`;
}

/**
 * Generate a transcript from call data
 * @param {Object} call - The call data from Vapi
 * @returns {string} - The generated transcript
 */
function generateTranscriptFromCall(call) {
  // For now, return a placeholder since we don't have transcript data in the basic call object
  return 'Transcript not available - call ended before completion or transcript not captured.';
}

/**
 * Get recent consultations for an attorney
 * @param {string} attorneyId - The attorney ID
 * @param {number} limit - Maximum number of consultations to return
 * @returns {Promise<Array>} - Array of consultation records
 */
export async function getConsultationsForAttorney(attorneyId, limit = 50) {
  try {
    const { data, error } = await supabase
      .from('consultations')
      .select('*')
      .eq('attorney_id', attorneyId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error getting consultations for attorney:', error);
    return [];
  }
}

/**
 * Enhance consultation records with detailed Vapi call data
 * @param {Array} consultations - Array of consultation records
 * @returns {Promise<Array>} - Enhanced consultation records
 */
export async function enhanceConsultationsWithVapiData(consultations) {
  // This would fetch detailed call data from Vapi MCP server
  // For now, return consultations as-is since we have basic data
  return consultations.map(consultation => ({
    ...consultation,
    // Add any computed fields or enhancements here
    duration_formatted: consultation.duration ? `${Math.round(consultation.duration / 60)} min` : 'Unknown',
    status_display: consultation.status === 'new' ? 'New' : consultation.status
  }));
}
