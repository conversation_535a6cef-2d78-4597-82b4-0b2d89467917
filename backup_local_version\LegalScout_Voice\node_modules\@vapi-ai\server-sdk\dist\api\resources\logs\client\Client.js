"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logs = void 0;
const environments = __importStar(require("../../../../environments"));
const core = __importStar(require("../../../../core"));
const url_join_1 = __importDefault(require("url-join"));
const errors = __importStar(require("../../../../errors/index"));
class Logs {
    constructor(_options) {
        this._options = _options;
    }
    /**
     * @param {Vapi.LogsGetRequest} request
     * @param {Logs.RequestOptions} requestOptions - Request-specific configuration.
     */
    get() {
        return __awaiter(this, arguments, void 0, function* (request = {}, requestOptions) {
            const list = (request) => __awaiter(this, void 0, void 0, function* () {
                var _a, _b, _c;
                const { type: type_, webhookType, assistantId, phoneNumberId, customerId, squadId, callId, page, sortOrder, limit, createdAtGt, createdAtLt, createdAtGe, createdAtLe, updatedAtGt, updatedAtLt, updatedAtGe, updatedAtLe, } = request;
                const _queryParams = {};
                if (type_ != null) {
                    _queryParams["type"] = type_;
                }
                if (webhookType != null) {
                    _queryParams["webhookType"] = webhookType;
                }
                if (assistantId != null) {
                    _queryParams["assistantId"] = assistantId;
                }
                if (phoneNumberId != null) {
                    _queryParams["phoneNumberId"] = phoneNumberId;
                }
                if (customerId != null) {
                    _queryParams["customerId"] = customerId;
                }
                if (squadId != null) {
                    _queryParams["squadId"] = squadId;
                }
                if (callId != null) {
                    _queryParams["callId"] = callId;
                }
                if (page != null) {
                    _queryParams["page"] = page.toString();
                }
                if (sortOrder != null) {
                    _queryParams["sortOrder"] = sortOrder;
                }
                if (limit != null) {
                    _queryParams["limit"] = limit.toString();
                }
                if (createdAtGt != null) {
                    _queryParams["createdAtGt"] = createdAtGt;
                }
                if (createdAtLt != null) {
                    _queryParams["createdAtLt"] = createdAtLt;
                }
                if (createdAtGe != null) {
                    _queryParams["createdAtGe"] = createdAtGe;
                }
                if (createdAtLe != null) {
                    _queryParams["createdAtLe"] = createdAtLe;
                }
                if (updatedAtGt != null) {
                    _queryParams["updatedAtGt"] = updatedAtGt;
                }
                if (updatedAtLt != null) {
                    _queryParams["updatedAtLt"] = updatedAtLt;
                }
                if (updatedAtGe != null) {
                    _queryParams["updatedAtGe"] = updatedAtGe;
                }
                if (updatedAtLe != null) {
                    _queryParams["updatedAtLe"] = updatedAtLe;
                }
                const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                    url: (0, url_join_1.default)((_c = (_b = (yield core.Supplier.get(this._options.baseUrl))) !== null && _b !== void 0 ? _b : (yield core.Supplier.get(this._options.environment))) !== null && _c !== void 0 ? _c : environments.VapiEnvironment.Default, "logs"),
                    method: "GET",
                    headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "@vapi-ai/server-sdk", "X-Fern-SDK-Version": "0.5.2", "User-Agent": "@vapi-ai/server-sdk/0.5.2", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                    contentType: "application/json",
                    queryParameters: _queryParams,
                    requestType: "json",
                    timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                    maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                    abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
                });
                if (_response.ok) {
                    return _response.body;
                }
                if (_response.error.reason === "status-code") {
                    throw new errors.VapiError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.body,
                    });
                }
                switch (_response.error.reason) {
                    case "non-json":
                        throw new errors.VapiError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.rawBody,
                        });
                    case "timeout":
                        throw new errors.VapiTimeoutError("Timeout exceeded when calling GET /logs.");
                    case "unknown":
                        throw new errors.VapiError({
                            message: _response.error.errorMessage,
                        });
                }
            });
            let _offset = (request === null || request === void 0 ? void 0 : request.page) != null ? request === null || request === void 0 ? void 0 : request.page : 1;
            return new core.Pageable({
                response: yield list(request),
                hasNextPage: (response) => { var _a; return ((_a = response === null || response === void 0 ? void 0 : response.results) !== null && _a !== void 0 ? _a : []).length > 0; },
                getItems: (response) => { var _a; return (_a = response === null || response === void 0 ? void 0 : response.results) !== null && _a !== void 0 ? _a : []; },
                loadPage: (_response) => {
                    _offset += 1;
                    return list(core.setObjectProperty(request, "page", _offset));
                },
            });
        });
    }
    /**
     * @param {Vapi.LoggingControllerLogsDeleteQueryRequest} request
     * @param {Logs.RequestOptions} requestOptions - Request-specific configuration.
     */
    loggingControllerLogsDeleteQuery() {
        return __awaiter(this, arguments, void 0, function* (request = {}, requestOptions) {
            var _a, _b, _c;
            const { type: type_, assistantId, phoneNumberId, customerId, squadId, callId } = request;
            const _queryParams = {};
            if (type_ != null) {
                _queryParams["type"] = type_;
            }
            if (assistantId != null) {
                _queryParams["assistantId"] = assistantId;
            }
            if (phoneNumberId != null) {
                _queryParams["phoneNumberId"] = phoneNumberId;
            }
            if (customerId != null) {
                _queryParams["customerId"] = customerId;
            }
            if (squadId != null) {
                _queryParams["squadId"] = squadId;
            }
            if (callId != null) {
                _queryParams["callId"] = callId;
            }
            const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
                url: (0, url_join_1.default)((_c = (_b = (yield core.Supplier.get(this._options.baseUrl))) !== null && _b !== void 0 ? _b : (yield core.Supplier.get(this._options.environment))) !== null && _c !== void 0 ? _c : environments.VapiEnvironment.Default, "logs"),
                method: "DELETE",
                headers: Object.assign({ Authorization: yield this._getAuthorizationHeader(), "X-Fern-Language": "JavaScript", "X-Fern-SDK-Name": "@vapi-ai/server-sdk", "X-Fern-SDK-Version": "0.5.2", "User-Agent": "@vapi-ai/server-sdk/0.5.2", "X-Fern-Runtime": core.RUNTIME.type, "X-Fern-Runtime-Version": core.RUNTIME.version }, requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return;
            }
            if (_response.error.reason === "status-code") {
                throw new errors.VapiError({
                    statusCode: _response.error.statusCode,
                    body: _response.error.body,
                });
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.VapiError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                    });
                case "timeout":
                    throw new errors.VapiTimeoutError("Timeout exceeded when calling DELETE /logs.");
                case "unknown":
                    throw new errors.VapiError({
                        message: _response.error.errorMessage,
                    });
            }
        });
    }
    _getAuthorizationHeader() {
        return __awaiter(this, void 0, void 0, function* () {
            return `Bearer ${yield core.Supplier.get(this._options.token)}`;
        });
    }
}
exports.Logs = Logs;
