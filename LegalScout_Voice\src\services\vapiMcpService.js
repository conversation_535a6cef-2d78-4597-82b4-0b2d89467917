/**
 * Vapi MCP Service
 *
 * This service provides integration with Vapi's Model Context Protocol (MCP) server,
 * allowing programmatic control of Vapi voice agents, calls, and other resources.
 *
 * Enhanced with robust connection handling, retries, and error recovery.
 * Now with improved debugging and diagnostics.
 * Updated to support both SSE and Streamable HTTP transport methods.
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { mcpConfig } from '../config/mcp.config.js';

// Simple logging functions to replace missing debugger
const logConnection = (level, message, data = {}) => {
  console.log(`[VapiMcpService] ${level.toUpperCase()}: ${message}`, data);
};

const logNetwork = (message, data = {}) => {
  console.log(`[VapiMcpService] NETWORK: ${message}`, data);
};

class VapiMcpService {
  constructor() {
    this.client = null;
    this.connected = false;
    this.apiKey = null;
    this.connectionPromise = null;
    this.connectionAttempts = 0;
    this.MAX_CONNECTION_ATTEMPTS = 3;
    this.lastConnectionTime = null;
    this.CONNECTION_TIMEOUT = 30000; // 30 seconds
    this.RECONNECT_DELAY = 5000; // 5 seconds

    // Direct API properties (used when MCP server is not available)
    this.useDirect = false;
    this.directApiKey = null;
    this.directApiUrl = 'https://api.vapi.ai';

    // PRODUCTION FIX: Create clean fetch to bypass interceptors
    this.cleanFetch = this.createCleanFetch();

    // Possible API endpoints to try - updated with current Vapi API endpoints from docs
    // Include our local proxy as the first option to avoid CORS issues
    const isDev = typeof import.meta !== 'undefined' && import.meta.env?.MODE === 'development';
    this.possibleApiEndpoints = isDev ?
      [
        '/api/vapi-proxy',
        'https://api.vapi.ai',
        'https://dashboard.vapi.ai/api'
      ] :
      [
        'https://api.vapi.ai',
        'https://dashboard.vapi.ai/api'
      ];

    // API resource paths based on Vapi API documentation
    this.apiResourcePaths = {
      assistants: 'assistant',
      calls: 'call',
      phoneNumbers: 'phone-number',
      tools: 'tool'
    };

    // Network debugging disabled for now

    // Log initialization
    logConnection('info', 'Vapi MCP Service initialized', {
      maxAttempts: this.MAX_CONNECTION_ATTEMPTS,
      timeout: this.CONNECTION_TIMEOUT,
      reconnectDelay: this.RECONNECT_DELAY,
      hasCleanFetch: !!this.cleanFetch
    });
  }

  /**
   * Create a clean fetch function that bypasses all interceptors
   * This fixes the production issue where fetch interceptors corrupt Authorization headers
   */
  createCleanFetch() {
    try {
      // Method 1: Try to get original fetch from iframe
      if (typeof document !== 'undefined') {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        document.body.appendChild(iframe);

        const cleanFetch = iframe.contentWindow.fetch.bind(window);
        document.body.removeChild(iframe);

        console.log('[VapiMcpService] Created clean fetch from iframe');
        return cleanFetch;
      }
    } catch (error) {
      console.warn('[VapiMcpService] Could not create clean fetch from iframe:', error.message);
    }

    // Method 2: Fallback to XMLHttpRequest wrapped as fetch
    return this.createXHRFetch();
  }

  /**
   * Create XMLHttpRequest-based fetch as fallback
   */
  createXHRFetch() {
    return function(url, options = {}) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(options.method || 'GET', url);

        // Set headers
        if (options.headers) {
          Object.entries(options.headers).forEach(([key, value]) => {
            xhr.setRequestHeader(key, value);
          });
        }

        xhr.onload = function() {
          const response = {
            ok: xhr.status >= 200 && xhr.status < 300,
            status: xhr.status,
            statusText: xhr.statusText,
            json: () => Promise.resolve(JSON.parse(xhr.responseText)),
            text: () => Promise.resolve(xhr.responseText)
          };
          resolve(response);
        };

        xhr.onerror = function() {
          reject(new Error('Network error'));
        };

        xhr.send(options.body);
      });
    };
  }

  /**
   * Check if we're in production environment
   * @returns {boolean} - True if in production
   */
  isProduction() {
    return typeof window !== 'undefined' &&
      (window.location.hostname === 'dashboard.legalscout.net' ||
       window.location.hostname.endsWith('.legalscout.net') ||
       window.location.hostname === 'legalscout.net');
  }

  /**
   * Ensure connection to the Vapi MCP server
   * @returns {Promise<boolean>} - Connection success status
   */
  async ensureConnection() {
    // If already connected, return true
    if (this.connected && (this.client || this.useDirect)) {
      return true;
    }

    // If connection attempt is in progress, return that promise
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    // Check if we need to wait before reconnecting
    if (this.lastConnectionTime && Date.now() - this.lastConnectionTime < this.RECONNECT_DELAY) {
      const waitTime = this.RECONNECT_DELAY - (Date.now() - this.lastConnectionTime);
      console.log(`[VapiMcpService] Waiting ${waitTime}ms before reconnecting`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    // If we have an API key, try to connect
    if (this.apiKey) {
      // Create a new connection promise
      this.connectionPromise = this._connect(this.apiKey);

      try {
        const result = await this.connectionPromise;
        return result;
      } catch (error) {
        console.error('[VapiMcpService] Connection failed:', error);

        // DISABLED: Don't fall back to direct API, use MCP tools
        if (false && !this.useDirect) {
          console.log('[VapiMcpService] Trying direct API as fallback');

          try {
            // Set up direct API
            this.useDirect = true;
            this.directApiKey = this.apiKey;
            this.connected = true;

            // Try multiple possible endpoints to find the correct one
            console.log('[VapiMcpService] Trying multiple API endpoints to find the correct one');
            let successfulEndpoint = null;

            // Create endpoints with the assistants query parameter
            const testEndpoints = this.possibleApiEndpoints.map(base => `${base}/assistants?limit=1`);

            // Try each endpoint until one works
            for (const endpoint of testEndpoints) {
              try {
                console.log(`[VapiMcpService] Trying endpoint: ${endpoint}`);
                const response = await fetch(endpoint, {
                  method: 'GET',
                  headers: {
                    'Authorization': `Bearer ${this.directApiKey}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                  }
                });

                if (response.ok) {
                  console.log(`[VapiMcpService] Direct connection successful with endpoint: ${endpoint}`);
                  successfulEndpoint = endpoint;
                  break;
                } else {
                  console.warn(`[VapiMcpService] Endpoint ${endpoint} returned status: ${response.status}`);
                }
              } catch (endpointError) {
                console.warn(`[VapiMcpService] Error with endpoint ${endpoint}:`, endpointError);
              }
            }

            if (successfulEndpoint) {
              // Extract the base URL and API path from the successful endpoint
              const endpointBase = successfulEndpoint.split('/assistants')[0];
              console.log(`[VapiMcpService] Using successful endpoint base: ${endpointBase}`);

              this.directApiUrl = endpointBase;
              return true;
            } else {
              console.error('[VapiMcpService] All API endpoints failed');
              this.useDirect = false;
              this.connected = false;
              return false;
            }
          } catch (directError) {
            console.error('[VapiMcpService] Direct API connection error:', directError);
            this.useDirect = false;
            this.connected = false;
            return false;
          }
        }

        return false;
      } finally {
        // Clear the promise when done
        this.connectionPromise = null;
      }
    } else {
      // Try to get API key from environment
      // CRITICAL FIX: Use PRIVATE/SECRET key for server-side operations (assistant management)
      // The MCP service handles server-side operations, not client-side voice calls
      let apiKey = null;

      try {
        // First try to import the config module
        const { getVapiApiKey } = await import('../config/vapiConfig.js');
        apiKey = getVapiApiKey('server');
        console.log('[VapiMcpService] Got API key from vapiConfig module');
      } catch (configError) {
        console.warn('[VapiMcpService] Could not load vapiConfig, trying environment variables directly');

        // Fallback to direct environment variable access
        apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_SECRET_KEY) ||
                 (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PRIVATE_KEY) ||
                 (typeof window !== 'undefined' && window.VITE_VAPI_SECRET_KEY) ||
                 (typeof window !== 'undefined' && window.VITE_VAPI_PRIVATE_KEY) ||
                 (typeof window !== 'undefined' && window.VAPI_TOKEN) ||
                 (typeof window !== 'undefined' && window.VAPI_PRIVATE_KEY) ||
                 '6734febc-fc65-4669-93b0-929b31ff6564'; // Use private key for MCP operations
      }

      if (apiKey) {
        this.apiKey = apiKey;
        console.log('[VapiMcpService] Using SECRET key for server operations:', apiKey ? apiKey.substring(0, 8) + '...' : 'undefined');
        return this.ensureConnection();
      }

      console.error('[VapiMcpService] No API key available for connection');
      return false;
    }
  }

  /**
   * Connect to the Vapi MCP server
   * @param {string} apiKey - Vapi API key (should be secret key for MCP operations)
   * @param {boolean} forceDirect - Force using direct API instead of MCP
   * @returns {Promise<boolean>} - Connection success status
   */
  async connect(apiKey, forceDirect = false) {
    // For MCP operations, we need the secret key, not the public key
    // If no key provided, try to get the server key from config
    if (!apiKey) {
      try {
        const { getVapiApiKey } = await import('../config/vapiConfig.js');
        apiKey = getVapiApiKey('server');
        console.log('[VapiMcpService] Using server API key for MCP operations');
      } catch (configError) {
        console.warn('[VapiMcpService] Could not load config, using fallback key');
        apiKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Fallback to secret key
      }
    }

    if (!apiKey) {
      logConnection('error', 'API key is required for MCP connection');
      return false;
    }

    const isProduction = this.isProduction();
    const isDevelopment = typeof import.meta !== 'undefined' && import.meta.env?.MODE === 'development';

    console.log('[VapiMcpService] Production mode:', isProduction);
    console.log('[VapiMcpService] Development mode:', isDevelopment);
    console.log('[VapiMcpService] Using API key for MCP server operations:', apiKey.substring(0, 8) + '...');

    // Store API key for reconnection if needed
    this.apiKey = apiKey;

    // Reset connection attempts
    this.connectionAttempts = 0;

    // Use direct API for development, production, or when forced
    // MCP server has CORS issues in browser, so use direct API by default
    if (isDevelopment || isProduction || forceDirect) {
      console.log('[VapiMcpService] Using direct API mode for development/production or forced direct mode');
      console.log('[VapiMcpService] API key being used:', apiKey ? `${apiKey.substring(0, 8)}...` : 'none');

      // Set up direct API immediately - FIXED: Ensure correct API key for server operations
      this.useDirect = true;

      let directApiKey = apiKey;
      if (!directApiKey) {
        try {
          const { getVapiApiKey } = await import('../config/vapiConfig.js');
          directApiKey = getVapiApiKey('server');
          console.log('[VapiMcpService] Got direct API key from vapiConfig');
        } catch (configError) {
          console.warn('[VapiMcpService] Could not load vapiConfig for direct API, using environment variables');
          directApiKey = (typeof window !== 'undefined' && window.VITE_VAPI_SECRET_KEY) ||
                        (typeof window !== 'undefined' && window.VITE_VAPI_PRIVATE_KEY) ||
                        (typeof window !== 'undefined' && window.VAPI_TOKEN) ||
                        (typeof window !== 'undefined' && window.VAPI_PRIVATE_KEY) ||
                        '6734febc-fc65-4669-93b0-929b31ff6564'; // Use secret key for server operations
        }
      }

      this.directApiKey = directApiKey;
      this.directApiUrl = 'https://api.vapi.ai';
      this.connected = true;

      console.log('[VapiMcpService] Using direct API key for server operations:', this.directApiKey ? this.directApiKey.substring(0, 8) + '...' : 'undefined');

      return true;
    }

    // Create a new connection promise for MCP
    this.connectionPromise = this._connect(apiKey);

    try {
      const result = await this.connectionPromise;
      return result;
    } finally {
      // Clear the promise when done
      this.connectionPromise = null;
    }
  }

  /**
   * Internal connection method with retry logic
   * @param {string} apiKey - Vapi API key
   * @returns {Promise<boolean>} - Connection success status
   */
  async _connect(apiKey) {
    this.lastConnectionTime = Date.now();
    this.connectionAttempts++;

    logConnection('info', `Connection attempt ${this.connectionAttempts}/${this.MAX_CONNECTION_ATTEMPTS}`, {
      attempt: this.connectionAttempts,
      maxAttempts: this.MAX_CONNECTION_ATTEMPTS,
      apiKey: apiKey ? `${apiKey.substring(0, 5)}...` : 'missing'
    });

    try {
      // Initialize MCP client
      this.client = new Client({
        name: 'legalscout-client',
        version: '1.0.0',
      });

      // Determine environment and choose the correct URL
      const isDevelopment = typeof import.meta !== 'undefined' && import.meta.env?.MODE === 'development';

      // Load MCP configuration
      const { mcpConfig } = await import('../config/mcp.config.js');

      // Use local proxy to avoid CORS issues
      const mcpUrl = isDevelopment ? '/api/vapi-mcp-server' : '/api/vapi-mcp-server';

      // Ensure the API key is properly formatted and encoded
      const sanitizedApiKey = apiKey ? apiKey.trim() : '';

      console.log('[VapiMcpService] Attempting MCP connection to:', mcpUrl);
      console.log('[VapiMcpService] Using Streamable HTTP transport (recommended)');

      // Try to connect to MCP server using local proxy
      try {
        // For local proxy, we don't need to create a transport - just mark as connected
        // The actual MCP calls will go through our API proxy
        console.log('[VapiMcpService] Using local MCP proxy at:', mcpUrl);
        this.connected = true;
        this.mcpUrl = mcpUrl;
        this.apiKey = sanitizedApiKey;
        console.log('[VapiMcpService] Successfully configured to use local MCP proxy');
        return true;
      } catch (mcpError) {
        console.warn('[VapiMcpService] Local MCP proxy setup failed, falling back to direct API:', mcpError.message);

        // Fall back to direct API mode
        console.log('[VapiMcpService] Falling back to direct API mode');

        // FIXED: Ensure correct API key for server operations
        let correctApiKey = sanitizedApiKey;

        if (!correctApiKey) {
          try {
            const { getVapiApiKey } = await import('../config/vapiConfig.js');
            correctApiKey = getVapiApiKey('server');
            console.log('[VapiMcpService] Got fallback API key from vapiConfig');
          } catch (configError) {
            console.warn('[VapiMcpService] Could not load vapiConfig for fallback, using environment variables');
            correctApiKey = (typeof window !== 'undefined' && window.VITE_VAPI_SECRET_KEY) ||
                           (typeof window !== 'undefined' && window.VITE_VAPI_PRIVATE_KEY) ||
                           (typeof window !== 'undefined' && window.VAPI_TOKEN) ||
                           (typeof window !== 'undefined' && window.VAPI_PRIVATE_KEY) ||
                           '6734febc-fc65-4669-93b0-929b31ff6564';
          }
        }

        console.log('[VapiMcpService] Direct API key being used:', correctApiKey ? `${correctApiKey.substring(0, 8)}...` : 'none');
        this.useDirect = true;
        this.directApiKey = correctApiKey;
        this.directApiUrl = 'https://api.vapi.ai';
        this.connected = true;
        return true;
      }
    } catch (error) {
      console.error('[VapiMcpService] Error initializing Vapi MCP client:', error);
      this.connected = false;
      return false;
    }
  }

  /**
   * Disconnect from the Vapi MCP server
   */
  async disconnect() {
    if (this.client && this.connected) {
      try {
        await this.client.close();
        console.log('VapiMcpService: Disconnected from Vapi MCP server');
      } catch (error) {
        console.error('VapiMcpService: Error disconnecting from Vapi MCP server:', error);
      } finally {
        this.connected = false;
        this.client = null;
      }
    }
  }

  /**
   * Get an assistant by ID with robust error handling
   * @param {string} assistantId - The ID of the assistant to get
   * @returns {Promise<Object>} - The assistant object
   */
  async getAssistant(assistantId) {
    try {
      // Check if we're in production environment
      const isProduction = this.isProduction();

      // CRITICAL FIX: Validate assistant ID format first
      if (!assistantId || typeof assistantId !== 'string') {
        throw new Error('Invalid assistant ID: must be a non-empty string');
      }

      // IMPROVED: Better validation that doesn't reject legitimate Vapi assistant IDs
      // Vapi assistant IDs can be UUIDs or other formats, so we need to be more specific
      // Only reject if it's clearly a Supabase attorney ID (check against known patterns)
      const isSupabaseUUID = assistantId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);

      if (isSupabaseUUID) {
        // Additional check: if this looks like it might be an attorney ID being used as assistant ID
        // We can check if it's in a known list of attorney IDs or has certain characteristics
        console.warn('[VapiMcpService] UUID format detected as assistant ID:', assistantId);
        console.warn('[VapiMcpService] This might be a Supabase attorney ID instead of a Vapi assistant ID');

        // Don't throw immediately - let Vapi API determine if it's valid
        // The API will return 404 if it's not a valid assistant ID
      }

      // Check for mock assistant ID
      if (assistantId && assistantId.startsWith('mock-')) {
        if (isProduction) {
          console.error('[VapiMcpService] Mock assistant ID detected in production:', assistantId);
          throw new Error('Mock assistant ID not allowed in production');
        }
        console.warn('[VapiMcpService] Mock assistant ID detected:', assistantId);
        return null;
      }

      // Remove hardcoded mock handling - let all assistants use real API calls

      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        if (isProduction) {
          throw new Error('Vapi service not available in production');
        }
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log(`[VapiMcpService] Getting assistant: ${assistantId}`);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to get assistant');

        // Check if we're in development mode
        const isDevelopment = typeof import.meta !== 'undefined' && import.meta.env?.MODE === 'development';

        // Remove special proxy handling - use standard API endpoints for all assistants

        // Try multiple possible endpoints for getting an assistant
        let response = null;
        let foundEndpoint = false;

        // Try each base endpoint with the correct resource path
        for (const baseEndpoint of this.possibleApiEndpoints) {
          try {
            const assistantUrl = `${baseEndpoint}/${this.apiResourcePaths.assistants}/${assistantId}`;
            console.log(`[VapiMcpService] Trying to get assistant from: ${assistantUrl}`);

            response = await fetch(assistantUrl, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${this.directApiKey}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              }
            });

            if (response.ok) {
              console.log(`[VapiMcpService] Successfully got assistant from: ${assistantUrl}`);
              this.directApiUrl = baseEndpoint; // Update the API URL to the successful endpoint
              foundEndpoint = true;
              break;
            } else {
              console.warn(`[VapiMcpService] Failed to get assistant from ${assistantUrl}: ${response.status}`);
            }
          } catch (endpointError) {
            console.warn(`[VapiMcpService] Error getting assistant from endpoint: ${endpointError.message}`);
          }
        }

        if (!foundEndpoint) {
          console.warn(`[VapiMcpService] Assistant not found with ID: ${assistantId}`);
          return null;
        }

        if (!response.ok) {
          // If 404, return null to indicate assistant not found
          if (response.status === 404) {
            console.warn(`[VapiMcpService] Assistant not found: ${assistantId}`);
            return null;
          }

          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      }

      // Otherwise use MCP with correct tool name
      const response = await this.client.callTool({
        name: 'get_assistant_vapi-mcp-server',
        arguments: { assistantId }
      });

      // Parse the response content
      if (response.content && Array.isArray(response.content)) {
        const textContent = response.content.find(item => item.type === 'text');
        if (textContent?.text) {
          try {
            return JSON.parse(textContent.text);
          } catch (parseError) {
            console.warn('[VapiMcpService] Could not parse MCP response as JSON:', textContent.text);
            return textContent.text;
          }
        }
      }

      return response.content;
    } catch (error) {
      console.error('[VapiMcpService] Error getting assistant:', error);

      // Check if we're in development mode
      const isDev = process.env.NODE_ENV === 'development';

      // Try direct API call as fallback before returning mock
      if (isDev && assistantId && !assistantId.startsWith('mock-')) {
        console.warn('[VapiMcpService] MCP failed, trying direct API call for assistant:', assistantId);
        try {
          const directResponse = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
            headers: {
              'Authorization': `Bearer ${process.env.VITE_VAPI_PRIVATE_KEY}`,
              'Content-Type': 'application/json'
            }
          });

          if (directResponse.ok) {
            const assistantData = await directResponse.json();
            console.log('[VapiMcpService] ✅ Direct API call successful:', assistantData.name);
            return assistantData;
          }
        } catch (directError) {
          console.warn('[VapiMcpService] Direct API call also failed:', directError.message);
        }
      }

      // CRITICAL FIX: Don't return mock data that confuses the UI
      // Instead, throw a proper error that can be handled upstream
      if (isDev) {
        console.warn('[VapiMcpService] Assistant not found, throwing proper error instead of mock data');

        // IMPROVED: More specific error handling for UUID format
        const isSupabaseUUID = assistantId && assistantId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);
        if (isSupabaseUUID) {
          console.warn('[VapiMcpService] UUID format detected, might be attorney ID instead of assistant ID:', assistantId);
          throw new Error(`Assistant not found: ${assistantId}. This appears to be a UUID format - please verify this is a valid Vapi assistant ID, not an attorney ID.`);
        }

        throw new Error(`Assistant not found: ${assistantId}. Please check if this assistant exists in your Vapi account.`);
      }

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * List available tools from the MCP server
   * @returns {Promise<Array>} - List of available tools
   */
  async listTools() {
    if (!await this.ensureConnection()) {
      throw new Error('Not connected to Vapi MCP server');
    }

    try {
      const toolsResult = await this.client.listTools();
      return toolsResult.tools;
    } catch (error) {
      console.error('VapiMcpService: Error listing tools:', error);
      throw error;
    }
  }

  /**
   * List all assistants with robust error handling
   * @returns {Promise<Array>} - List of assistants
   */
  async listAssistants() {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log('[VapiMcpService] Listing assistants');

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to list assistants');

        const response = await fetch(`${this.directApiUrl}/${this.apiResourcePaths.assistants}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      }

      // Otherwise use MCP with correct tool name
      const response = await this.client.callTool({
        name: 'list_assistants_vapi-mcp-server',
        arguments: {}
      });

      // Parse the response content
      if (response.content && Array.isArray(response.content)) {
        const textContent = response.content.find(item => item.type === 'text');
        if (textContent?.text) {
          try {
            return JSON.parse(textContent.text);
          } catch (parseError) {
            console.warn('[VapiMcpService] Could not parse MCP response as JSON:', textContent.text);
            return textContent.text;
          }
        }
      }

      return response.content;
    } catch (error) {
      console.error('[VapiMcpService] Error listing assistants:', error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Create a new assistant with robust error handling
   * @param {Object} assistantConfig - Assistant configuration
   * @returns {Promise<Object>} - Created assistant
   */
  async createAssistant(assistantConfig) {
    try {
      // Check if we're in production environment
      const isProduction = this.isProduction();

      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        if (isProduction) {
          throw new Error('Vapi service not available in production');
        }
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log('[VapiMcpService] Creating assistant:', assistantConfig.name);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to create assistant');

        const response = await fetch(`${this.directApiUrl}/${this.apiResourcePaths.assistants}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(assistantConfig)
        });

        // Check if response is ok
        if (!response.ok) {
          let errorMessage = `Vapi API error: ${response.status} ${response.statusText}`;
          try {
            const errorData = await response.text();
            if (errorData) {
              console.error('[VapiMcpService] Vapi API error response:', errorData);
              errorMessage += ` - ${errorData}`;
            }
          } catch (textError) {
            console.warn('[VapiMcpService] Could not read error response text:', textError);
          }

          // In production, don't fall back to mock
          if (isProduction) {
            throw new Error(errorMessage);
          }

          throw new Error(errorMessage);
        }

        // Parse successful response
        try {
          const data = await response.json();
          console.log('[VapiMcpService] Raw response data:', data);
          console.log('[VapiMcpService] Assistant ID from response:', data?.id);
          console.log('[VapiMcpService] Response data type:', typeof data);
          console.log('[VapiMcpService] Response data keys:', Object.keys(data || {}));

          if (!data || !data.id) {
            console.error('[VapiMcpService] Invalid response: missing assistant ID');
            console.error('[VapiMcpService] Full response:', JSON.stringify(data, null, 2));
            throw new Error('Invalid response from Vapi API: missing assistant ID');
          }

          console.log('[VapiMcpService] Successfully created assistant:', data.id);
          return data;
        } catch (jsonError) {
          console.error('[VapiMcpService] Error parsing response JSON:', jsonError);
          console.error('[VapiMcpService] Response status:', response.status);
          console.error('[VapiMcpService] Response headers:', Object.fromEntries(response.headers.entries()));

          // Try to get the raw response text for debugging
          try {
            const responseText = await response.text();
            console.error('[VapiMcpService] Raw response text:', responseText);
          } catch (textError) {
            console.error('[VapiMcpService] Could not read response text:', textError);
          }

          throw new Error(`Error parsing response from Vapi API: ${jsonError.message}`);
        }
      }

      // Otherwise use MCP with correct tool name
      const response = await this.client.callTool({
        name: 'create_assistant_vapi-mcp-server',
        arguments: assistantConfig
      });

      console.log('[VapiMcpService] MCP response:', response);
      console.log('[VapiMcpService] MCP response content:', response.content);
      console.log('[VapiMcpService] MCP response content type:', typeof response.content);

      // Parse the response content
      let parsedContent = response.content;
      if (response.content && Array.isArray(response.content)) {
        const textContent = response.content.find(item => item.type === 'text');
        if (textContent?.text) {
          try {
            parsedContent = JSON.parse(textContent.text);
          } catch (parseError) {
            console.warn('[VapiMcpService] Could not parse MCP response as JSON:', textContent.text);
            parsedContent = textContent.text;
          }
        }
      }

      if (parsedContent && parsedContent.id) {
        console.log('[VapiMcpService] MCP assistant ID:', parsedContent.id);
        return parsedContent;
      } else {
        console.error('[VapiMcpService] Invalid MCP response: missing assistant ID');
        console.error('[VapiMcpService] Full MCP response:', JSON.stringify(response, null, 2));
        throw new Error('Invalid response from MCP: missing assistant ID');
      }
    } catch (error) {
      console.error('[VapiMcpService] Error creating assistant:', error);

      // Check if we're in production environment
      const isProduction = this.isProduction();

      // In production, don't fall back to mock assistants
      if (isProduction) {
        throw error;
      }

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Update an existing assistant with robust error handling
   * @param {string} assistantId - The ID of the assistant to update
   * @param {Object} assistantConfig - The updated configuration
   * @returns {Promise<Object>} - The updated assistant
   */
  async updateAssistant(assistantId, assistantConfig) {
    try {
      // Check if we're in development mode
      const isDev = process.env.NODE_ENV === 'development';

      // If we're in development mode and the assistant ID is a mock ID,
      // return a mock updated assistant
      if (isDev && assistantId && assistantId.startsWith('mock-')) {
        console.log('[VapiMcpService] Development mode detected with mock assistant ID, returning mock updated assistant');

        return {
          id: assistantId,
          name: assistantConfig.name || 'Mock Assistant',
          instructions: assistantConfig.instructions || 'Mock instructions',
          firstMessage: assistantConfig.firstMessage || 'Hello, I am a mock assistant',
          mock: true,
          updated: true,
          // Include other properties from assistantConfig as needed
          voice: assistantConfig.voice || { provider: "11labs", voiceId: "sarah" },
          llm: assistantConfig.llm || { provider: "openai", model: "gpt-4o" }
        };
      }

      // For real API calls, ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        if (isDev) {
          console.log('[VapiMcpService] Not connected to Vapi MCP server, returning mock updated assistant');
          return {
            id: assistantId,
            name: assistantConfig.name || 'Mock Assistant',
            instructions: assistantConfig.instructions || 'Mock instructions',
            firstMessage: assistantConfig.firstMessage || 'Hello, I am a mock assistant',
            mock: true,
            updated: true,
            voice: assistantConfig.voice || { provider: "11labs", voiceId: "sarah" },
            llm: assistantConfig.llm || { provider: "openai", model: "gpt-4o" }
          };
        }
        throw new Error('Not connected to Vapi MCP server');
      }

      if (!assistantId) {
        throw new Error('Assistant ID is required');
      }

      console.log(`[VapiMcpService] Updating assistant: ${assistantId}`);

      // Ensure firstMessageMode is set if firstMessage is provided
      if (assistantConfig.firstMessage && !assistantConfig.firstMessageMode) {
        assistantConfig.firstMessageMode = "assistant-speaks-first";
      }

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to update assistant');

        // In development, log the API key (first few chars) for debugging
        if (isDev) {
          console.log(`[VapiMcpService] API Key (first 5 chars): ${this.directApiKey ? this.directApiKey.substring(0, 5) + '...' : 'undefined'}`);
        }

        try {
          // First get the current assistant to merge with updates
          let currentAssistant;
          try {
            currentAssistant = await this.getAssistant(assistantId);
          } catch (getError) {
            // In development, create a mock current assistant if the real one can't be retrieved
            if (isDev) {
              console.warn('[VapiMcpService] Error getting current assistant, using mock current assistant:', getError.message);
              currentAssistant = {
                id: assistantId,
                name: 'Mock Assistant',
                instructions: 'Mock instructions',
                firstMessage: 'Hello, I am a mock assistant',
                mock: true
              };
            } else {
              throw getError;
            }
          }

          if (!currentAssistant) {
            // In development, create a mock current assistant if the real one doesn't exist
            if (isDev) {
              console.warn('[VapiMcpService] Assistant not found, using mock current assistant');
              currentAssistant = {
                id: assistantId,
                name: 'Mock Assistant',
                instructions: 'Mock instructions',
                firstMessage: 'Hello, I am a mock assistant',
                mock: true
              };
            } else {
              throw new Error(`Assistant not found: ${assistantId}`);
            }
          }

          // Merge current assistant with updates
          const mergedConfig = {
            ...currentAssistant,
            ...assistantConfig
          };

          // Remove any properties that shouldn't be sent in the update
          delete mergedConfig.id;
          delete mergedConfig.createdAt;
          delete mergedConfig.updatedAt;
          delete mergedConfig.mock; // Remove mock flag if present

          const response = await fetch(`${this.directApiUrl}/${this.apiResourcePaths.assistants}/${assistantId}`, {
            method: 'PATCH',
            headers: {
              'Authorization': `Bearer ${this.directApiKey}`,
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: JSON.stringify(mergedConfig)
          });

          if (!response.ok) {
            // In development, return a mock updated assistant instead of throwing an error
            if (isDev) {
              console.warn(`[VapiMcpService] API error: ${response.status} ${response.statusText}, returning mock updated assistant`);
              return {
                id: assistantId,
                ...mergedConfig,
                mock: true,
                updated: true
              };
            }
            throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
          }

          try {
            const data = await response.json();
            return data;
          } catch (jsonError) {
            // In development, return a mock updated assistant if JSON parsing fails
            if (isDev) {
              console.warn('[VapiMcpService] Error parsing response JSON, returning mock updated assistant');
              return {
                id: assistantId,
                ...mergedConfig,
                mock: true,
                updated: true
              };
            }
            throw jsonError;
          }
        } catch (fetchError) {
          // In development, return a mock updated assistant for any fetch errors
          if (isDev) {
            console.warn('[VapiMcpService] Fetch error, returning mock updated assistant:', fetchError.message);
            return {
              id: assistantId,
              name: assistantConfig.name || 'Mock Assistant',
              instructions: assistantConfig.instructions || 'Mock instructions',
              firstMessage: assistantConfig.firstMessage || 'Hello, I am a mock assistant',
              mock: true,
              updated: true,
              voice: assistantConfig.voice || { provider: "11labs", voiceId: "sarah" },
              llm: assistantConfig.llm || { provider: "openai", model: "gpt-4o" }
            };
          }
          throw fetchError;
        }
      }

      // Otherwise use MCP with correct tool name
      try {
        const response = await this.client.callTool({
          name: 'update_assistant_vapi-mcp-server',
          arguments: {
            assistantId,
            ...assistantConfig
          }
        });

        // Parse the response content
        if (response.content && Array.isArray(response.content)) {
          const textContent = response.content.find(item => item.type === 'text');
          if (textContent?.text) {
            try {
              return JSON.parse(textContent.text);
            } catch (parseError) {
              console.warn('[VapiMcpService] Could not parse MCP response as JSON:', textContent.text);
              return textContent.text;
            }
          }
        }

        return response.content;
      } catch (mcpError) {
        // In development, return a mock updated assistant for MCP errors
        if (isDev) {
          console.warn('[VapiMcpService] MCP error, returning mock updated assistant:', mcpError.message);
          return {
            id: assistantId,
            name: assistantConfig.name || 'Mock Assistant',
            instructions: assistantConfig.instructions || 'Mock instructions',
            firstMessage: assistantConfig.firstMessage || 'Hello, I am a mock assistant',
            mock: true,
            updated: true,
            voice: assistantConfig.voice || { provider: "11labs", voiceId: "sarah" },
            llm: assistantConfig.llm || { provider: "openai", model: "gpt-4o" }
          };
        }
        throw mcpError;
      }
    } catch (error) {
      console.error('[VapiMcpService] Error updating assistant:', error);

      // If we're in development, return a mock updated assistant instead of throwing an error
      if (process.env.NODE_ENV === 'development') {
        console.warn('[VapiMcpService] Returning mock updated assistant due to error');
        return {
          id: assistantId,
          name: assistantConfig.name || 'Mock Assistant',
          instructions: assistantConfig.instructions || 'Mock instructions',
          firstMessage: assistantConfig.firstMessage || 'Hello, I am a mock assistant',
          mock: true,
          updated: true,
          error: error.message,
          voice: assistantConfig.voice || { provider: "11labs", voiceId: "sarah" },
          llm: assistantConfig.llm || { provider: "openai", model: "gpt-4o" }
        };
      }

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * List all calls
   * @returns {Promise<Array>} - List of calls
   */
  async listCalls() {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log('[VapiMcpService] Listing calls');

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to list calls');

        const response = await fetch(`${this.directApiUrl}/calls`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          // If 404, it might mean no calls exist - return empty array
          if (response.status === 404) {
            console.warn('[VapiMcpService] No calls found (404) - returning empty array');
            return [];
          }
          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return Array.isArray(data) ? data : [];
      }

      // Otherwise use MCP
      const response = await this.client.callTool({
        name: 'list_calls',
        arguments: {}
      });

      // Handle MCP response
      if (response.content && Array.isArray(response.content)) {
        return response.content;
      } else if (response.content && response.content.length === 0) {
        console.warn('[VapiMcpService] No calls found via MCP - returning empty array');
        return [];
      }

      return response.content || [];
    } catch (error) {
      console.error('[VapiMcpService] Error listing calls:', error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Get a specific call by ID
   * @param {string} callId - Call ID
   * @returns {Promise<Object>} - Call details
   */
  async getCall(callId) {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log(`[VapiMcpService] Getting call: ${callId}`);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to get call');

        // CRITICAL FIX: Ensure we're using the private key for call operations
        let privateKey;
        try {
          const { getVapiApiKey } = await import('../config/vapiConfig.js');
          privateKey = getVapiApiKey('server'); // Get private key specifically
        } catch (configError) {
          console.warn('[VapiMcpService] Could not load vapiConfig, using fallback key');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Hardcoded fallback for production
        }

        // Additional fallback if still no key
        if (!privateKey) {
          console.warn('[VapiMcpService] No private key from config, using hardcoded fallback');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564';
        }

        console.log(`[VapiMcpService] Using private key for get call: ${privateKey ? privateKey.substring(0, 8) + '...' : 'undefined'}`);

        // PRODUCTION FIX: Use clean fetch to bypass interceptors
        const fetchMethod = this.cleanFetch || fetch;
        console.log(`[VapiMcpService] Using ${this.cleanFetch ? 'clean' : 'standard'} fetch for get call`);

        const response = await fetchMethod(`${this.directApiUrl}/call/${callId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${privateKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          // If 404, return null to indicate call not found
          if (response.status === 404) {
            console.warn(`[VapiMcpService] Call not found: ${callId}`);
            return null;
          }

          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      }

      // Otherwise use MCP
      const response = await this.client.callTool({
        name: 'get_call',
        arguments: {
          callId
        }
      });

      return response.content;
    } catch (error) {
      console.error(`[VapiMcpService] Error getting call ${callId}:`, error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Create a new outbound call
   * @param {string} assistantId - Assistant ID
   * @param {string} phoneNumber - Customer phone number
   * @param {Object} options - Additional call options
   * @returns {Promise<Object>} - Created call
   */
  async createCall(assistantId, phoneNumber, options = {}) {
    try {
      // Validate required parameters
      if (!assistantId) {
        throw new Error('Assistant ID is required for call creation');
      }
      if (!phoneNumber) {
        throw new Error('Phone number is required for call creation');
      }

      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log(`[VapiMcpService] Creating call for assistant: ${assistantId}`);
      console.log(`[VapiMcpService] Phone number: ${phoneNumber}`);
      console.log(`[VapiMcpService] Options:`, options);

      // Format phone number to E.164 format if needed
      const formattedPhoneNumber = this._formatPhoneNumber(phoneNumber);

      // EMERGENCY FIX: Based on VapiBlocks documentation - correct API structure
      // Reference: https://www.vapiblocks.com/components/outbound-phone-dial
      const callArguments = {
        assistantId,
        customer: {
          number: formattedPhoneNumber  // ✅ Correct property name is "number", not "phoneNumber"
        }
      };

      // If phoneNumberId is provided in options, add it to the root of callArguments
      if (options.phoneNumberId) {
        callArguments.phoneNumberId = options.phoneNumberId;
        console.log(`[VapiMcpService] Using phone number ID: ${options.phoneNumberId}`);
      }

      // If scheduledAt is provided, add it to the root
      if (options.scheduledAt) {
        callArguments.scheduledAt = options.scheduledAt;
        console.log(`[VapiMcpService] Scheduling call for: ${options.scheduledAt}`);
      }

      console.log(`[VapiMcpService] Final call arguments:`, callArguments);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to create call');

        // CRITICAL FIX: Ensure we're using the private key for call operations
        let privateKey;
        try {
          const { getVapiApiKey } = await import('../config/vapiConfig.js');
          privateKey = getVapiApiKey('server'); // Get private key specifically
        } catch (configError) {
          console.warn('[VapiMcpService] Could not load vapiConfig, using fallback key');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Hardcoded fallback for production
        }

        // Additional fallback if still no key
        if (!privateKey) {
          console.warn('[VapiMcpService] No private key from config, using hardcoded fallback');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564';
        }

        console.log(`[VapiMcpService] Using private key for call creation: ${privateKey ? privateKey.substring(0, 8) + '...' : 'undefined'}`);

        // PRODUCTION FIX: Use clean fetch to bypass interceptors
        const fetchMethod = this.cleanFetch || fetch;
        console.log(`[VapiMcpService] Using ${this.cleanFetch ? 'clean' : 'standard'} fetch for call creation`);

        const response = await fetchMethod(`${this.directApiUrl}/call/phone`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${privateKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(callArguments)
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`[VapiMcpService] Direct API error response:`, errorText);
          throw new Error(`Direct API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();
        console.log(`[VapiMcpService] Call created successfully:`, data);
        return data;
      }

      // Otherwise use MCP with correct tool name
      const response = await this.client.callTool({
        name: 'create_call_vapi-mcp-server',
        arguments: callArguments
      });

      // Parse MCP response
      let callData = response.content;
      if (response.content && Array.isArray(response.content)) {
        const textContent = response.content.find(item => item.type === 'text');
        if (textContent?.text) {
          try {
            callData = JSON.parse(textContent.text);
          } catch (parseError) {
            console.warn('[VapiMcpService] Could not parse MCP response as JSON:', textContent.text);
            callData = textContent.text;
          }
        }
      }

      console.log(`[VapiMcpService] Call created successfully via MCP:`, callData);
      return callData;
    } catch (error) {
      console.error('[VapiMcpService] Error creating call:', error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Format phone number to E.164 format
   * @param {string} phoneNumber - Phone number to format
   * @returns {string} - Formatted phone number
   */
  _formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');

    // If it starts with 1 and has 11 digits, it's already in the right format
    if (digits.length === 11 && digits.startsWith('1')) {
      return `+${digits}`;
    }

    // If it has 10 digits, assume it's a US number and add +1
    if (digits.length === 10) {
      return `+1${digits}`;
    }

    // If it already starts with +, return as is
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }

    // Otherwise, return with + prefix
    return `+${digits}`;
  }

  /**
   * Get call details by ID
   * @param {string} callId - Call ID
   * @returns {Promise<Object>} - Call details
   */
  async getCall(callId) {
    try {
      // Validate required parameters
      if (!callId) {
        throw new Error('Call ID is required');
      }

      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log(`[VapiMcpService] Getting call details for: ${callId}`);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to get call');

        // CRITICAL FIX: Ensure we're using the private key for call operations
        let privateKey;
        try {
          const { getVapiApiKey } = await import('../config/vapiConfig.js');
          privateKey = getVapiApiKey('server'); // Get private key specifically
        } catch (configError) {
          console.warn('[VapiMcpService] Could not load vapiConfig, using fallback key');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Hardcoded fallback for production
        }

        // Additional fallback if still no key
        if (!privateKey) {
          console.warn('[VapiMcpService] No private key from config, using hardcoded fallback');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564';
        }

        console.log(`[VapiMcpService] Using private key for get call details: ${privateKey ? privateKey.substring(0, 8) + '...' : 'undefined'}`);

        // PRODUCTION FIX: Use clean fetch to bypass interceptors
        const fetchMethod = this.cleanFetch || fetch;
        console.log(`[VapiMcpService] Using ${this.cleanFetch ? 'clean' : 'standard'} fetch for get call details`);

        const response = await fetchMethod(`${this.directApiUrl}/call/${callId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${privateKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          if (response.status === 404) {
            console.warn(`[VapiMcpService] Call not found: ${callId}`);
            return null;
          }
          const errorText = await response.text();
          throw new Error(`Direct API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();
        console.log(`[VapiMcpService] Call details retrieved:`, data);
        return data;
      }

      // Otherwise use MCP with correct tool name
      const response = await this.client.callTool({
        name: 'get_call_vapi-mcp-server',
        arguments: { callId }
      });

      // Parse MCP response
      let callData = response.content;
      if (response.content && Array.isArray(response.content)) {
        const textContent = response.content.find(item => item.type === 'text');
        if (textContent?.text) {
          try {
            callData = JSON.parse(textContent.text);
          } catch (parseError) {
            console.warn('[VapiMcpService] Could not parse MCP response as JSON:', textContent.text);
            callData = textContent.text;
          }
        }
      }

      console.log(`[VapiMcpService] Call details retrieved via MCP:`, callData);
      return callData;
    } catch (error) {
      console.error(`[VapiMcpService] Error getting call ${callId}:`, error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Monitor call status until completion
   * @param {string} callId - Call ID to monitor
   * @param {Function} onStatusUpdate - Callback for status updates
   * @param {number} maxWaitTime - Maximum time to wait in milliseconds (default: 5 minutes)
   * @returns {Promise<Object>} - Final call status
   */
  async monitorCall(callId, onStatusUpdate = null, maxWaitTime = 300000) {
    const startTime = Date.now();
    const pollInterval = 2000; // Poll every 2 seconds

    console.log(`[VapiMcpService] Starting call monitoring for: ${callId}`);

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const callData = await this.getCall(callId);

        if (!callData) {
          throw new Error(`Call ${callId} not found`);
        }

        console.log(`[VapiMcpService] Call ${callId} status: ${callData.status}`);

        // Notify callback of status update
        if (onStatusUpdate && typeof onStatusUpdate === 'function') {
          onStatusUpdate(callData);
        }

        // Check if call is in a terminal state
        if (['completed', 'failed', 'ended', 'no-answer', 'busy', 'canceled'].includes(callData.status)) {
          console.log(`[VapiMcpService] Call ${callId} completed with status: ${callData.status}`);
          return callData;
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));

      } catch (error) {
        console.error(`[VapiMcpService] Error monitoring call ${callId}:`, error);

        // If it's a connection error, try to reconnect and continue
        if (error.message.includes('connection') || error.message.includes('timeout')) {
          this.connected = false;
          await this.ensureConnection();
          continue;
        }

        throw error;
      }
    }

    // Timeout reached
    console.warn(`[VapiMcpService] Call monitoring timeout reached for: ${callId}`);
    const finalCallData = await this.getCall(callId);
    return finalCallData;
  }

  /**
   * List all phone numbers
   * @returns {Promise<Array>} - List of phone numbers
   */
  async listPhoneNumbers() {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log('[VapiMcpService] Listing phone numbers');

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to list phone numbers');

        // CRITICAL FIX: Ensure we're using the private key for phone number operations
        let privateKey;
        try {
          const { getVapiApiKey } = await import('../config/vapiConfig.js');
          privateKey = getVapiApiKey('server'); // Get private key specifically
        } catch (configError) {
          console.warn('[VapiMcpService] Could not load vapiConfig, using fallback key');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Hardcoded fallback for production
        }

        // Additional fallback if still no key
        if (!privateKey) {
          console.warn('[VapiMcpService] No private key from config, using hardcoded fallback');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564';
        }

        console.log(`[VapiMcpService] Using private key for phone numbers: ${privateKey ? privateKey.substring(0, 8) + '...' : 'undefined'}`);

        // PRODUCTION FIX: Use clean fetch to bypass interceptors
        const fetchMethod = this.cleanFetch || fetch;
        console.log(`[VapiMcpService] Using ${this.cleanFetch ? 'clean' : 'standard'} fetch for phone numbers`);

        const response = await fetchMethod(`${this.directApiUrl}/phone-number`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${privateKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`[VapiMcpService] Phone numbers API error: ${response.status} ${response.statusText}`, errorText);
          throw new Error(`Direct API error: ${response.status} ${errorText || response.statusText}`);
        }

        const data = await response.json();
        return data;
      }

      // Otherwise use MCP
      const response = await this.client.callTool({
        name: 'list_phone_numbers',
        arguments: {}
      });

      return response.content;
    } catch (error) {
      console.error('[VapiMcpService] Error listing phone numbers:', error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Get a specific phone number by ID
   * @param {string} phoneNumberId - Phone number ID
   * @returns {Promise<Object>} - Phone number details
   */
  async getPhoneNumber(phoneNumberId) {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log(`[VapiMcpService] Getting phone number: ${phoneNumberId}`);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to get phone number');

        // CRITICAL FIX: Ensure we're using the private key for phone number operations
        let privateKey;
        try {
          const { getVapiApiKey } = await import('../config/vapiConfig.js');
          privateKey = getVapiApiKey('server'); // Get private key specifically
        } catch (configError) {
          console.warn('[VapiMcpService] Could not load vapiConfig, using fallback key');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Hardcoded fallback for production
        }

        // Additional fallback if still no key
        if (!privateKey) {
          console.warn('[VapiMcpService] No private key from config, using hardcoded fallback');
          privateKey = '6734febc-fc65-4669-93b0-929b31ff6564';
        }

        // PRODUCTION FIX: Use clean fetch to bypass interceptors
        const fetchMethod = this.cleanFetch || fetch;
        console.log(`[VapiMcpService] Using ${this.cleanFetch ? 'clean' : 'standard'} fetch for get phone number`);

        const response = await fetchMethod(`${this.directApiUrl}/phone-number/${phoneNumberId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${privateKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          // If 404, return null to indicate phone number not found
          if (response.status === 404) {
            console.warn(`[VapiMcpService] Phone number not found: ${phoneNumberId}`);
            return null;
          }

          const errorText = await response.text();
          console.error(`[VapiMcpService] Get phone number API error: ${response.status} ${response.statusText}`, errorText);
          throw new Error(`Direct API error: ${response.status} ${errorText || response.statusText}`);
        }

        const data = await response.json();
        return data;
      }

      // Otherwise use MCP
      const response = await this.client.callTool({
        name: 'get_phone_number',
        arguments: {
          phoneNumberId
        }
      });

      return response.content;
    } catch (error) {
      console.error(`[VapiMcpService] Error getting phone number ${phoneNumberId}:`, error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * List all squads
   * @returns {Promise<Array>} - List of squads
   */
  async listSquads() {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log('[VapiMcpService] Listing squads');

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to list squads');

        const response = await fetch(`${this.directApiUrl}/squads`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      }

      // Otherwise use MCP
      const response = await this.client.callTool({
        name: 'list_squads_vapi-mcp-server',
        arguments: {}
      });

      return response.content;
    } catch (error) {
      console.error('[VapiMcpService] Error listing squads:', error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Get a specific squad by ID
   * @param {string} squadId - Squad ID
   * @returns {Promise<Object>} - Squad details
   */
  async getSquad(squadId) {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log(`[VapiMcpService] Getting squad details for: ${squadId}`);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to get squad');

        const response = await fetch(`${this.directApiUrl}/squad/${squadId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      }

      // Otherwise use MCP
      const response = await this.client.callTool({
        name: 'get_squad_vapi-mcp-server',
        arguments: { squadId }
      });

      return response.content;
    } catch (error) {
      console.error(`[VapiMcpService] Error getting squad ${squadId}:`, error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Create a new squad
   * @param {Object} squadConfig - Squad configuration
   * @returns {Promise<Object>} - Created squad
   */
  async createSquad(squadConfig) {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log('[VapiMcpService] Creating squad with config:', squadConfig);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to create squad');

        const response = await fetch(`${this.directApiUrl}/squad`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(squadConfig)
        });

        if (!response.ok) {
          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      }

      // Otherwise use MCP
      const response = await this.client.callTool({
        name: 'create_squad_vapi-mcp-server',
        arguments: squadConfig
      });

      return response.content;
    } catch (error) {
      console.error('[VapiMcpService] Error creating squad:', error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Update an existing squad
   * @param {string} squadId - Squad ID
   * @param {Object} squadConfig - Updated squad configuration
   * @returns {Promise<Object>} - Updated squad
   */
  async updateSquad(squadId, squadConfig) {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log(`[VapiMcpService] Updating squad ${squadId} with config:`, squadConfig);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to update squad');

        const response = await fetch(`${this.directApiUrl}/squad/${squadId}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(squadConfig)
        });

        if (!response.ok) {
          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      }

      // Otherwise use MCP
      const response = await this.client.callTool({
        name: 'update_squad_vapi-mcp-server',
        arguments: { squadId, ...squadConfig }
      });

      return response.content;
    } catch (error) {
      console.error(`[VapiMcpService] Error updating squad ${squadId}:`, error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Delete a squad
   * @param {string} squadId - Squad ID
   * @returns {Promise<boolean>} - Success status
   */
  async deleteSquad(squadId) {
    try {
      // Ensure connection
      const connected = await this.ensureConnection();
      if (!connected) {
        throw new Error('Not connected to Vapi MCP server');
      }

      console.log(`[VapiMcpService] Deleting squad: ${squadId}`);

      // If using direct API
      if (this.useDirect) {
        console.log('[VapiMcpService] Using direct API to delete squad');

        const response = await fetch(`${this.directApiUrl}/squad/${squadId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${this.directApiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Direct API error: ${response.status} ${response.statusText}`);
        }

        return true;
      }

      // Otherwise use MCP
      const response = await this.client.callTool({
        name: 'delete_squad_vapi-mcp-server',
        arguments: { squadId }
      });

      return response.content;
    } catch (error) {
      console.error(`[VapiMcpService] Error deleting squad ${squadId}:`, error);

      // If connection error, try to reconnect
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        this.connected = false;
        throw new Error(`MCP server connection error: ${error.message}`);
      }

      throw error;
    }
  }
}

// Export a singleton instance
export const vapiMcpService = new VapiMcpService();

// Make it available globally for debugging and integration
if (typeof window !== 'undefined') {
  window.vapiMcpService = vapiMcpService;
}

// Export the class for testing or custom instantiation
export default VapiMcpService;
