/**
 * Token Utilities
 *
 * This module provides utilities for creating and verifying secure tokens
 * for features like call control links.
 */

import * as jose from 'jose';

// Secret key for signing tokens - in production, this should be an environment variable
const TOKEN_SECRET = process.env.TOKEN_SECRET || 'legalscout-secure-token-secret';
// Convert the secret to a Uint8Array for jose
const secretKey = new TextEncoder().encode(TOKEN_SECRET);

/**
 * Create a secure JWT token
 * @param {Object} payload - The data to include in the token
 * @param {string} expiresIn - When the token expires (e.g., '1h', '1d')
 * @returns {Promise<string>} - The generated token
 */
export const createSecureToken = async (payload, expiresIn = '24h') => {
  try {
    // Convert expiresIn string to seconds
    let expiresInSeconds = 86400; // Default to 24 hours
    if (expiresIn.endsWith('h')) {
      expiresInSeconds = parseInt(expiresIn) * 3600;
    } else if (expiresIn.endsWith('m')) {
      expiresInSeconds = parseInt(expiresIn) * 60;
    } else if (expiresIn.endsWith('d')) {
      expiresInSeconds = parseInt(expiresIn) * 86400;
    } else if (expiresIn.endsWith('s')) {
      expiresInSeconds = parseInt(expiresIn);
    }

    const jwt = await new jose.SignJWT(payload)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(`${expiresInSeconds}s`)
      .sign(secretKey);

    return jwt;
  } catch (error) {
    console.error('Error creating secure token:', error);
    throw error;
  }
};

/**
 * Verify and decode a JWT token
 * @param {string} token - The token to verify
 * @returns {Promise<Object|null>} - The decoded token payload or null if invalid
 */
export const verifySecureToken = async (token) => {
  try {
    const { payload } = await jose.jwtVerify(token, secretKey);
    return payload;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
};

/**
 * Create a call control token
 * @param {string} callId - The ID of the call
 * @param {string} attorneyId - The ID of the attorney
 * @param {Object} options - Additional options
 * @returns {Promise<string>} - The generated token
 */
export const createCallControlToken = async (callId, attorneyId, options = {}) => {
  const payload = {
    callId,
    attorneyId,
    type: 'call-control',
    ...options
  };

  return await createSecureToken(payload, options.expiresIn || '24h');
};

/**
 * Verify a call control token
 * @param {string} token - The token to verify
 * @returns {Promise<Object|null>} - The decoded token payload or null if invalid
 */
export const verifyCallControlToken = async (token) => {
  const decoded = await verifySecureToken(token);

  if (!decoded || decoded.type !== 'call-control') {
    return null;
  }

  return decoded;
};

/**
 * Extract token from URL query parameters
 * @returns {string|null} - The token or null if not found
 */
export const getTokenFromUrl = () => {
  if (typeof window === 'undefined') {
    return null;
  }

  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('token');
};

// Create a synchronous version of createCallControlToken for backward compatibility
export const createCallControlTokenSync = (callId, attorneyId, options = {}) => {
  const payload = {
    callId,
    attorneyId,
    type: 'call-control',
    ...options
  };

  // Return a placeholder token for now - this is just for compatibility
  // In a real implementation, you'd need to use a synchronous JWT library
  return `call-control-token-${callId}-${attorneyId}-${Date.now()}`;
};

export default {
  createSecureToken,
  verifySecureToken,
  createCallControlToken,
  verifyCallControlToken,
  getTokenFromUrl,
  createCallControlTokenSync
};
