/**
 * Enhanced Integration Utilities
 *
 * This file provides utility functions to integrate the enhanced Vapi components
 * with the rest of the application. It helps ensure consistent use of the enhanced
 * Vapi services throughout the application.
 */

import { enhancedVapiAssistantManager } from '../services/EnhancedVapiAssistantManager';
import { enhancedVapiMcpService } from '../services/EnhancedVapiMcpService';
import { mcpConfig } from '../config/mcp.config';

// Keep track of initialization state
let isInitialized = false;
let initializationPromise = null;

/**
 * Initialize the enhanced Vapi services
 * @param {Object} options - Initialization options
 * @returns {Promise<boolean>} - Whether initialization was successful
 */
export const initializeEnhancedVapi = async (options = {}) => {
  // If already initialized, return true
  if (isInitialized) {
    return true;
  }

  // If initialization is in progress, return that promise
  if (initializationPromise) {
    return initializationPromise;
  }
  
  // Start initialization
  initializationPromise = (async () => {
    try {
      console.log('[EnhancedIntegration] Initializing enhanced Vapi services');
      
      // Get API key from config
      const apiKey = options.apiKey || 
                    mcpConfig.voice.vapi.secretKey || 
                    mcpConfig.voice.vapi.publicKey ||
                    (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||
                    (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY);
      
      if (!apiKey) {
        console.error('[EnhancedIntegration] No API key available');
        return false;
      }
      
      // Initialize MCP service
      const mcpResult = await enhancedVapiMcpService.connect(apiKey, options.forceDirect);
      
      if (!mcpResult) {
        console.error('[EnhancedIntegration] Failed to initialize MCP service');
        return false;
      }
      
      // Initialize assistant manager
      let assistantManagerResult;
      
      if (options.forceDirect || enhancedVapiMcpService.useDirect) {
        // If MCP service is using direct API, use direct API for assistant manager too
        assistantManagerResult = await enhancedVapiAssistantManager.initialize({
          directApiKey: apiKey
        });
      } else {
        // Otherwise use MCP
        assistantManagerResult = await enhancedVapiAssistantManager.initialize({
          mcpUrl: options.mcpUrl || mcpConfig.voice.vapi.mcpUrl
        });
      }
      
      if (!assistantManagerResult) {
        console.error('[EnhancedIntegration] Failed to initialize assistant manager');
        return false;
      }
      
      console.log('[EnhancedIntegration] Enhanced Vapi services initialized successfully');
      isInitialized = true;
      return true;
    } catch (error) {
      console.error('[EnhancedIntegration] Error initializing enhanced Vapi services:', error);
      return false;
    } finally {
      initializationPromise = null;
    }
  })();
  
  return initializationPromise;
};

/**
 * Ensure an attorney has a valid Vapi assistant
 * @param {Object} attorney - The attorney object from Supabase
 * @returns {Promise<Object>} - The updated attorney object
 */
export const ensureAttorneyAssistant = async (attorney) => {
  if (!attorney) {
    throw new Error('Attorney object is required');
  }
  
  // Initialize enhanced Vapi services if needed
  if (!isInitialized) {
    const initResult = await initializeEnhancedVapi();
    if (!initResult) {
      throw new Error('Failed to initialize enhanced Vapi services');
    }
  }
  
  // Ensure attorney has a valid Vapi assistant
  return await enhancedVapiAssistantManager.ensureAssistant(attorney);
};

/**
 * Sync an attorney's Vapi assistant with the latest data from Supabase
 * @param {Object} attorney - The attorney object from Supabase
 * @returns {Promise<Object>} - The sync result
 */
export const syncAttorneyAssistant = async (attorney) => {
  if (!attorney) {
    throw new Error('Attorney object is required');
  }
  
  // Initialize enhanced Vapi services if needed
  if (!isInitialized) {
    const initResult = await initializeEnhancedVapi();
    if (!initResult) {
      throw new Error('Failed to initialize enhanced Vapi services');
    }
  }
  
  // Sync attorney's Vapi assistant
  return await enhancedVapiAssistantManager.syncAssistant(attorney);
};

/**
 * Create a new outbound call
 * @param {string} assistantId - The ID of the assistant to use for the call
 * @param {string} phoneNumber - The phone number to call
 * @param {Object} options - Additional call options
 * @returns {Promise<Object>} - The created call
 */
export const createOutboundCall = async (assistantId, phoneNumber, options = {}) => {
  // Initialize enhanced Vapi services if needed
  if (!isInitialized) {
    const initResult = await initializeEnhancedVapi();
    if (!initResult) {
      throw new Error('Failed to initialize enhanced Vapi services');
    }
  }
  
  // Create outbound call
  return await enhancedVapiMcpService.createCall(assistantId, phoneNumber, options);
};

/**
 * Get a list of all assistants
 * @returns {Promise<Array>} - List of assistants
 */
export const listAssistants = async () => {
  // Initialize enhanced Vapi services if needed
  if (!isInitialized) {
    const initResult = await initializeEnhancedVapi();
    if (!initResult) {
      throw new Error('Failed to initialize enhanced Vapi services');
    }
  }
  
  // List assistants
  return await enhancedVapiMcpService.listAssistants();
};

/**
 * Get a specific assistant by ID
 * @param {string} assistantId - The ID of the assistant to get
 * @returns {Promise<Object>} - The assistant
 */
export const getAssistant = async (assistantId) => {
  // Initialize enhanced Vapi services if needed
  if (!isInitialized) {
    const initResult = await initializeEnhancedVapi();
    if (!initResult) {
      throw new Error('Failed to initialize enhanced Vapi services');
    }
  }
  
  // Get assistant
  return await enhancedVapiMcpService.getAssistant(assistantId);
};

// Export initialization state
export const getInitializationState = () => ({
  isInitialized,
  initializationInProgress: !!initializationPromise
});
