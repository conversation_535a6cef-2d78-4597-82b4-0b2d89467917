import { supabase } from '../lib/supabase-fixed';

/**
 * Service for handling Supabase database operations related to call data and attorneys
 */
class SupabaseService {
  /**
   * Store a call brief in Supabase and associate it with an attorney
   * @param {Object} briefData - The brief data from the call
   * @param {string} attorneyId - The ID of the attorney associated with this brief
   * @returns {Promise<Object>} The stored brief data
   */
  async storeBrief(briefData, attorneyId) {
    try {
      // Check if this is a stub implementation
      if (typeof supabase.from !== 'function' || !briefData) {
        console.log('Using stub implementation for storeBrief');
        return { data: { id: 'mock-id' }, error: null };
      }
      
      // Insert the brief into the briefs table
      const { data, error } = await supabase
        .from('briefs')
        .insert({
          attorney_id: attorneyId,
          client_name: briefData.name || 'Anonymous',
          practice_area: briefData.practiceArea || 'General',
          location: briefData.location?.address || 'Unknown',
          issue: briefData.issue || 'Not specified',
          urgency: briefData.urgency || 'Medium',
          noteworthy: briefData.noteworthy || '',
          location_data: briefData.location || null,
          created_at: new Date().toISOString(),
          status: 'new'
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error storing brief:', error);
      return { data: null, error };
    }
  }

  /**
   * Get attorney information by subdomain
   * @param {string} subdomain - The attorney's subdomain
   * @returns {Promise<Object>} Attorney data
   */
  async getAttorneyBySubdomain(subdomain) {
    try {
      // Check if this is a stub implementation
      if (typeof supabase.from !== 'function' || !subdomain) {
        console.log('Using stub implementation for getAttorneyBySubdomain');
        return { 
          data: { 
            id: 'mock-attorney-id', 
            name: 'Mock Attorney', 
            subdomain,
            practice_areas: ['Family Law', 'Estate Planning'] 
          }, 
          error: null 
        };
      }
      
      const { data, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('subdomain', subdomain)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error getting attorney:', error);
      return { data: null, error };
    }
  }

  /**
   * Get all briefs for a specific attorney
   * @param {string} attorneyId - The ID of the attorney
   * @returns {Promise<Array>} Array of brief objects
   */
  async getAttorneyBriefs(attorneyId) {
    try {
      const { data, error } = await supabase
        .from('briefs')
        .select('*')
        .eq('attorney_id', attorneyId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching attorney briefs:', error);
      throw error;
    }
  }

  /**
   * Configure recording storage settings for Vapi
   * @param {Object} vapiInstance - The Vapi instance to configure
   * @param {string} subdomainOrAttorneyId - The subdomain or attorney ID to associate with recordings
   * @returns {Object} The configured Vapi instance
   */
  configureVapiRecording(vapiInstance, subdomainOrAttorneyId) {
    // Skip if vapiInstance is undefined or not properly initialized
    if (!vapiInstance || typeof vapiInstance.setRecordingStorage !== 'function') {
      console.log('Skipping Vapi recording configuration - vapiInstance not properly initialized');
      return;
    }
    
    try {
      // Skip actual Supabase configuration if we're using the stub
      if (typeof supabase.from !== 'function') {
        console.log('Using stub implementation for configureVapiRecording');
        return;
      }
      
      // Configure Vapi to store recordings in Supabase
      // This uses Vapi's built-in Supabase integration
      vapiInstance.configure({
        recordingEnabled: true,
        recordingProvider: 'supabase',
        recordingMetadata: {
          attorneyId: subdomainOrAttorneyId,
          source: 'legalscout-web'
        }
      });
      
      return vapiInstance;
    } catch (error) {
      console.error('Error configuring Vapi recording:', error);
      return vapiInstance;
    }
  }
}

// Export a singleton instance
export const supabaseService = new SupabaseService(); 