/**
 * Standalone Attorney Manager Fix
 * 
 * This service creates the missing window.standaloneAttorneyManager that useStandaloneAttorney hook expects.
 * It bridges the gap between AttorneyProfileManager and the hook system.
 */

console.log('🔧 [StandaloneAttorneyManagerFix] Initializing...');

export function createStandaloneAttorneyManager() {
  console.log('🔧 [StandaloneAttorneyManagerFix] Creating standalone attorney manager...');
  
  // Check if AttorneyProfileManager exists
  if (!window.attorneyProfileManager) {
    console.error('❌ [StandaloneAttorneyManagerFix] AttorneyProfileManager not found');
    return false;
  }

  // Check if already exists
  if (window.standaloneAttorneyManager) {
    console.log('✅ [StandaloneAttorneyManagerFix] Standalone attorney manager already exists');
    return true;
  }

  // Create the fallback manager
  const fallbackManager = {
    attorney: null,
    subscribers: [],
    
    // Subscribe to updates
    subscribe(callback) {
      console.log('[StandaloneAttorneyManagerFix] Adding subscriber');
      this.subscribers.push(callback);
      
      // Return unsubscribe function
      return () => {
        const index = this.subscribers.indexOf(callback);
        if (index > -1) {
          this.subscribers.splice(index, 1);
        }
      };
    },
    
    // Notify all subscribers
    notifySubscribers() {
      console.log('[StandaloneAttorneyManagerFix] Notifying subscribers:', this.subscribers.length);
      this.subscribers.forEach(callback => {
        try {
          callback(this.attorney);
        } catch (error) {
          console.error('[StandaloneAttorneyManagerFix] Error in subscriber callback:', error);
        }
      });
    },
    
    // Load from localStorage
    loadFromLocalStorage() {
      try {
        const stored = localStorage.getItem('attorney');
        if (stored) {
          const attorney = JSON.parse(stored);
          console.log('[StandaloneAttorneyManagerFix] Loaded from localStorage:', attorney.id);
          this.attorney = attorney;
          this.notifySubscribers();
          return attorney;
        }
      } catch (error) {
        console.error('[StandaloneAttorneyManagerFix] Error loading from localStorage:', error);
      }
      return null;
    },
    
    // Load attorney for user
    async loadAttorneyForUser(userId) {
      try {
        console.log('[StandaloneAttorneyManagerFix] Loading attorney for user:', userId);
        
        // Use AttorneyProfileManager to load attorney
        const attorney = await window.attorneyProfileManager.loadAttorneyByUserId(userId);
        
        if (attorney) {
          this.attorney = attorney;
          this.notifySubscribers();
          console.log('[StandaloneAttorneyManagerFix] ✅ Attorney loaded:', attorney.id);
          return attorney;
        } else {
          console.warn('[StandaloneAttorneyManagerFix] No attorney found for user:', userId);
          return null;
        }
      } catch (error) {
        console.error('[StandaloneAttorneyManagerFix] Error loading attorney:', error);
        throw error;
      }
    },
    
    // Update attorney
    async updateAttorney(updates) {
      try {
        if (!this.attorney) {
          throw new Error('No attorney loaded');
        }

        const updatedAttorney = { ...this.attorney, ...updates };

        // Update via AttorneyProfileManager (using correct method name)
        await window.attorneyProfileManager.updateAttorneyInSupabase(updatedAttorney);

        this.attorney = updatedAttorney;
        this.notifySubscribers();

        return updatedAttorney;
      } catch (error) {
        console.error('[StandaloneAttorneyManagerFix] Error updating attorney:', error);
        throw error;
      }
    },

    // Save to localStorage
    saveToLocalStorage() {
      try {
        if (this.attorney) {
          localStorage.setItem('attorney', JSON.stringify(this.attorney));
          localStorage.setItem('attorney_last_updated', new Date().toISOString());
          console.log('[StandaloneAttorneyManagerFix] Saved attorney to localStorage:', this.attorney.id);
        }
      } catch (error) {
        console.error('[StandaloneAttorneyManagerFix] Error saving to localStorage:', error);
      }
    }
  };
  
  // Initialize with current attorney from AttorneyProfileManager
  if (window.attorneyProfileManager.currentAttorney) {
    fallbackManager.attorney = window.attorneyProfileManager.currentAttorney;
    console.log('[StandaloneAttorneyManagerFix] Initialized with current attorney:', fallbackManager.attorney.id);
  } else {
    // Try to load from localStorage
    fallbackManager.loadFromLocalStorage();
  }
  
  // Set up listener to AttorneyProfileManager
  window.attorneyProfileManager.addListener((attorney) => {
    console.log('[StandaloneAttorneyManagerFix] AttorneyProfileManager updated, syncing...');
    fallbackManager.attorney = attorney;
    fallbackManager.notifySubscribers();
  });
  
  // Assign to global
  window.standaloneAttorneyManager = fallbackManager;
  
  console.log('✅ [StandaloneAttorneyManagerFix] Standalone attorney manager created');
  return true;
}

export function initializeStandaloneAttorneyManagerFix() {
  console.log('🔧 [StandaloneAttorneyManagerFix] Starting initialization...');
  
  // Function to apply the fix
  const applyFix = () => {
    // Wait for AttorneyProfileManager to be available
    if (!window.attorneyProfileManager) {
      console.log('⏳ [StandaloneAttorneyManagerFix] Waiting for AttorneyProfileManager...');
      setTimeout(applyFix, 500);
      return;
    }
    
    // Create the standalone attorney manager
    const success = createStandaloneAttorneyManager();
    
    if (success) {
      console.log('🎉 [StandaloneAttorneyManagerFix] Fix applied successfully!');
      
      // Trigger a refresh of the attorney context if attorney is available
      if (window.standaloneAttorneyManager.attorney) {
        window.standaloneAttorneyManager.notifySubscribers();
      }
      
      // Dispatch event to notify components
      window.dispatchEvent(new CustomEvent('standaloneAttorneyManagerReady', {
        detail: { success: true }
      }));
      
    } else {
      console.error('❌ [StandaloneAttorneyManagerFix] Failed to create standalone attorney manager');
    }
  };
  
  // Start the fix process
  applyFix();
}

// Auto-initialize when imported
initializeStandaloneAttorneyManagerFix();

console.log('✅ [StandaloneAttorneyManagerFix] Service loaded');
