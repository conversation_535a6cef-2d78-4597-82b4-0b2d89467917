/**
 * Utility functions for handling image storage in localStorage
 */

// Key prefix for storing images in localStorage
const IMAGE_STORAGE_PREFIX = 'legalscout_image_';

/**
 * Sanitize image data for logging
 * @param {string} imageData - Image data to sanitize
 * @returns {string} - Sanitized string for logging
 */
const sanitizeImageForLog = (imageData) => {
  if (!imageData) return 'null';

  if (imageData.startsWith('data:image/') && imageData.includes('base64,')) {
    const [prefix, base64Data] = imageData.split('base64,');
    if (base64Data && base64Data.length > 50) {
      return `${prefix}base64,[${base64Data.length} chars]`;
    }
  }

  if (imageData.length > 100) {
    return `[${imageData.length} chars]${imageData.slice(0, 50)}...`;
  }

  return imageData;
};

/**
 * Store an image in localStorage
 * @param {string} imageData - Base64 encoded image data
 * @returns {string} - Image ID for retrieval
 */
export const storeImage = (imageData) => {
  if (!imageData) return null;

  console.log('Storing image data:', sanitizeImageForLog(imageData));

  // Generate a unique ID for the image
  const imageId = `${IMAGE_STORAGE_PREFIX}${Date.now()}`;

  try {
    // Store the image data in localStorage
    localStorage.setItem(imageId, imageData);
    console.log('Image stored successfully with ID:', imageId);
    return imageId;
  } catch (error) {
    console.error('Error storing image in localStorage:', error);
    return null;
  }
};

/**
 * Retrieve an image from localStorage
 * @param {string} imageId - Image ID
 * @returns {string|null} - Base64 encoded image data or null if not found
 */
export const retrieveImage = (imageId) => {
  if (!imageId) return null;

  try {
    // Check if the imageId is already a data URL
    if (imageId.startsWith('data:')) {
      return imageId;
    }

    // Check if the imageId is a URL
    if (imageId.startsWith('http')) {
      return imageId;
    }

    // Check if the imageId is a local path
    if (imageId.startsWith('/')) {
      return imageId;
    }

    // Retrieve the image data from localStorage
    return localStorage.getItem(imageId);
  } catch (error) {
    console.error('Error retrieving image from localStorage:', error);
    return null;
  }
};

/**
 * Remove an image from localStorage
 * @param {string} imageId - Image ID
 */
export const removeImage = (imageId) => {
  if (!imageId) return;

  try {
    // Remove the image data from localStorage
    localStorage.removeItem(imageId);
  } catch (error) {
    console.error('Error removing image from localStorage:', error);
  }
};

/**
 * Check if a string is an image ID
 * @param {string} str - String to check
 * @returns {boolean} - True if the string is an image ID
 */
export const isImageId = (str) => {
  if (!str) return false;
  return str.startsWith(IMAGE_STORAGE_PREFIX);
};

/**
 * Process an image URL or ID
 * If it's an image ID, retrieve the image data from localStorage
 * Otherwise, return the URL as is
 * @param {string} imageUrlOrId - Image URL or ID
 * @returns {string} - Processed image URL
 */
export const processImageUrl = (imageUrlOrId) => {
  if (!imageUrlOrId) return null;

  console.log('Processing image URL or ID:', sanitizeImageForLog(imageUrlOrId));

  // If it's an image ID, retrieve the image data from localStorage
  if (isImageId(imageUrlOrId)) {
    console.log('Image is an ID, retrieving from localStorage');
    const image = retrieveImage(imageUrlOrId);
    console.log('Retrieved image:', image ? 'Image found' : 'Image not found');
    return image;
  }

  // If it's a data URL, return as is
  if (imageUrlOrId.startsWith('data:')) {
    console.log('Image is a data URL');
    return imageUrlOrId;
  }

  // If it's an absolute URL, check if it's a production URL
  if (imageUrlOrId.startsWith('http')) {
    console.log('Image is an absolute URL');

    // Check if it's a production URL that won't work in local development
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const isProductionUrl = imageUrlOrId.includes('legalscout.ai/static/media');

    if (isLocalhost && isProductionUrl) {
      console.log('Detected production URL in local environment, using fallback');
      return '/PRIMARY CLEAR.png';
    }

    return imageUrlOrId;
  }

  // If it's a relative URL, return as is
  if (imageUrlOrId.startsWith('/')) {
    console.log('Image is a relative URL');
    return imageUrlOrId;
  }

  // Otherwise, return the URL as is
  console.log('Image is of unknown type, returning as is');
  return imageUrlOrId;
};
