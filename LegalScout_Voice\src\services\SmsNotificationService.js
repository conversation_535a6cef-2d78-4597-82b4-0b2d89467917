/**
 * SMS Notification Service
 *
 * This service handles sending SMS notifications to attorneys about ongoing calls.
 * It integrates with Vapi's SMS capabilities and generates secure call control links.
 */

import { supabase } from '../lib/supabase';
import { enhancedVapiCallService } from './EnhancedVapiCallService';
import { vapiMcpService } from './vapiMcpService';
import { createCallControlToken } from '../utils/tokenUtils';

class SmsNotificationService {
  constructor() {
    this.initialized = false;
    this.callService = enhancedVapiCallService;
    this.mcpService = vapiMcpService;
    this.notificationTemplates = {
      callStarted: 'You have a new call from {phoneNumber}. Click here to monitor and control: {callControlUrl}',
      callInProgress: 'Call with {phoneNumber} is in progress. Legal issue: {legalIssue}. Click here to monitor: {callControlUrl}',
      callEnded: 'Call with {phoneNumber} has ended. Duration: {duration} minutes. Click here to view summary: {callControlUrl}'
    };
  }

  /**
   * Initialize the service
   * @returns {Promise<boolean>} - Whether initialization was successful
   */
  async initialize() {
    try {
      if (this.initialized) {
        return true;
      }

      // Initialize call service if not already initialized
      await this.callService.initialize();

      this.initialized = true;
      return true;
    } catch (error) {
      console.error('[SmsNotificationService] Initialization error:', error);
      return false;
    }
  }

  /**
   * Send an SMS notification
   * @param {string} phoneNumber - The phone number to send to
   * @param {string} message - The message to send
   * @returns {Promise<Object>} - The result of the SMS send operation
   */
  async sendSms(phoneNumber, message) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        const initResult = await this.initialize();
        if (!initResult) {
          throw new Error('Failed to initialize SMS notification service');
        }
      }

      if (!phoneNumber) {
        throw new Error('Phone number is required');
      }

      // Get Vapi API key
      const apiKey = import.meta.env.VITE_VAPI_SECRET_KEY || import.meta.env.VAPI_TOKEN;

      if (!apiKey) {
        throw new Error('Vapi API key not configured');
      }

      console.log(`[SmsNotificationService] Sending SMS to ${phoneNumber}`);

      // PRODUCTION FIX: Use clean fetch to bypass interceptors
      let cleanFetch;
      try {
        // Method 1: Try to get original fetch from iframe
        if (typeof document !== 'undefined') {
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          document.body.appendChild(iframe);

          cleanFetch = iframe.contentWindow.fetch.bind(window);
          document.body.removeChild(iframe);

          console.log('[SmsNotificationService] Using clean fetch from iframe');
        } else {
          cleanFetch = fetch;
        }
      } catch (error) {
        console.warn('[SmsNotificationService] Could not create clean fetch, using standard fetch:', error.message);
        cleanFetch = fetch;
      }

      // Send SMS using Vapi's SMS API with clean fetch
      const response = await cleanFetch('https://api.vapi.ai/sms', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: phoneNumber,
          message
        })
      });

      if (!response.ok) {
        throw new Error(`SMS API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log(`[SmsNotificationService] SMS sent successfully to ${phoneNumber}`);
      return result;
    } catch (error) {
      console.error('[SmsNotificationService] Error sending SMS:', error);

      // In development, return a mock response
      if (process.env.NODE_ENV === 'development') {
        console.log('[SmsNotificationService] Returning mock SMS response in development');
        return {
          id: 'mock-sms-' + Date.now(),
          status: 'sent',
          to: phoneNumber,
          message: message,
          mock: true
        };
      }

      throw error;
    }
  }

  /**
   * Generate a call control URL
   * @param {string} callId - The ID of the call
   * @param {string} attorneyId - The ID of the attorney
   * @param {Object} options - Token options
   * @returns {string} - The call control URL
   */
  generateCallControlUrl(callId, attorneyId, options = {}) {
    // Generate token
    const token = createCallControlToken(callId, attorneyId, options);

    // Create URL
    const baseUrl = typeof window !== 'undefined'
      ? window.location.origin
      : options.baseUrl || 'https://legalscout.app';

    return `${baseUrl}/call-control?token=${token}`;
  }

  /**
   * Send a call started notification
   * @param {string} callId - The ID of the call
   * @param {string} attorneyId - The ID of the attorney
   * @returns {Promise<Object>} - The result of the SMS send operation
   */
  async sendCallStartedNotification(callId, attorneyId) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Get attorney details
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();

      if (error || !attorney) {
        throw new Error(`Attorney not found: ${attorneyId}`);
      }

      if (!attorney.phone) {
        console.warn(`Attorney ${attorneyId} has no phone number, skipping notification`);
        return null;
      }

      // Get call details
      const call = await this.callService.getCall(callId);

      if (!call) {
        throw new Error(`Call not found: ${callId}`);
      }

      // Generate call control URL
      const callControlUrl = this.generateCallControlUrl(callId, attorneyId, { expiresIn: '24h' });

      // Create message from template
      const message = this.notificationTemplates.callStarted
        .replace('{phoneNumber}', call.customer?.phoneNumber || 'a potential client')
        .replace('{callControlUrl}', callControlUrl);

      // Send SMS
      return await this.sendSms(attorney.phone, message);
    } catch (error) {
      console.error('[SmsNotificationService] Error sending call started notification:', error);
      throw error;
    }
  }

  /**
   * Send a call in progress notification with legal issue details
   * @param {string} callId - The ID of the call
   * @param {string} attorneyId - The ID of the attorney
   * @param {Object} callData - Additional call data
   * @returns {Promise<Object>} - The result of the SMS send operation
   */
  async sendCallInProgressNotification(callId, attorneyId, callData = {}) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Get attorney details
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();

      if (error || !attorney) {
        throw new Error(`Attorney not found: ${attorneyId}`);
      }

      if (!attorney.phone) {
        console.warn(`Attorney ${attorneyId} has no phone number, skipping notification`);
        return null;
      }

      // Get call details
      const call = await this.callService.getCall(callId);

      if (!call) {
        throw new Error(`Call not found: ${callId}`);
      }

      // Generate call control URL
      const callControlUrl = this.generateCallControlUrl(callId, attorneyId, { expiresIn: '24h' });

      // Create message from template
      const message = this.notificationTemplates.callInProgress
        .replace('{phoneNumber}', call.customer?.phoneNumber || 'a potential client')
        .replace('{legalIssue}', callData.legalIssue || 'Not specified yet')
        .replace('{callControlUrl}', callControlUrl);

      // Send SMS
      return await this.sendSms(attorney.phone, message);
    } catch (error) {
      console.error('[SmsNotificationService] Error sending call in progress notification:', error);
      throw error;
    }
  }

  /**
   * Send a call ended notification
   * @param {string} callId - The ID of the call
   * @param {string} attorneyId - The ID of the attorney
   * @returns {Promise<Object>} - The result of the SMS send operation
   */
  async sendCallEndedNotification(callId, attorneyId) {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.initialize();
      }

      // Get attorney details
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();

      if (error || !attorney) {
        throw new Error(`Attorney not found: ${attorneyId}`);
      }

      if (!attorney.phone) {
        console.warn(`Attorney ${attorneyId} has no phone number, skipping notification`);
        return null;
      }

      // Get call details
      const call = await this.callService.getCall(callId);

      if (!call) {
        throw new Error(`Call not found: ${callId}`);
      }

      // Generate call control URL
      const callControlUrl = this.generateCallControlUrl(callId, attorneyId, { expiresIn: '24h' });

      // Calculate duration in minutes
      const durationMinutes = Math.round((call.duration || 0) / 60);

      // Create message from template
      const message = this.notificationTemplates.callEnded
        .replace('{phoneNumber}', call.customer?.phoneNumber || 'a potential client')
        .replace('{duration}', durationMinutes.toString())
        .replace('{callControlUrl}', callControlUrl);

      // Send SMS
      return await this.sendSms(attorney.phone, message);
    } catch (error) {
      console.error('[SmsNotificationService] Error sending call ended notification:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const smsNotificationService = new SmsNotificationService();
export default smsNotificationService;
