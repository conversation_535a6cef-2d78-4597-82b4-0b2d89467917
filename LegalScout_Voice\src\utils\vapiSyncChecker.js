/**
 * Vapi Sync Checker Utility
 * 
 * This utility checks if attorney data in Supabase is synchronized with their Vapi assistant
 * and provides automatic sync functionality.
 */

import { enhancedVapiAssistantManager } from '../services/EnhancedVapiAssistantManager.js';

/**
 * Check if attorney data is in sync with their Vapi assistant
 * @param {Object} attorney - The attorney object from Supabase
 * @returns {Promise<Object>} - Sync status and differences
 */
export async function checkAttorneySyncStatus(attorney) {
  try {
    if (!attorney.vapi_assistant_id) {
      return { 
        inSync: false, 
        reason: 'NO_ASSISTANT_ID', 
        needsCreation: true,
        action: 'CREATE_ASSISTANT'
      };
    }

    // Initialize the enhanced manager
    await enhancedVapiAssistantManager.initialize();

    // Get current assistant from Vapi
    let vapiAssistant;
    try {
      vapiAssistant = await enhancedVapiAssistantManager.mcpService.getAssistant(attorney.vapi_assistant_id);
    } catch (error) {
      if (error.message.includes('404') || error.message.includes('not found')) {
        return { 
          inSync: false, 
          reason: 'ASSISTANT_NOT_FOUND', 
          needsCreation: true,
          action: 'CREATE_ASSISTANT',
          error: 'Assistant ID exists in database but not in Vapi'
        };
      }
      throw error;
    }

    if (!vapiAssistant) {
      return { 
        inSync: false, 
        reason: 'ASSISTANT_NOT_FOUND', 
        needsCreation: true,
        action: 'CREATE_ASSISTANT'
      };
    }

    // Compare key fields
    const differences = [];
    
    const expectedName = `${attorney.firm_name || 'Legal'} Assistant`;
    if (vapiAssistant.name !== expectedName) {
      differences.push({ 
        field: 'name', 
        supabase: expectedName, 
        vapi: vapiAssistant.name 
      });
    }

    const expectedFirstMessage = attorney.welcome_message || 'Hello, how can I help you with your legal needs today?';
    if (vapiAssistant.firstMessage !== expectedFirstMessage) {
      differences.push({ 
        field: 'firstMessage', 
        supabase: expectedFirstMessage, 
        vapi: vapiAssistant.firstMessage || 'Not set'
      });
    }

    const expectedInstructions = attorney.vapi_instructions || 'You are a legal assistant helping potential clients understand their legal needs.';
    const currentInstructions = vapiAssistant.model?.messages?.[0]?.content || '';
    if (currentInstructions !== expectedInstructions) {
      differences.push({ 
        field: 'instructions', 
        supabase: expectedInstructions, 
        vapi: currentInstructions || 'Not set'
      });
    }

    const inSync = differences.length === 0;
    return { 
      inSync, 
      differences, 
      reason: inSync ? 'IN_SYNC' : 'DATA_MISMATCH',
      action: inSync ? 'NO_ACTION_NEEDED' : 'SYNC_REQUIRED',
      needsSync: !inSync,
      vapiAssistant
    };

  } catch (error) {
    console.error('[VapiSyncChecker] Error checking sync status:', error);
    return { 
      inSync: false, 
      reason: 'ERROR', 
      action: 'ERROR',
      error: error.message 
    };
  }
}

/**
 * Automatically sync attorney data to Vapi if needed
 * @param {Object} attorney - The attorney object from Supabase
 * @param {boolean} forceSync - Force sync even if data appears to be in sync
 * @returns {Promise<Object>} - Sync result
 */
export async function autoSyncAttorney(attorney, forceSync = false) {
  try {
    console.log(`[VapiSyncChecker] Checking sync status for attorney ${attorney.id}`);

    // Check current sync status
    const syncStatus = await checkAttorneySyncStatus(attorney);
    
    if (syncStatus.inSync && !forceSync) {
      console.log('[VapiSyncChecker] Attorney data is already in sync');
      return { 
        success: true, 
        action: 'NO_SYNC_NEEDED', 
        syncStatus,
        message: 'Attorney data is already synchronized with Vapi'
      };
    }

    if (syncStatus.reason === 'ERROR') {
      console.error('[VapiSyncChecker] Error checking sync status:', syncStatus.error);
      return { 
        success: false, 
        action: 'ERROR', 
        error: syncStatus.error 
      };
    }

    // Perform sync based on the required action
    console.log(`[VapiSyncChecker] Sync required: ${syncStatus.action}`);

    if (syncStatus.differences && syncStatus.differences.length > 0) {
      console.log('[VapiSyncChecker] Differences found:', syncStatus.differences);
    }

    // Check for assistant ID conflicts before syncing
    if (attorney.vapi_assistant_id) {
      const { checkAssistantIdConflicts } = await import('../services/syncHelpers');
      const conflictCheck = await checkAssistantIdConflicts(attorney.vapi_assistant_id, attorney.id);

      if (conflictCheck.hasConflicts) {
        console.warn(`[VapiSyncChecker] Assistant ID conflict detected for ${attorney.email}:`, conflictCheck.conflicts);
        console.warn(`[VapiSyncChecker] Using enhanced sync helpers to resolve conflict`);

        // Use the enhanced sync helpers which handle conflicts
        const { ensureProfilePersistence } = await import('../services/syncHelpers');
        const syncResult = await ensureProfilePersistence({
          attorneyId: attorney.id,
          localData: attorney,
          forceUpdate: true
        });

        return {
          success: syncResult.success,
          action: syncResult.action,
          syncStatus,
          syncResult,
          conflictResolved: true,
          message: `Conflict resolved: ${syncResult.message}`
        };
      }
    }

    // Use the enhanced manager to sync
    const syncResult = await enhancedVapiAssistantManager.syncAssistant(attorney);
    
    return {
      success: true,
      action: 'SYNCED',
      syncStatus,
      syncResult,
      message: `Successfully synchronized attorney data to Vapi assistant`
    };

  } catch (error) {
    console.error('[VapiSyncChecker] Error during auto-sync:', error);
    return {
      success: false,
      action: 'ERROR',
      error: error.message
    };
  }
}

/**
 * Check sync status for multiple attorneys
 * @param {Array} attorneys - Array of attorney objects from Supabase
 * @returns {Promise<Object>} - Summary of sync statuses
 */
export async function checkMultipleAttorneySync(attorneys) {
  const results = {
    total: attorneys.length,
    inSync: 0,
    needsSync: 0,
    needsCreation: 0,
    errors: 0,
    details: []
  };

  for (const attorney of attorneys) {
    try {
      const syncStatus = await checkAttorneySyncStatus(attorney);
      
      if (syncStatus.inSync) {
        results.inSync++;
      } else if (syncStatus.needsCreation) {
        results.needsCreation++;
      } else if (syncStatus.needsSync) {
        results.needsSync++;
      }
      
      if (syncStatus.reason === 'ERROR') {
        results.errors++;
      }

      results.details.push({
        attorneyId: attorney.id,
        subdomain: attorney.subdomain,
        firmName: attorney.firm_name,
        syncStatus
      });

    } catch (error) {
      results.errors++;
      results.details.push({
        attorneyId: attorney.id,
        subdomain: attorney.subdomain,
        firmName: attorney.firm_name,
        syncStatus: { inSync: false, reason: 'ERROR', error: error.message }
      });
    }
  }

  return results;
}

/**
 * Add sync checking to dashboard startup
 * This function should be called when the dashboard loads
 * @param {Object} attorney - The current attorney object
 * @returns {Promise<Object>} - Sync check result
 */
export async function dashboardSyncCheck(attorney) {
  console.log('[VapiSyncChecker] Performing dashboard startup sync check');

  // Import conflict checking function
  const { checkAssistantIdConflicts } = await import('../services/syncHelpers');
  
  try {
    const syncStatus = await checkAttorneySyncStatus(attorney);
    
    if (!syncStatus.inSync) {
      console.warn('[VapiSyncChecker] Attorney data is out of sync with Vapi:', syncStatus);
      
      // Optionally auto-sync (you can make this configurable)
      const autoSyncResult = await autoSyncAttorney(attorney);
      
      return {
        syncCheckPerformed: true,
        wasInSync: false,
        autoSyncAttempted: true,
        autoSyncResult,
        originalSyncStatus: syncStatus
      };
    }

    console.log('[VapiSyncChecker] Attorney data is in sync with Vapi');
    return {
      syncCheckPerformed: true,
      wasInSync: true,
      autoSyncAttempted: false,
      syncStatus
    };

  } catch (error) {
    console.error('[VapiSyncChecker] Error during dashboard sync check:', error);
    return {
      syncCheckPerformed: true,
      wasInSync: false,
      autoSyncAttempted: false,
      error: error.message
    };
  }
}
