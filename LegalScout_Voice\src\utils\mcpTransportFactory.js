/**
 * MCP Transport Factory
 *
 * This utility provides functions for creating MCP clients with different transport methods.
 * It supports both SSE and Streamable HTTP transports, with fallback mechanisms.
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamable-http.js';
import { mcpConfig } from '../config/mcp.config';

/**
 * Creates an MCP client with the appropriate transport
 * @param {Object} options - Configuration options
 * @param {string} options.transportType - 'sse' or 'http'
 * @param {string} options.url - URL for the transport
 * @param {Object} options.headers - Headers for the transport
 * @param {string} options.clientName - Name for the client
 * @param {string} options.clientVersion - Version for the client
 * @returns {Promise<Object>} - MCP client
 */
export const createMcpClient = async (options = {}) => {
  const {
    transportType = 'sse',
    url = 'https://mcp.vapi.ai/sse',
    headers = {},
    clientName = 'legalscout-vapi-client',
    clientVersion = '1.0.0'
  } = options;
  
  let transport;
  
  // Create transport based on type
  if (transportType === 'sse') {
    transport = new SSEClientTransport({
      url,
      headers
    });
  } else if (transportType === 'http') {
    transport = new StreamableHTTPClientTransport({
      url: url.replace('/sse', '/mcp'),
      requestInit: {
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      }
    });
  } else {
    throw new Error(`Unsupported transport type: ${transportType}`);
  }
  
  // Create client
  const client = new Client({
    transport,
    name: clientName,
    version: clientVersion
  });
  
  // Connect client
  await client.connect();
  
  return client;
};

/**
 * Creates an MCP client with fallback
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} - MCP client
 */
export const createMcpClientWithFallback = async (options = {}) => {
  try {
    // Try SSE first
    console.log('[mcpTransportFactory] Trying SSE transport');
    return await createMcpClient({
      ...options,
      transportType: 'sse'
    });
  } catch (sseError) {
    console.warn('[mcpTransportFactory] SSE transport failed, trying HTTP:', sseError);
    
    try {
      // Try HTTP as fallback
      return await createMcpClient({
        ...options,
        transportType: 'http'
      });
    } catch (httpError) {
      console.error('[mcpTransportFactory] All transport methods failed:', httpError);
      throw new Error('Failed to create MCP client: all transport methods failed');
    }
  }
};

/**
 * Creates an MCP client with the appropriate transport based on configuration
 * @param {string} apiKey - API key for authentication
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - MCP client
 */
export const createConfiguredMcpClient = async (apiKey, options = {}) => {
  const {
    clientName = 'legalscout-vapi-client',
    clientVersion = '1.0.0',
    preferredTransport = null
  } = options;
  
  // Sanitize API key
  const sanitizedApiKey = apiKey.trim();
  
  // Set up headers
  const headers = {
    'Authorization': `Bearer ${sanitizedApiKey}`,
    'Content-Type': 'application/json'
  };
  
  // If preferred transport is specified, use it
  if (preferredTransport) {
    return await createMcpClient({
      transportType: preferredTransport,
      url: mcpConfig.voice.vapi.mcpUrl,
      headers,
      clientName,
      clientVersion
    });
  }
  
  // Otherwise use fallback mechanism
  return await createMcpClientWithFallback({
    url: mcpConfig.voice.vapi.mcpUrl,
    headers,
    clientName,
    clientVersion
  });
};

export default {
  createMcpClient,
  createMcpClientWithFallback,
  createConfiguredMcpClient
};
