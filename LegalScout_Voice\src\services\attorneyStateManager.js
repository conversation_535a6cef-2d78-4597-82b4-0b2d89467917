/**
 * Attorney State Manager
 *
 * A robust service for managing attorney state across the application.
 * Handles loading, saving, and synchronizing attorney profiles between
 * Supabase, Vapi, and local storage.
 */

import { getSupabaseClient } from '../lib/supabase-fixed';
import {
  fetchFromVapi,
  createVapiAssistant,
  updateVapiAssistant
} from './syncHelpers';

/**
 * Sanitize attorney data for logging by removing/truncating sensitive or large data
 * @param {Object} attorney - Attorney data to sanitize
 * @returns {Object} - Sanitized attorney data for logging
 */
const sanitizeAttorneyForLog = (attorney) => {
  if (!attorney) return attorney;

  const sanitized = { ...attorney };

  // Sanitize image fields
  ['logo_url', 'profile_image_url', 'background_image_url'].forEach(field => {
    if (sanitized[field] && typeof sanitized[field] === 'string') {
      if (sanitized[field].startsWith('data:image/') && sanitized[field].includes('base64,')) {
        const [prefix, base64Data] = sanitized[field].split('base64,');
        if (base64Data && base64Data.length > 50) {
          sanitized[field] = `${prefix}base64,[${base64Data.length} chars]`;
        }
      } else if (sanitized[field].length > 100) {
        sanitized[field] = `[${sanitized[field].length} chars]${sanitized[field].slice(0, 50)}...`;
      }
    }
  });

  // Sanitize long text fields
  ['vapi_instructions', 'welcome_message'].forEach(field => {
    if (sanitized[field] && typeof sanitized[field] === 'string' && sanitized[field].length > 200) {
      sanitized[field] = `[${sanitized[field].length} chars]${sanitized[field].slice(0, 100)}...`;
    }
  });

  return sanitized;
};

// Constants
const STORAGE_KEYS = {
  ATTORNEY: 'attorney',
  ATTORNEY_ID: 'attorney_id',
  ATTORNEY_VERSION: 'attorney_version',
  LAST_SYNC: 'attorney_last_sync'
};

const DEFAULT_RETRY_OPTIONS = {
  maxRetries: 3,
  retryDelay: 1000,
  backoffFactor: 1.5
};

// UUID validation regex
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

/**
 * Attorney State Manager Class
 */
class AttorneyStateManager {
  constructor() {
    this.attorney = null;
    this.isLoading = false;
    this.isSaving = false;
    this.isSyncing = false;
    this.lastError = null;
    this.subscribers = [];
    this.initialized = false;

    // Bind methods
    this.initialize = this.initialize.bind(this);
    this.loadAttorney = this.loadAttorney.bind(this);
    this.saveAttorney = this.saveAttorney.bind(this);
    this.syncWithVapi = this.syncWithVapi.bind(this);
    this.createAttorney = this.createAttorney.bind(this);
    this.updateAttorney = this.updateAttorney.bind(this);
    this.subscribe = this.subscribe.bind(this);
    this.unsubscribe = this.unsubscribe.bind(this);

    // Auto-initialize - DISABLED to prevent conflicts
    // this.initialize();
  }

  /**
   * Initialize the state manager
   */
  async initialize() {
    console.log('[AttorneyStateManager] Initializing... (but auto-sync disabled)');

    if (this.initialized) {
      console.log('[AttorneyStateManager] Already initialized');
      return;
    }

    try {
      // Load attorney from all available sources - Keep this for manual calls if needed
      await this.loadAttorney();

      // Set up automatic sync interval (every 5 minutes) - DISABLED
      /*
      this.syncInterval = setInterval(() => {
        this.syncWithVapi({ force: false });
      }, 5 * 60 * 1000);
      */

      this.initialized = true;
      console.log('[AttorneyStateManager] Initialization complete (auto-sync disabled)');
    } catch (error) {
      console.error('[AttorneyStateManager] Initialization failed:', error);
      this.lastError = error;
    }
  }

  /**
   * Validate a UUID
   * @param {string} uuid - The UUID to validate
   * @returns {boolean} Whether the UUID is valid
   */
  isValidUUID(uuid) {
    if (!uuid || typeof uuid !== 'string') return false;
    return UUID_REGEX.test(uuid);
  }

  /**
   * Generate a fallback UUID
   * @returns {string} A valid UUID
   */
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Create a default attorney object
   * @param {Object} overrides - Properties to override in the default attorney
   * @returns {Object} A default attorney object
   */
  createDefaultAttorney(overrides = {}) {
    const defaultAttorney = {
      id: this.generateUUID(),
      subdomain: 'default',
      firm_name: 'Your Law Firm',
      name: 'Your Name',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      voice_provider: '11labs',
      voice_id: 'sarah',
      welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
      information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
      vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.'
    };

    return { ...defaultAttorney, ...overrides };
  }

  /**
   * Load attorney from all available sources
   * @param {Object} options - Loading options
   * @param {boolean} options.force - Whether to force a reload from Supabase
   * @param {boolean} options.fallbackToLocal - Whether to fall back to local storage if Supabase fails
   * @returns {Promise<Object>} The loaded attorney
   */
  async loadAttorney(options = {}) {
    const { force = false, fallbackToLocal = true } = options;

    if (this.isLoading) {
      console.log('[AttorneyStateManager] Already loading attorney');
      return this.attorney;
    }

    this.isLoading = true;
    this.lastError = null;

    try {
      console.log('[AttorneyStateManager] Loading attorney...');

      // Try to get from local storage first (for immediate display)
      let localAttorney = null;
      try {
        const storedAttorney = localStorage.getItem(STORAGE_KEYS.ATTORNEY);
        if (storedAttorney) {
          localAttorney = JSON.parse(storedAttorney);
          console.log('[AttorneyStateManager] Loaded attorney from local storage:', localAttorney.id);

          // If not forcing a reload and we have a local attorney, use it
          if (!force && localAttorney && this.isValidUUID(localAttorney.id)) {
            this.attorney = localAttorney;
            this.notifySubscribers();
          }
        }
      } catch (localError) {
        console.error('[AttorneyStateManager] Error loading from local storage:', localError);
      }

      // Try to get from Supabase (source of truth)
      let supabaseAttorney = null;
      try {
        // First try to get by ID if we have one
        if (localAttorney && this.isValidUUID(localAttorney.id)) {
          const { data, error } = await supabase
            .from('attorneys')
            .select('*')
            .eq('id', localAttorney.id)
            .single();

          if (data && !error) {
            supabaseAttorney = data;
            console.log('[AttorneyStateManager] Loaded attorney from Supabase by ID:', supabaseAttorney.id);
          }
        }

        // If that fails, try to get by user ID
        if (!supabaseAttorney) {
          // Get user ID from auth
          const supabase = await getSupabaseClient();
          const { data: { user } } = await supabase.auth.getUser();

          if (user && this.isValidUUID(user.id)) {
            const { data, error } = await supabase
              .from('attorneys')
              .select('*')
              .eq('user_id', user.id)
              .single();

            if (data && !error) {
              supabaseAttorney = data;
              console.log('[AttorneyStateManager] Loaded attorney from Supabase by user ID:', supabaseAttorney.id);
            }
          }
        }

        // If we found a Supabase attorney, use it and update local storage
        if (supabaseAttorney) {
          this.attorney = supabaseAttorney;
          this.saveToLocalStorage(supabaseAttorney);
          this.notifySubscribers();
        }
      } catch (supabaseError) {
        console.error('[AttorneyStateManager] Error loading from Supabase:', supabaseError);

        // If we should fall back to local storage and we have a local attorney
        if (fallbackToLocal && localAttorney && this.isValidUUID(localAttorney.id)) {
          console.log('[AttorneyStateManager] Falling back to local storage');
          this.attorney = localAttorney;
          this.notifySubscribers();
        }
      }

      // If we still don't have an attorney, create a default one
      if (!this.attorney) {
        console.log('[AttorneyStateManager] No attorney found, creating default');

        // Get user ID from auth
        let userId = null;
        try {
          const supabase = await getSupabaseClient();
          const { data: { user } } = await supabase.auth.getUser();
          userId = user?.id;
        } catch (authError) {
          console.error('[AttorneyStateManager] Error getting user ID:', authError);
        }

        // Create default attorney
        const defaultAttorney = this.createDefaultAttorney({ user_id: userId });

        // Try to save to Supabase
        try {
          const { data, error } = await supabase
            .from('attorneys')
            .insert(defaultAttorney)
            .select()
            .single();

          if (data && !error) {
            this.attorney = data;
            console.log('[AttorneyStateManager] Created default attorney in Supabase:', data.id);
          } else {
            this.attorney = defaultAttorney;
            console.log('[AttorneyStateManager] Using local default attorney:', defaultAttorney.id);
          }
        } catch (createError) {
          console.error('[AttorneyStateManager] Error creating default attorney:', createError);
          this.attorney = defaultAttorney;
        }

        // Save to local storage
        this.saveToLocalStorage(this.attorney);
        this.notifySubscribers();
      }

      // Sync with Vapi if we have an attorney
      if (this.attorney) {
        this.syncWithVapi({ force: false });
      }

      return this.attorney;
    } catch (error) {
      console.error('[AttorneyStateManager] Error loading attorney:', error);
      this.lastError = error;
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Save attorney to local storage
   * @param {Object} attorney - The attorney to save
   */
  saveToLocalStorage(attorney) {
    if (!attorney || !this.isValidUUID(attorney.id)) {
      console.warn('[AttorneyStateManager] Cannot save invalid attorney to local storage');
      return;
    }

    try {
      // Save full attorney object
      localStorage.setItem(STORAGE_KEYS.ATTORNEY, JSON.stringify(attorney));

      // Save ID separately for redundancy
      localStorage.setItem(STORAGE_KEYS.ATTORNEY_ID, attorney.id);

      // Save version and timestamp
      localStorage.setItem(STORAGE_KEYS.ATTORNEY_VERSION, Date.now().toString());

      console.log('[AttorneyStateManager] Saved attorney to local storage:', attorney.id);
    } catch (error) {
      console.error('[AttorneyStateManager] Error saving to local storage:', error);
    }
  }

  /**
   * Save attorney to Supabase
   * @param {Object} attorney - The attorney to save
   * @param {Object} options - Save options
   * @param {boolean} options.updateLocalStorage - Whether to update local storage
   * @returns {Promise<Object>} The saved attorney
   */
  async saveAttorney(attorney, options = {}) {
    const { updateLocalStorage = true } = options;

    if (this.isSaving) {
      console.log('[AttorneyStateManager] Already saving attorney');
      return this.attorney;
    }

    this.isSaving = true;
    this.lastError = null;

    try {
      console.log('[AttorneyStateManager] Saving attorney...');

      // Ensure we have a valid attorney
      if (!attorney || !this.isValidUUID(attorney.id)) {
        throw new Error('Invalid attorney object');
      }

      // Update timestamp
      attorney.updated_at = new Date().toISOString();

      // Save to Supabase
      const { data, error } = await supabase
        .from('attorneys')
        .upsert(attorney)
        .select()
        .single();

      if (error) {
        throw error;
      }

      console.log('[AttorneyStateManager] Saved attorney to Supabase:', data.id);

      // Update local state
      this.attorney = data;

      // Update local storage if requested
      if (updateLocalStorage) {
        this.saveToLocalStorage(data);
      }

      // Notify subscribers
      this.notifySubscribers();

      return data;
    } catch (error) {
      console.error('[AttorneyStateManager] Error saving attorney:', error);
      this.lastError = error;
      throw error;
    } finally {
      this.isSaving = false;
    }
  }

  /**
   * Sync attorney with Vapi
   * @param {Object} options - Sync options
   * @param {boolean} options.force - Whether to force a sync even if no changes
   * @returns {Promise<Object>} The sync result
   */
  async syncWithVapi(options = {}) {
    const { force = false } = options;

    if (this.isSyncing) {
      console.log('[AttorneyStateManager] Already syncing with Vapi');
      return null;
    }

    this.isSyncing = true;
    this.lastError = null;

    try {
      console.log('[AttorneyStateManager] Syncing with Vapi...');

      // Ensure we have a valid attorney
      if (!this.attorney || !this.isValidUUID(this.attorney.id)) {
        throw new Error('No valid attorney to sync');
      }

      // Check if attorney has a Vapi assistant ID
      let vapiAssistantId = this.attorney.vapi_assistant_id;
      let vapiAssistant = null;

      // If they have an assistant ID, fetch the assistant data from Vapi
      if (vapiAssistantId && this.isValidUUID(vapiAssistantId)) {
        try {
          vapiAssistant = await fetchFromVapi(vapiAssistantId);
          console.log('[AttorneyStateManager] Found Vapi assistant:', vapiAssistantId);
        } catch (vapiError) {
          console.error('[AttorneyStateManager] Error fetching Vapi assistant:', vapiError);
          vapiAssistantId = null; // Reset if assistant doesn't exist
        }
      }

      // If no assistant ID or assistant not found, create a new one
      if (!vapiAssistantId || !vapiAssistant) {
        console.log('[AttorneyStateManager] Creating new Vapi assistant');

        // Create default values for missing fields
        const firmName = this.attorney.firm_name || 'Your Law Firm';
        const welcomeMessage = this.attorney.welcome_message || `Hello, I'm the legal assistant for ${firmName}. How can I help you today?`;
        const vapiInstructions = this.attorney.vapi_instructions || `You are a legal assistant for ${firmName}. Help potential clients understand their legal needs and collect relevant information.`;
        const voiceProvider = this.attorney.voice_provider || '11labs';
        const voiceId = this.attorney.voice_id || 'sarah';

        // Create new assistant
        const newAssistant = await createVapiAssistant({
          name: firmName,
          firstMessage: welcomeMessage,
          instructions: vapiInstructions,
          voice: {
            provider: voiceProvider,
            voiceId: voiceId
          }
        });

        console.log('[AttorneyStateManager] Created new Vapi assistant:', newAssistant.id);

        // Update attorney with new assistant ID
        this.attorney.vapi_assistant_id = newAssistant.id;

        // Save to Supabase
        await this.saveAttorney(this.attorney);

        // Update last sync time
        localStorage.setItem(STORAGE_KEYS.LAST_SYNC, Date.now().toString());

        return {
          action: 'created',
          assistantId: newAssistant.id
        };
      }

      // If we have an assistant and we're forcing an update, update it
      if (vapiAssistant && force) {
        console.log('[AttorneyStateManager] Forcing update of Vapi assistant');

        // Update assistant
        const updatedAssistant = await updateVapiAssistant(vapiAssistantId, {
          name: this.attorney.firm_name || 'Your Law Firm',
          firstMessage: this.attorney.welcome_message || `Hello, I'm the legal assistant for ${this.attorney.firm_name}. How can I help you today?`,
          instructions: this.attorney.vapi_instructions || `You are a legal assistant for ${this.attorney.firm_name}. Help potential clients understand their legal needs and collect relevant information.`,
          voice: {
            provider: this.attorney.voice_provider || '11labs',
            voiceId: this.attorney.voice_id || 'sarah'
          }
        });

        console.log('[AttorneyStateManager] Updated Vapi assistant:', updatedAssistant.id);

        // Update last sync time
        localStorage.setItem(STORAGE_KEYS.LAST_SYNC, Date.now().toString());

        return {
          action: 'updated',
          assistantId: updatedAssistant.id
        };
      }

      console.log('[AttorneyStateManager] Vapi assistant is up to date');

      // Update last sync time
      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, Date.now().toString());

      return {
        action: 'none',
        assistantId: vapiAssistantId
      };
    } catch (error) {
      console.error('[AttorneyStateManager] Error syncing with Vapi:', error);
      this.lastError = error;
      throw error;
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Create a new attorney
   * @param {Object} attorneyData - The attorney data
   * @returns {Promise<Object>} The created attorney
   */
  async createAttorney(attorneyData) {
    console.log('[AttorneyStateManager] Creating new attorney');

    try {
      // Generate ID if not provided
      if (!attorneyData.id || !this.isValidUUID(attorneyData.id)) {
        attorneyData.id = this.generateUUID();
      }

      // Set timestamps
      const now = new Date().toISOString();
      attorneyData.created_at = now;
      attorneyData.updated_at = now;

      // Save to Supabase
      const savedAttorney = await this.saveAttorney(attorneyData);

      // Sync with Vapi
      await this.syncWithVapi({ force: true });

      return savedAttorney;
    } catch (error) {
      console.error('[AttorneyStateManager] Error creating attorney:', error);
      this.lastError = error;
      throw error;
    }
  }

  /**
   * Update an existing attorney
   * @param {Object} attorneyData - The attorney data to update
   * @returns {Promise<Object>} The updated attorney
   */
  async updateAttorney(attorneyData) {
    console.log('[AttorneyStateManager] Updating attorney');

    try {
      // Ensure we have a valid attorney
      if (!this.attorney || !this.isValidUUID(this.attorney.id)) {
        throw new Error('No valid attorney to update');
      }

      // Merge with existing attorney
      const mergedAttorney = {
        ...this.attorney,
        ...attorneyData,
        updated_at: new Date().toISOString()
      };

      // Ensure ID doesn't change
      mergedAttorney.id = this.attorney.id;

      // Save to Supabase
      const savedAttorney = await this.saveAttorney(mergedAttorney);

      // Sync with Vapi if voice or message settings changed
      const voiceOrMessageChanged = [
        'firm_name',
        'welcome_message',
        'vapi_instructions',
        'voice_provider',
        'voice_id'
      ].some(key => attorneyData[key] !== undefined);

      if (voiceOrMessageChanged) {
        await this.syncWithVapi({ force: true });
      }

      return savedAttorney;
    } catch (error) {
      console.error('[AttorneyStateManager] Error updating attorney:', error);
      this.lastError = error;
      throw error;
    }
  }

  /**
   * Subscribe to attorney state changes
   * @param {Function} callback - The callback to call when state changes
   * @returns {Function} A function to unsubscribe
   */
  subscribe(callback) {
    if (typeof callback !== 'function') {
      console.warn('[AttorneyStateManager] Cannot subscribe with non-function callback');
      return () => {};
    }

    this.subscribers.push(callback);

    // Call immediately with current state
    if (this.attorney) {
      callback(this.attorney);
    }

    // Return unsubscribe function
    return () => this.unsubscribe(callback);
  }

  /**
   * Unsubscribe from attorney state changes
   * @param {Function} callback - The callback to unsubscribe
   */
  unsubscribe(callback) {
    this.subscribers = this.subscribers.filter(cb => cb !== callback);
  }

  /**
   * Notify all subscribers of state changes
   */
  notifySubscribers() {
    if (!this.attorney) return;

    this.subscribers.forEach(callback => {
      try {
        callback(this.attorney);
      } catch (error) {
        console.error('[AttorneyStateManager] Error in subscriber callback:', error);
      }
    });
  }

  /**
   * Clean up resources
   */
  cleanup() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.subscribers = [];
  }
}

// Create singleton instance
const attorneyStateManager = new AttorneyStateManager();

// Export singleton
export default attorneyStateManager;

// Export hook for React components - removed as we're using the context-based hook instead
// This was causing issues with the React context fix
// The hook is now defined in AttorneyStateContext.jsx
