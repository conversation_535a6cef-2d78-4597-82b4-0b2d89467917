/**
 * Tests for Enhanced Sync Helpers
 */

import {
  fetchFromSupabase,
  updateSupabaseAttorney,
  getAttorneyByEmail,
  getAttorneyByAuthId,
  getAttorneyById,
  createAttorney,
  updateAttorney,
  fetchFromVapi,
  createVapiAssistant,
  updateVapiAssistant,
  getVapiAssistant,
  findProfileDiscrepancies,
  getValidVoicesForProvider,
  ensureProfilePersistence
} from '../services/EnhancedSyncHelpers';

import { enhancedVapiMcpService } from '../services/EnhancedVapiMcpService';

// Mock enhancedVapiMcpService
jest.mock('../services/EnhancedVapiMcpService', () => ({
  enhancedVapiMcpService: {
    connected: false,
    connect: jest.fn().mockResolvedValue(true),
    getAssistant: jest.fn().mockResolvedValue({
      id: 'test-assistant-id',
      name: 'Test Law Firm',
      firstMessage: 'Welcome to Test Law Firm',
      instructions: 'You are a legal assistant for Test Law Firm',
      voice: {
        provider: 'playht',
        voiceId: 'ranger'
      },
      llm: {
        provider: 'openai',
        model: 'gpt-4o'
      }
    }),
    createAssistant: jest.fn().mockResolvedValue({
      id: 'new-assistant-id',
      name: 'Test Law Firm',
      firstMessage: 'Welcome to Test Law Firm',
      instructions: 'You are a legal assistant for Test Law Firm',
      voice: {
        provider: 'playht',
        voiceId: 'ranger'
      },
      llm: {
        provider: 'openai',
        model: 'gpt-4o'
      }
    }),
    updateAssistant: jest.fn().mockResolvedValue({
      id: 'test-assistant-id',
      name: 'Updated Law Firm',
      firstMessage: 'Welcome to Updated Law Firm',
      instructions: 'You are a legal assistant for Updated Law Firm',
      voice: {
        provider: 'playht',
        voiceId: 'ranger'
      },
      llm: {
        provider: 'openai',
        model: 'gpt-4o'
      }
    })
  }
}));

// Mock supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn().mockReturnValue({
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({
      data: {
        id: 'test-attorney-id',
        name: 'Test Attorney',
        email: '<EMAIL>',
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        vapi_assistant_id: 'test-assistant-id'
      },
      error: null
    }),
    update: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis()
  })
}));

describe('EnhancedSyncHelpers', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Reset enhancedVapiMcpService state
    enhancedVapiMcpService.connected = false;
  });
  
  describe('fetchFromSupabase', () => {
    it('should fetch attorney data from Supabase', async () => {
      const result = await fetchFromSupabase('test-attorney-id');
      
      expect(result).toEqual({
        id: 'test-attorney-id',
        name: 'Test Attorney',
        email: '<EMAIL>',
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        vapi_assistant_id: 'test-assistant-id'
      });
    });
  });
  
  describe('updateSupabaseAttorney', () => {
    it('should update attorney data in Supabase', async () => {
      const result = await updateSupabaseAttorney('test-attorney-id', {
        firm_name: 'Updated Law Firm'
      });
      
      expect(result).toEqual({
        id: 'test-attorney-id',
        name: 'Test Attorney',
        email: '<EMAIL>',
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        vapi_assistant_id: 'test-assistant-id'
      });
    });
  });
  
  describe('getAttorneyByEmail', () => {
    it('should get attorney by email', async () => {
      const result = await getAttorneyByEmail('<EMAIL>');
      
      expect(result).toEqual({
        id: 'test-attorney-id',
        name: 'Test Attorney',
        email: '<EMAIL>',
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        vapi_assistant_id: 'test-assistant-id'
      });
    });
  });
  
  describe('fetchFromVapi', () => {
    it('should fetch assistant data from Vapi', async () => {
      // Mock environment variables
      global.import = { meta: { env: { VITE_VAPI_PUBLIC_KEY: 'test-api-key' } } };
      
      const result = await fetchFromVapi('test-assistant-id');
      
      expect(result).toEqual({
        id: 'test-assistant-id',
        name: 'Test Law Firm',
        firstMessage: 'Welcome to Test Law Firm',
        instructions: 'You are a legal assistant for Test Law Firm',
        voice: {
          provider: 'playht',
          voiceId: 'ranger'
        },
        llm: {
          provider: 'openai',
          model: 'gpt-4o'
        }
      });
      expect(enhancedVapiMcpService.connect).toHaveBeenCalled();
      expect(enhancedVapiMcpService.getAssistant).toHaveBeenCalledWith('test-assistant-id');
    });
  });
  
  describe('findProfileDiscrepancies', () => {
    it('should find discrepancies between Supabase and Vapi data', () => {
      const supabaseData = {
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        ai_model: 'gpt-4o'
      };
      
      const vapiData = {
        name: 'Different Law Firm',
        firstMessage: 'Welcome to Different Law Firm',
        instructions: 'You are a legal assistant for Different Law Firm',
        voice: {
          provider: '11labs',
          voiceId: 'sarah'
        },
        llm: {
          provider: 'openai',
          model: 'gpt-3.5-turbo'
        }
      };
      
      const result = findProfileDiscrepancies(supabaseData, vapiData);
      
      expect(result).toEqual({
        firm_name: {
          supabase: 'Test Law Firm',
          vapi: 'Different Law Firm'
        },
        welcome_message: {
          supabase: 'Welcome to Test Law Firm',
          vapi: 'Welcome to Different Law Firm'
        },
        vapi_instructions: {
          supabase: 'You are a legal assistant for Test Law Firm',
          vapi: 'You are a legal assistant for Different Law Firm'
        },
        voice: {
          supabase: {
            provider: 'playht',
            voiceId: 'ranger'
          },
          vapi: {
            provider: '11labs',
            voiceId: 'sarah'
          }
        },
        ai_model: {
          supabase: 'gpt-4o',
          vapi: 'gpt-3.5-turbo'
        }
      });
    });
    
    it('should return empty object if no discrepancies', () => {
      const supabaseData = {
        firm_name: 'Test Law Firm',
        welcome_message: 'Welcome to Test Law Firm',
        vapi_instructions: 'You are a legal assistant for Test Law Firm',
        voice_provider: 'playht',
        voice_id: 'ranger',
        ai_model: 'gpt-4o'
      };
      
      const vapiData = {
        name: 'Test Law Firm',
        firstMessage: 'Welcome to Test Law Firm',
        instructions: 'You are a legal assistant for Test Law Firm',
        voice: {
          provider: 'playht',
          voiceId: 'ranger'
        },
        llm: {
          provider: 'openai',
          model: 'gpt-4o'
        }
      };
      
      const result = findProfileDiscrepancies(supabaseData, vapiData);
      
      expect(result).toEqual({});
    });
  });
  
  describe('getValidVoicesForProvider', () => {
    it('should return valid voices for a provider', async () => {
      const result = await getValidVoicesForProvider('11labs');
      
      expect(result).toContain('sarah');
      expect(result).toContain('adam');
      expect(result).toContain('daniel');
    });
    
    it('should return empty array for invalid provider', async () => {
      const result = await getValidVoicesForProvider('invalid-provider');
      
      expect(result).toEqual([]);
    });
  });
});
