/**
 * Firecrawl Service
 * 
 * This service provides web search functionality using Firecrawl's API,
 * which is specifically designed for AI assistants.
 */

// Firecrawl API configuration
const FIRECRAWL_API_KEY = 'fc-dcf7841e59c643be991ff4a74807c90a';
const FIRECRAWL_API_URL = 'https://api.firecrawl.dev/search';

/**
 * Search the web using Firecrawl
 * @param {string} query - The search query
 * @param {Object} options - Search options
 * @param {number} options.numResults - Number of results to return (default: 3)
 * @param {string[]} options.domains - Optional domain filters
 * @param {boolean} options.legalFocus - Whether to focus on legal content
 * @returns {Promise<Object>} - Search results
 */
export const searchWithFirecrawl = async (query, options = {}) => {
  try {
    // Validate inputs
    if (!query) {
      throw new Error('Search query is required');
    }
    
    // Prepare request body
    const requestBody = {
      query,
      max_results: options.numResults || 3,
      extract_content: true,
      summarize: true
    };
    
    // Add domain filter if provided
    if (options.domains && Array.isArray(options.domains) && options.domains.length > 0) {
      requestBody.domain_filter = options.domains;
    }
    
    // Add legal focus if requested
    if (options.legalFocus) {
      // Enhance query with legal focus
      requestBody.query = `${query} legal information law`;
      
      // Prioritize legal domains if no specific domains are provided
      if (!requestBody.domain_filter) {
        requestBody.domain_filter = [
          'law.cornell.edu',
          'justia.com',
          'findlaw.com',
          'nolo.com',
          'uscourts.gov',
          'courtlistener.com',
          'casetext.com',
          'lexisnexis.com',
          'westlaw.com'
        ];
      }
    }
    
    // Make the API request
    console.log('Sending Firecrawl request:', requestBody);
    const response = await fetch(FIRECRAWL_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Firecrawl API error: ${errorData.error || response.statusText}`);
    }
    
    const data = await response.json();
    console.log('Firecrawl response received:', data);
    
    return {
      query,
      results: data.results || [],
      totalResults: data.results?.length || 0
    };
  } catch (error) {
    console.error('Firecrawl search error:', error);
    throw error;
  }
};

/**
 * Format Firecrawl results for display
 * @param {Object} searchResults - Raw search results from Firecrawl
 * @param {string} format - Display format (simple, detailed, etc.)
 * @returns {Object} - Formatted results
 */
export const formatFirecrawlResults = (searchResults, format = 'simple') => {
  if (!searchResults || !searchResults.results || !Array.isArray(searchResults.results)) {
    return { type: format, items: [] };
  }
  
  switch (format) {
    case 'detailed':
      return {
        type: 'detailed',
        items: searchResults.results.map(result => ({
          title: result.title,
          url: result.url,
          content: result.content_html || result.content_text || '',
          summary: result.summary || '',
          source: new URL(result.url).hostname
        }))
      };
      
    case 'legal':
      return {
        type: 'legal',
        items: searchResults.results.map(result => {
          // Extract citation if available
          const citationMatch = result.title.match(/(\d+\s+[A-Za-z\.]+\s+\d+|\d+\s+[A-Za-z\.]+\s+\d+d\s+\d+)/);
          const citation = citationMatch ? citationMatch[0] : '';
          
          return {
            title: result.title,
            url: result.url,
            content: result.content_text || '',
            summary: result.summary || '',
            citation,
            source: new URL(result.url).hostname
          };
        })
      };
      
    case 'simple':
    default:
      return {
        type: 'simple',
        items: searchResults.results.map(result => ({
          title: result.title,
          url: result.url,
          summary: result.summary || '',
          source: new URL(result.url).hostname
        }))
      };
  }
};

export default {
  searchWithFirecrawl,
  formatFirecrawlResults
};
