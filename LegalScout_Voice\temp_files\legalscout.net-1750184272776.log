callback:18 🚀 [LegalScout] Initializing environment...
callback:40 ✅ [LegalScout] Environment initialized
callback:129 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
callback:216 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-27ab20fa.js:52 🔐 [UnifiedAuth] Service available globally as window.unifiedAuthService
index-27ab20fa.js:52 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
index-27ab20fa.js:86 [VapiLoader] Starting Vapi SDK loading process
index-27ab20fa.js:86 [VapiLoader] Attempting to import @vapi-ai/web package
index-27ab20fa.js:173 [VapiMcpService] Created clean fetch from iframe
index-27ab20fa.js:173 [VapiMcpService] INFO: Vapi MCP Service initialized Object
hook.js:608 ⚠️ [Supabase-Fixed] Client not initialized, returning stub for: channel
overrideMethod @ hook.js:608
hook.js:608 ❌ [AssistantSyncManager] Error initializing real-time subscriptions: Error: Supabase client not initialized. Call getSupabaseClient() first.
    at Proxy.<anonymous> (index-27ab20fa.js:52:11549)
    at m1e.initializeRealtimeSubscriptions (index-27ab20fa.js:435:47687)
overrideMethod @ hook.js:608
hook.js:608 ⚠️ [AssistantContextValidator] No valid assistant context found: Object
overrideMethod @ hook.js:608
hook.js:608 ⚠️ [AssistantAwareContext] Assistant selected but no subdomain found - using assistant ID fallback
overrideMethod @ hook.js:608
hook.js:608 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
overrideMethod @ hook.js:608
index.ts:5 Loaded contentScript
callback#:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
index-27ab20fa.js:50 ❌ [Supabase-Fixed] OAuth callback attempt 5 failed: Error: No user session found after OAuth callback
    at m8 (index-27ab20fa.js:50:37219)
    at async Oxe.handleOAuthCallback (index-27ab20fa.js:476:52978)
    at async index-27ab20fa.js:530:485
overrideMethod @ hook.js:608
m8 @ index-27ab20fa.js:50
index-27ab20fa.js:476 ❌ [UnifiedAuth-Fixed] OAuth callback error: Error: No user session found after OAuth callback
    at Oxe.handleOAuthCallback (index-27ab20fa.js:476:53014)
    at async index-27ab20fa.js:530:485
overrideMethod @ hook.js:608
handleOAuthCallback @ index-27ab20fa.js:476
index-27ab20fa.js:530 Auth callback error: Error: No user session found after OAuth callback
    at index-27ab20fa.js:530:536
overrideMethod @ hook.js:608
(anonymous) @ index-27ab20fa.js:530
