/**
 * Vapi Webhook Integration Test Suite
 *
 * Tests the actual webhook endpoint and Vapi integration to identify
 * why call records are not being received from Vapi.
 */

import { describe, test, expect, beforeAll, afterAll, vi } from 'vitest';
import { createServer } from 'http';
import request from 'supertest';
import { supabase } from '../../lib/supabase.js';

// Mock the webhook handler
const mockWebhookHandler = async (req, res) => {
  // Import the actual handler
  const handler = await import('../../api/webhook/vapi-call/index.js');
  return handler.default(req, res);
};

// Test data
const mockCallData = {
  id: 'test-call-webhook-' + Date.now(),
  assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
  status: 'completed',
  duration: 120,
  start_time: new Date().toISOString(),
  end_time: new Date().toISOString(),
  customer: {
    phone_number: '+1234567890'
  },
  transcripts: [
    {
      role: 'assistant',
      message: 'Hello! How can I help you today?'
    },
    {
      role: 'user',
      message: 'I need help with a legal matter.'
    }
  ],
  messages: [],
  tool_executions: [],
  metadata: {
    urgency: 'Medium',
    practice_area: 'General'
  }
};

describe('Vapi Webhook Integration Tests', () => {
  let server;
  let attorneyId;

  beforeAll(async () => {
    // Get attorney ID for testing
    const { data: attorney } = await supabase
      .from('attorneys')
      .select('id')
      .eq('email', '<EMAIL>')
      .single();

    attorneyId = attorney?.id;
    expect(attorneyId).toBeDefined();

    // Create test server
    server = createServer(mockWebhookHandler);
  });

  afterAll(async () => {
    if (server) {
      server.close();
    }

    // Clean up test data
    await supabase
      .from('call_records')
      .delete()
      .like('call_id', 'test-call-webhook-%');

    await supabase
      .from('consultations')
      .delete()
      .like('metadata->call_id', 'test-call-webhook-%');
  });

  test('should verify attorney exists in database', async () => {
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    expect(error).toBeNull();
    expect(attorney).toBeDefined();
    expect(attorney.id).toBe('87756a2c-a398-43f2-889a-b8815684df71');
    expect(attorney.vapi_assistant_id).toBe('cd0b44b7-397e-410d-8835-ce9c3ba584b2');
  });

  test('should verify webhook endpoint exists and is accessible', async () => {
    const response = await request(server)
      .get('/api/webhook/vapi-call')
      .expect(405); // Should return Method Not Allowed for GET

    expect(response.body.error).toBe('Method not allowed');
  });

  test('should handle webhook POST request with valid call data', async () => {
    const response = await request(server)
      .post('/api/webhook/vapi-call')
      .send(mockCallData)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.message).toBe('Call data stored successfully');
  });

  test('should create call record in Supabase', async () => {
    // Send webhook request
    await request(server)
      .post('/api/webhook/vapi-call')
      .send(mockCallData)
      .expect(200);

    // Check if call record was created
    const { data: callRecord, error } = await supabase
      .from('call_records')
      .select('*')
      .eq('call_id', mockCallData.id)
      .single();

    expect(error).toBeNull();
    expect(callRecord).toBeDefined();
    expect(callRecord.assistant_id).toBe(mockCallData.assistant_id);
    expect(callRecord.attorney_id).toBe(attorneyId);
    expect(callRecord.status).toBe('completed');
  });

  test('should create consultation record for completed call', async () => {
    const completedCallData = {
      ...mockCallData,
      id: 'test-call-consultation-' + Date.now(),
      status: 'completed'
    };

    // Send webhook request
    await request(server)
      .post('/api/webhook/vapi-call')
      .send(completedCallData)
      .expect(200);

    // Check if consultation record was created
    const { data: consultation, error } = await supabase
      .from('consultations')
      .select('*')
      .eq('metadata->call_id', completedCallData.id)
      .single();

    expect(error).toBeNull();
    expect(consultation).toBeDefined();
    expect(consultation.attorney_id).toBe(attorneyId);
    expect(consultation.status).toBe('new');
  });

  test('should handle invalid webhook data gracefully', async () => {
    const invalidData = {
      // Missing required fields
      status: 'completed'
    };

    const response = await request(server)
      .post('/api/webhook/vapi-call')
      .send(invalidData)
      .expect(400);

    expect(response.body.error).toBe('Invalid call data');
  });

  test('should verify webhook signature if secret is configured', async () => {
    // This test would verify webhook signature validation
    // For now, we'll just check that the endpoint handles missing signatures
    const response = await request(server)
      .post('/api/webhook/vapi-call')
      .send(mockCallData)
      .expect(200);

    expect(response.body.success).toBe(true);
  });
});

describe('Vapi Webhook Integration Tests', () => {
  let server;
  let testPort = 3001;

  beforeAll(() => {
    // Create a test server for webhook testing
    server = createServer(mockWebhookHandler);
    server.listen(testPort);
  });

  afterAll(() => {
    if (server) {
      server.close();
    }
  });

  describe('1. Webhook Endpoint Accessibility', () => {
    test('should respond to POST requests', async () => {
      const response = await request(server)
        .post('/')
        .send({
          id: 'test-call-123',
          assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
          status: 'in-progress'
        });

      expect(response.status).not.toBe(404);
      expect(response.status).not.toBe(405);
    });

    test('should reject non-POST requests', async () => {
      const response = await request(server)
        .get('/');

      expect(response.status).toBe(405);
      expect(response.body.error).toBe('Method not allowed');
    });

    test('should validate required fields', async () => {
      const response = await request(server)
        .post('/')
        .send({
          // Missing required fields
          status: 'completed'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid call data');
    });
  });

  describe('2. Webhook Signature Verification', () => {
    test('should handle missing webhook secret gracefully', async () => {
      // Test without webhook secret (should warn but continue)
      const response = await request(server)
        .post('/')
        .send({
          id: 'test-call-no-secret',
          assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
          status: 'completed'
        });

      // Should not fail due to missing signature when secret is not configured
      expect(response.status).not.toBe(401);
    });
  });

  describe('3. Call Data Processing', () => {
    test('should process valid call data', async () => {
      const callData = {
        id: 'test-call-valid-' + Date.now(),
        assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
        status: 'completed',
        duration: 120,
        start_time: new Date().toISOString(),
        end_time: new Date().toISOString(),
        customer: {
          phone_number: '+1234567890'
        },
        transcripts: [
          {
            role: 'user',
            message: 'I need legal help with a contract'
          },
          {
            role: 'assistant', 
            message: 'I can help you with contract law'
          }
        ],
        messages: [],
        tool_executions: [
          {
            tool_name: 'collect_client_info',
            result: {
              name: 'Jane Smith',
              email: '<EMAIL>',
              practice_area: 'Contract Law',
              location: 'California'
            }
          }
        ],
        metadata: {
          source: 'web_call'
        }
      };

      const response = await request(server)
        .post('/')
        .send(callData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Call data stored successfully');
    });

    test('should handle unknown assistant_id', async () => {
      const callData = {
        id: 'test-call-unknown-assistant',
        assistant_id: 'unknown-assistant-id-12345',
        status: 'completed'
      };

      const response = await request(server)
        .post('/')
        .send(callData);

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Attorney not found');
    });
  });

  describe('4. Real Vapi Configuration Check', () => {
    test('should verify Vapi webhook URL configuration', async () => {
      // This test checks if the webhook URL is properly configured in Vapi
      // We'll simulate what Vapi should be sending
      
      const expectedWebhookUrl = process.env.VERCEL_URL 
        ? `https://${process.env.VERCEL_URL}/api/webhook/vapi-call`
        : 'http://localhost:5175/api/webhook/vapi-call';

      console.log('Expected webhook URL:', expectedWebhookUrl);
      
      // Test that our webhook endpoint structure matches what Vapi expects
      expect(expectedWebhookUrl).toMatch(/\/api\/webhook\/vapi-call$/);
    });

    test('should handle Vapi call status events', async () => {
      const statusEvents = [
        'queued',
        'ringing', 
        'in-progress',
        'forwarding',
        'ended',
        'completed'
      ];

      for (const status of statusEvents) {
        const callData = {
          id: `test-call-${status}-${Date.now()}`,
          assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
          status: status,
          start_time: new Date().toISOString()
        };

        if (status === 'completed' || status === 'ended') {
          callData.end_time = new Date().toISOString();
          callData.duration = 60;
        }

        const response = await request(server)
          .post('/')
          .send(callData);

        // All status events should be processed successfully
        expect(response.status).toBe(200);
        console.log(`✅ Status '${status}' processed successfully`);
      }
    });
  });

  describe('5. Data Flow Verification', () => {
    test('should create both call_record and consultation for completed calls', async () => {
      const callData = {
        id: 'test-call-full-flow-' + Date.now(),
        assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
        status: 'completed',
        duration: 300,
        start_time: new Date(Date.now() - 300000).toISOString(),
        end_time: new Date().toISOString(),
        customer: {
          phone_number: '+1555123456'
        },
        transcripts: [
          {
            role: 'user',
            message: 'I need help with a personal injury case'
          }
        ],
        tool_executions: [
          {
            tool_name: 'collect_client_info',
            result: {
              name: 'Bob Johnson',
              email: '<EMAIL>',
              practice_area: 'Personal Injury',
              location: 'Texas',
              issue: 'Car accident claim'
            }
          }
        ]
      };

      const response = await request(server)
        .post('/')
        .send(callData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Verify both records were created
      // Note: In a real test, we'd check the database here
      console.log('✅ Full data flow test completed');
    });
  });

  describe('6. Error Handling', () => {
    test('should handle malformed JSON gracefully', async () => {
      const response = await request(server)
        .post('/')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}');

      expect(response.status).toBe(400);
    });

    test('should handle database connection errors', async () => {
      // Mock a database error scenario
      const callData = {
        id: 'test-call-db-error',
        assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
        status: 'completed'
      };

      // This test verifies error handling exists
      const response = await request(server)
        .post('/')
        .send(callData);

      // Should not crash the server
      expect(response.status).toBeDefined();
    });
  });
});
